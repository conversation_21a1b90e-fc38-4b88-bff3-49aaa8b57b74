import { logger } from '../../utils/logger.js';

export type AutomationMode = 'scheduler' | 'n8n';

export interface AutomationConfig {
  mode: AutomationMode;
  schedulerEnabled: boolean;
  n8nWebhookUrl?: string;
  n8nApiKey?: string;
}

export class AutomationConfigService {
  private config: AutomationConfig;

  constructor() {
    this.config = this.loadConfigFromEnv();
    logger.info('Automation config loaded', this.config);
  }

  private loadConfigFromEnv(): AutomationConfig {
    const mode = (process.env.AUTOMATION_MODE as AutomationMode) || 'scheduler';
    
    return {
      mode,
      schedulerEnabled: mode === 'scheduler' && process.env.SCHEDULER_ENABLED === 'true',
      n8nWebhookUrl: process.env.N8N_WEBHOOK_URL,
      n8nApiKey: process.env.N8N_API_KEY
    };
  }

  getConfig(): AutomationConfig {
    return { ...this.config };
  }

  getMode(): AutomationMode {
    return this.config.mode;
  }

  isSchedulerMode(): boolean {
    return this.config.mode === 'scheduler';
  }

  isN8nMode(): boolean {
    return this.config.mode === 'n8n';
  }

  isSchedulerEnabled(): boolean {
    return this.config.schedulerEnabled;
  }

  updateConfig(newConfig: Partial<AutomationConfig>): void {
    this.config = { ...this.config, ...newConfig };
    logger.info('Automation config updated', this.config);
  }

  validateN8nConfig(): { valid: boolean; message?: string } {
    if (this.config.mode !== 'n8n') {
      return { valid: true };
    }

    if (!this.config.n8nWebhookUrl) {
      return { 
        valid: false, 
        message: 'N8N_WEBHOOK_URL is required when using n8n mode' 
      };
    }

    return { valid: true };
  }

  getN8nConfig(): { webhookUrl?: string; apiKey?: string } {
    if (this.config.mode !== 'n8n') {
      return {};
    }

    return {
      webhookUrl: this.config.n8nWebhookUrl,
      apiKey: this.config.n8nApiKey
    };
  }
}

// 单例实例
export const automationConfigService = new AutomationConfigService(); 