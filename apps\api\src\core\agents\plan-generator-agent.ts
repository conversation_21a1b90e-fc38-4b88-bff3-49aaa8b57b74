import { z } from 'zod';
import { BaseAgent, type AgentContext } from './base-agent.js';
import { planRepository, taskRepository, configRepository } from '../database/repositories.js';

// 计划生成输入schema
export const PlanGeneratorInputSchema = z.object({
  date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
  maxTasks: z.number().min(1).max(50).default(10),
  categories: z.array(z.enum(['industry', 'research', 'events', 'frontier', 'insight', 'misc'])).default(['industry', 'research', 'frontier']),
  customPrompt: z.string().optional(),
  forceRegenerate: z.boolean().default(false)
});

export type PlanGeneratorInput = z.infer<typeof PlanGeneratorInputSchema>;

/**
 * 计划生成Agent
 * 负责生成每日内容计划和任务
 */
export class PlanGeneratorAgent extends BaseAgent {
  private openaiApiKey?: string;
  private readonly categories = ['industry', 'research', 'events', 'frontier', 'insight', 'misc'];
  private readonly keywords = [
    'hypersonic', 'satellite', 'aerospace', 'defense', 'military',
    'space exploration', 'commercial aviation', 'drone technology',
    'artificial intelligence', 'cybersecurity', 'radar systems',
    'stealth technology', 'missile defense', 'propulsion',
    'spacecraft', 'space station', 'mars mission', 'lunar base'
  ];

  constructor() {
    super({
      name: 'PlanGeneratorAgent',
      description: 'Generates daily content plans and tasks for aerospace and defense news',
      version: '2.0.0',
      maxRetries: 3,
      timeout: 120000 // 2 minutes
    });
  }

  protected async onInitialize(): Promise<void> {
    // 获取OpenAI API Key
    this.openaiApiKey = await configRepository.get('openai_api_key') || process.env.OPENAI_API_KEY;
    
    if (!this.openaiApiKey) {
      await this.log('warn', 'OpenAI API key not configured - will use fallback topic generation', {});
    } else if (!this.openaiApiKey.startsWith('sk-')) {
      await this.log('warn', 'Invalid OpenAI API key format detected', {});
      this.openaiApiKey = undefined; // 重置为undefined，使用fallback
    }
  }

  protected async validateInput(input: any): Promise<any> {
    return PlanGeneratorInputSchema.parse(input);
  }

  protected async onExecute(input: any, context: AgentContext): Promise<any> {
    const validatedInput = input as PlanGeneratorInput;
    // 检查是否已存在该日期的计划
    const existingPlan = await planRepository.getByDate(validatedInput.date);
    
    if (existingPlan && !validatedInput.forceRegenerate) {
      await this.log('info', `Plan for ${validatedInput.date} already exists`, { planId: existingPlan.id });
      return {
        plan: existingPlan,
        tasks: await taskRepository.getByPlanId(existingPlan.id),
        isNewPlan: false
      };
    }

    // 分析趋势和生成主题
    const [trendAnalysis, generatedTopics] = await Promise.all([
      this.analyzeTrends(),
      this.generateTopics(validatedInput)
    ]);

    // 创建新计划
    const newPlan = await planRepository.create({
      date: validatedInput.date,
      status: 'draft',
      maxTasks: validatedInput.maxTasks,
      categories: validatedInput.categories,
      customPrompt: validatedInput.customPrompt
    });

    // 创建任务
    const tasks: any[] = [];
    for (const [index, topic] of generatedTopics.entries()) {
      const task = await taskRepository.create({
        planId: newPlan.id,
        title: topic.title,
        description: topic.description,
        keyword: topic.keyword || this.extractKeyword(topic.title),
        sourceUrl: topic.sourceUrl,
        targetCategory: topic.category,
        priority: this.calculatePriority(topic, trendAnalysis),
        relatedProducts: topic.relatedProducts || [],
        estimatedDuration: this.estimateWordCount(topic.category)
      });
      tasks.push(task);
    }

    // 更新计划状态为active
    await planRepository.update(newPlan.id, { status: 'active' });

    return {
      plan: newPlan,
      tasks,
      trendAnalysis,
      isNewPlan: true,
      stats: {
        totalTasks: tasks.length,
        highPriorityTasks: tasks.filter(t => t.priority >= 4).length,
        categoriesDistribution: this.calculateCategoryDistribution(tasks)
      }
    };
  }

  protected async onHealthCheck(): Promise<Record<string, any>> {
    const hasApiKey = !!this.openaiApiKey;
    const canAccessDatabase = await this.testDatabaseAccess();

    return {
      openaiConfigured: hasApiKey,
      databaseAccess: canAccessDatabase,
      categoriesCount: this.categories.length,
      keywordsCount: this.keywords.length
    };
  }

  /**
   * 分析当前趋势
   */
  private async analyzeTrends(): Promise<any> {
    // 简化的趋势分析，实际可以集成外部API
    const trends = await configRepository.get('cached_trends') || this.getDefaultTrends();
    
    return {
      trending_keywords: trends.keywords || [],
      seasonal_focus: this.getSeasonalFocus(),
      market_sentiment: trends.sentiment || 'neutral',
      update_timestamp: new Date().toISOString()
    };
  }

  /**
   * 生成主题列表
   */
  private async generateTopics(input: PlanGeneratorInput): Promise<any[]> {
    if (!this.openaiApiKey) {
      return this.generateFallbackTopics(input);
    }

    try {
      const prompt = this.buildPrompt(input);
      const response = await this.callOpenAI(prompt);
      
      // 解析OpenAI响应
      const topics = this.parseOpenAIResponse(response);
      
      // 验证和补充topics
      return topics.map(topic => ({
        ...topic,
        keyword: topic.keyword || this.extractKeyword(topic.title),
        relatedProducts: topic.relatedProducts || this.suggestRelatedProducts(topic)
      }));

    } catch (error) {
      await this.log('warn', 'OpenAI generation failed, using fallback', { error });
      return this.generateFallbackTopics(input);
    }
  }

  /**
   * 构建OpenAI提示
   */
  private buildPrompt(input: PlanGeneratorInput): string {
    const basePrompt = `Generate ${input.maxTasks} newsworthy article topics for an aerospace and defense news website.

Target date: ${input.date}
Categories to include: ${input.categories.join(', ')}

${input.customPrompt ? `Additional requirements: ${input.customPrompt}` : ''}

Current industry trends to consider:
- Hypersonic flight technology developments
- Commercial space industry growth
- AI integration in defense systems
- Satellite constellation expansion
- Next-generation fighter aircraft
- Space debris management
- Lunar exploration programs
- Cybersecurity in aerospace

For each topic, provide:
1. A compelling, SEO-friendly headline
2. Category (from the specified list)
3. Brief description (1-2 sentences)
4. Primary keyword for SEO
5. Potential source URL (if available)
6. Target deadline (within next 7 days)

Format as JSON array with objects containing: title, category, description, keyword, sourceUrl, deadline`;

    return basePrompt;
  }

  /**
   * 调用OpenAI API
   */
  private async callOpenAI(prompt: string): Promise<string> {
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.openaiApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o',
        messages: [
          {
            role: 'system',
            content: 'You are an expert aerospace and defense industry journalist. Generate timely, relevant, and technically accurate article topics.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 2000
      })
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    return result.choices[0]?.message?.content || '';
  }

  /**
   * 解析OpenAI响应
   */
  private parseOpenAIResponse(content: string): any[] {
    try {
      // 尝试提取JSON
      const jsonMatch = content.match(/\[[\s\S]*\]/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
      throw new Error('No valid JSON found in response');
    } catch (error) {
      console.warn('Failed to parse OpenAI response', { content, error });
      throw error;
    }
  }

  /**
   * 生成后备主题（当OpenAI不可用时）
   */
  private generateFallbackTopics(input: PlanGeneratorInput): any[] {
    const today = new Date(input.date);
    const getDeadline = (days: number) => {
      const deadline = new Date(today);
      deadline.setDate(today.getDate() + days);
      return deadline.toISOString().split('T')[0];
    };

    const templates = [
      {
        title: "Next-Generation Stealth Technology Developments in Fighter Aircraft",
        category: "industry",
        description: "Exploring the latest advancements in stealth technology and their impact on modern fighter aircraft design.",
        keyword: "stealth technology",
        deadline: getDeadline(3)
      },
      {
        title: "Commercial Space Station Projects Accelerate Development Timeline",
        category: "research", 
        description: "Private companies race to develop commercial space stations as the ISS approaches retirement.",
        keyword: "commercial space station",
        deadline: getDeadline(5)
      },
      {
        title: "AI-Powered Autonomous Defense Systems Undergo Field Testing",
        category: "frontier",
        description: "Military organizations test new AI systems for autonomous threat detection and response.",
        keyword: "autonomous defense systems",
        deadline: getDeadline(4)
      },
      {
        title: "Satellite Constellation Networks Transform Global Communications",
        category: "industry",
        description: "Low Earth Orbit satellite networks revolutionize internet connectivity and emergency communications.",
        keyword: "satellite constellation",
        deadline: getDeadline(6)
      },
      {
        title: "Hypersonic Missile Defense Systems Enter Development Phase",
        category: "research",
        description: "New defense technologies aim to counter the growing hypersonic weapon threat.",
        keyword: "hypersonic defense",
        deadline: getDeadline(7)
      }
    ];

    return templates
      .filter(t => input.categories.includes(t.category as any))
      .slice(0, input.maxTasks)
      .map(template => ({
        ...template,
        relatedProducts: this.suggestRelatedProducts(template)
      }));
  }

  // 工具方法
  private calculatePriority(topic: any, trends: any): number {
    const categoryWeights = {
      'industry': 3,
      'frontier': 4,
      'research': 3,
      'events': 2,
      'insight': 2,
      'misc': 1
    };

    let priority = categoryWeights[topic.category as keyof typeof categoryWeights] || 2;
    
    // 根据趋势关键词提升优先级
    if (trends.trending_keywords) {
      for (const trend of trends.trending_keywords) {
        if (topic.title.toLowerCase().includes(trend.keyword?.toLowerCase()) ||
            topic.description.toLowerCase().includes(trend.keyword?.toLowerCase())) {
          priority += trend.relevance === 'high' ? 2 : 1;
        }
      }
    }

    return Math.min(priority, 5);
  }

  private estimateWordCount(category: string): number {
    const wordCounts: Record<string, number> = {
      'industry': 800,
      'research': 1000,
      'events': 600,
      'frontier': 900,
      'insight': 700,
      'misc': 500
    };

    return wordCounts[category] || 700;
  }

  private extractKeyword(title: string): string {
    const words = title.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 3);

    // 优先返回与预设关键词匹配的词
    for (const word of words) {
      if (this.keywords.some(kw => kw.toLowerCase().includes(word))) {
        return word;
      }
    }

    return words[0] || 'aerospace';
  }

  private suggestRelatedProducts(topic: any): string[] {
    const productMappings: Record<string, string[]> = {
      'hypersonic': ['hypersonic-systems'],
      'satellite': ['satellite-communication'],
      'radar': ['radar-systems'],
      'stealth': ['radar-systems'],
      'defense': ['hypersonic-systems', 'radar-systems'],
      'space': ['satellite-communication'],
      'missile': ['hypersonic-systems', 'radar-systems']
    };

    const products = new Set<string>();
    const text = (topic.title + ' ' + topic.description).toLowerCase();
    
    Object.entries(productMappings).forEach(([keyword, prods]) => {
      if (text.includes(keyword)) {
        prods.forEach(prod => products.add(prod));
      }
    });

    return Array.from(products);
  }

  private getDefaultTrends(): any {
    return {
      keywords: [
        { keyword: "hypersonic", growth: "+45%", relevance: "high" },
        { keyword: "space economy", growth: "+32%", relevance: "high" },
        { keyword: "drone warfare", growth: "+28%", relevance: "medium" },
        { keyword: "quantum radar", growth: "+67%", relevance: "medium" }
      ],
      sentiment: 'positive'
    };
  }

  private getSeasonalFocus(): string[] {
    const month = new Date().getMonth();
    const seasonalFocus: Record<number, string[]> = {
      0: ['year-end reviews', 'budget announcements'],
      3: ['Q1 reports', 'spring conferences'],
      6: ['mid-year analysis', 'summer developments'],
      9: ['Q3 results', 'year-end planning']
    };

    const quarter = Math.floor(month / 3) * 3;
    return seasonalFocus[quarter] || ['general developments'];
  }

  private calculateCategoryDistribution(tasks: any[]): Record<string, number> {
    const distribution: Record<string, number> = {};
    
    for (const task of tasks) {
      distribution[task.targetCategory] = (distribution[task.targetCategory] || 0) + 1;
    }

    return distribution;
  }

  private async testDatabaseAccess(): Promise<boolean> {
    try {
      await planRepository.getAll();
      return true;
    } catch (error) {
      return false;
    }
  }
} 