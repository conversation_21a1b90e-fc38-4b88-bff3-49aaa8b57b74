import { ImageService, createImageService } from './image-service.js';
import { ImageGenerationRequest, ImageGenerationResult } from './base-image-provider.js';

export interface SafeImageOptions {
  prompt: string;
  category?: string;
  title?: string;
  keyword?: string;
  style?: string;
  timeout?: number;
  maxRetries?: number;
}

export interface SafeImageResult {
  success: boolean;
  url?: string;
  provider?: string;
  error?: string;
  fallbackUsed?: boolean;
  responseTime?: number;
  healthCheckResults?: Record<string, boolean>;
}

export class SafeImageTool {
  private imageService: ImageService;
  private fallbackImage: string;
  private logger?: (level: string, message: string, data?: any) => void;

  constructor(
    fallbackImage: string = 'https://via.placeholder.com/800x400/2563eb/ffffff?text=Image+Generation+Failed',
    logger?: (level: string, message: string, data?: any) => void
  ) {
    this.imageService = createImageService(logger);
    this.fallbackImage = fallbackImage;
    this.logger = logger;
  }

  /**
   * 安全生成图片 - 带有链接有效性检查和多级降级
   */
  async generateSafeImage(options: SafeImageOptions): Promise<SafeImageResult> {
    const startTime = Date.now();
    const { prompt, category, title, keyword, style, timeout = 30000, maxRetries = 1 } = options;

    this.log('info', 'Starting safe image generation', { prompt: prompt.substring(0, 100) });

    try {
      // 构建图片生成请求
      const request: ImageGenerationRequest = {
        prompt,
        category,
        title,
        keyword,
        style
      };

      // 尝试生成图片
      for (let attempt = 0; attempt <= maxRetries; attempt++) {
        if (attempt > 0) {
          this.log('info', `Retry attempt ${attempt}/${maxRetries}`);
        }

        const result = await this.imageService.generateImage(request);

        if (result.success && result.url) {
          // 对于AI生成的图片，采用渐进式等待策略
          const waitTime = this.getProviderWaitTime(result.provider);
          if (waitTime > 0) {
            this.log('info', `Waiting for ${result.provider} image to process...`, {
              url: result.url.substring(0, 80) + '...',
              waitTime: `${waitTime/1000}s`
            });
            await new Promise(resolve => setTimeout(resolve, waitTime));
          }
          
          // 验证图片链接有效性，使用提供商特定的重试策略
          const isValid = await this.validateImageUrlWithRetries(result.url, result.provider);
          
          if (isValid) {
            const responseTime = Date.now() - startTime;
            this.log('info', 'Safe image generation successful', {
              url: result.url,
              provider: result.provider,
              responseTime
            });

            return {
              success: true,
              url: result.url,
              provider: result.provider,
              responseTime
            };
          } else {
            this.log('warn', 'Generated image URL is not accessible after retries', {
              url: result.url,
              provider: result.provider,
              attempt: attempt + 1,
              maxRetries: maxRetries + 1
            });
          }
        } else {
          this.log('warn', 'Image generation failed', {
            error: result.error,
            provider: result.provider
          });
        }
      }

      // 所有尝试都失败，返回降级图片
      this.log('warn', 'All image generation attempts failed, using fallback');
      
      // 验证降级图片是否可用
      const fallbackValid = await this.isImageUrlAlive(this.fallbackImage);
      
      if (fallbackValid) {
        return {
          success: true,
          url: this.fallbackImage,
          provider: 'fallback',
          fallbackUsed: true,
          responseTime: Date.now() - startTime
        };
      } else {
        // 连降级图片都不可用，返回错误
        return {
          success: false,
          error: 'All image generation and fallback options failed',
          responseTime: Date.now() - startTime
        };
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.log('error', 'Safe image generation error', { error: errorMessage });

      return {
        success: false,
        error: errorMessage,
        responseTime: Date.now() - startTime
      };
    }
  }

  /**
   * 获取不同提供商的等待时间
   */
  private getProviderWaitTime(provider?: string): number {
    switch (provider) {
      case 'workers':
        return 300000; // Workers需要5分钟处理时间（充足时间）
      case 'fal':
        return 60000; // FAL需要1分钟处理时间
      case 'unsplash':
        return 0; // Unsplash图片立即可用
      default:
        return 60000; // 默认等待1分钟
    }
  }

  /**
   * 带重试的图片URL验证（适用于AI生成图片）
   */
  async validateImageUrlWithRetries(url: string, provider?: string, maxRetries: number = 4): Promise<boolean> {
    // 根据提供商调整超时时间和重试策略
    const timeouts = this.getProviderTimeouts(provider);
    const actualRetries = Math.min(maxRetries, timeouts.length);
    
    for (let i = 0; i < actualRetries; i++) {
      const timeout = timeouts[i];
      this.log('info', `Validating image URL (attempt ${i + 1}/${actualRetries})`, { 
        url: url.substring(0, 80) + '...', 
        timeout,
        provider 
      });
      
      const isValid = await this.isImageUrlAlive(url, timeout);
      if (isValid) {
        this.log('info', `Image URL validation successful on attempt ${i + 1}`, { 
          provider,
          totalAttempts: i + 1 
        });
        return true;
      }
      
      // 在重试之间等待，时间随着重试次数递增
      if (i < actualRetries - 1) {
        const waitTime = this.getRetryWaitTime(i, provider);
        this.log('info', `Image URL not ready, waiting ${waitTime/1000}s before retry...`, {
          provider,
          retryNumber: i + 1
        });
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
    }
    
    this.log('warn', `Image URL validation failed after ${actualRetries} attempts`, { 
      provider,
      url: url.substring(0, 80) + '...'
    });
    return false;
  }

  /**
   * 获取提供商特定的超时时间数组
   */
  private getProviderTimeouts(provider?: string): number[] {
    switch (provider) {
      case 'workers':
        return [15000, 20000, 25000, 30000, 35000]; // 增加超时时间：15s, 20s, 25s, 30s, 35s
      case 'fal':
        return [18000, 25000, 30000, 35000, 40000]; // FAL更长时间：18s, 25s, 30s, 35s, 40s
      case 'unsplash':
        return [4000, 6000, 8000]; // Unsplash较快
      default:
        return [10000, 15000, 20000, 25000]; // 默认策略也增加
    }
  }

  /**
   * 获取重试等待时间
   */
  private getRetryWaitTime(retryIndex: number, provider?: string): number {
    const baseWaitTime = provider === 'workers' || provider === 'fal' ? 5000 : 3000;
    return baseWaitTime + (retryIndex * 2000); // 递增等待时间，间隔更长
  }

  /**
   * 检查图片URL是否可访问
   */
  async isImageUrlAlive(url: string, timeout: number = 8000): Promise<boolean> {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      const response = await fetch(url, {
        method: 'HEAD',
        signal: controller.signal,
        headers: {
          'User-Agent': 'SafeImageTool/1.0'
        }
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        return false;
      }

      const contentType = response.headers.get('content-type');
      return contentType ? contentType.startsWith('image/') : false;

    } catch (error) {
      this.log('debug', 'Image URL check failed', { url, error: error instanceof Error ? error.message : 'Unknown' });
      return false;
    }
  }

  /**
   * 批量检查图片URL有效性
   */
  async validateImageUrls(urls: string[]): Promise<Record<string, boolean>> {
    const results: Record<string, boolean> = {};
    
    await Promise.all(
      urls.map(async (url) => {
        results[url] = await this.isImageUrlAlive(url);
      })
    );

    return results;
  }

  /**
   * 获取服务健康状态
   */
  async getHealthStatus(): Promise<Record<string, any>> {
    return await this.imageService.healthCheck();
  }

  /**
   * 设置新的降级图片
   */
  setFallbackImage(url: string): void {
    this.fallbackImage = url;
  }

  private log(level: string, message: string, data?: any): void {
    if (this.logger) {
      this.logger(level, `[SafeImageTool] ${message}`, data);
    }
  }
}

// 导出单例实例
export const safeImageTool = new SafeImageTool();

// 导出便捷函数
export async function safeImage(prompt: string, options: Partial<SafeImageOptions> = {}): Promise<string> {
  const result = await safeImageTool.generateSafeImage({
    prompt,
    ...options
  });

  if (result.success && result.url) {
    return result.url;
  }

  throw new Error(result.error || 'Image generation failed');
} 