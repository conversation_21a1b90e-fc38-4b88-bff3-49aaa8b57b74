---
import { getCollection, render } from 'astro:content';
import ArticleLayout from '../../layouts/ArticleLayout.astro';

export async function getStaticPaths() {
  const allPosts = await getCollection('news', ({ data }) => {
    return data.draft !== true;
  });
  
  return allPosts.map(post => ({
    params: { slug: post.id },
    props: { post }
  }));
}

const { post } = Astro.props;
const { Content } = await render(post);
---

<ArticleLayout post={post}>
  <Content />
</ArticleLayout> 