# 环境配置说明

## 环境变量配置

请在项目根目录创建 `.env` 文件，包含以下变量：

```bash
# === API 服务配置 ===
NODE_ENV=development
PORT=3002
HOST=0.0.0.0
LOG_LEVEL=info

# === OpenAI API ===
OPENAI_API_KEY=your_openai_api_key_here

# === 搜索和爬虫服务 ===
SERP_API_KEY=your_serpapi_key_here
FIRECRAWL_API_KEY=your_firecrawl_api_key_here
NEWSAPI_API_KEY=your_newsapi_key_here

# === 图片服务 ===
UNSPLASH_API_KEY=your_unsplash_api_key_here

# === GitHub API (用于自动提交) ===
GITHUB_TOKEN=your_github_token_here
GITHUB_OWNER=your_github_username
GITHUB_REPO=your_repo_name

# === 数据库配置 ===
DATABASE_URL=./data/database.sqlite

# === 开发配置 ===
API_KEY=dev-api-key-12345
RATE_LIMIT_WINDOW=60000
RATE_LIMIT_MAX=100

# === 内容生成配置 ===
MAX_WORD_COUNT=3000
MIN_WORD_COUNT=800
DEFAULT_CATEGORY=industry
```

## 快速开始

1. **安装依赖**
   ```bash
   pnpm install
   ```

2. **构建共享包**
   ```bash
   pnpm build:shared
   ```

3. **启动开发环境**
   ```bash
   pnpm dev
   ```

4. **验证服务**
   - API服务: http://localhost:3002/health
   - API文档: http://localhost:3002/docs
   - Web服务: http://localhost:4321

## 目录说明

```
├── apps/
│   ├── api/          # Fastify API服务
│   └── web/          # Astro前端 (现有src目录)
├── packages/
│   └── shared/       # 共享类型和工具
├── scripts/
│   └── dev.ts        # 开发环境启动脚本
└── data/             # SQLite数据库和缓存
```

## 开发命令

| 命令 | 说明 |
|------|------|
| `pnpm dev` | 启动所有开发服务 |
| `pnpm dev:api` | 仅启动API服务 |
| `pnpm dev:web` | 仅启动Web服务 |
| `pnpm build` | 构建所有包 |
| `pnpm lint` | 代码检查 |
| `pnpm type-check` | 类型检查 |

## 故障排除

### 常见问题

1. **端口占用**
   - API服务默认端口3002
   - Web服务默认端口4321
   - 如需修改，请在环境变量中设置

2. **依赖安装失败**
   ```bash
   # 清理并重新安装
   rm -rf node_modules
   rm pnpm-lock.yaml
   pnpm install
   ```

3. **类型错误**
   ```bash
   # 重新构建共享包
   pnpm build:shared
   ``` 