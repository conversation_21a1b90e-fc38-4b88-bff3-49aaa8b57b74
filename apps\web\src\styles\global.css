@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Import professional fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@400;500;600;700&display=swap');

/* CSS Custom Properties for consistent theming */
:root {
  --color-primary-50: #eff6ff;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-900: #1e3a8a;
  
  --color-secondary-50: #f8fafc;
  --color-secondary-500: #64748b;
  --color-secondary-700: #334155;
  --color-secondary-900: #0f172a;
  
  --color-accent-500: #f59e0b;
  --color-accent-600: #d97706;
  
  --shadow-soft: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
  --shadow-medium: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  --border-radius-base: 0.5rem;
  --border-radius-lg: 0.75rem;
  --border-radius-xl: 1rem;
}

/* Base styles */
html {
  scroll-behavior: smooth;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Custom component utilities */
@layer components {
  /* Button variants */
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 focus:ring-primary-500 text-white font-semibold py-3 px-6 rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200 shadow-soft hover:shadow-medium;
  }
  
  .btn-secondary {
    @apply bg-secondary-100 hover:bg-secondary-200 text-secondary-900 font-semibold py-3 px-6 rounded-lg focus:outline-none focus:ring-2 focus:ring-secondary-500 focus:ring-offset-2 transition-all duration-200;
  }
  
  .btn-outline {
    @apply border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white font-semibold py-3 px-6 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-all duration-200;
  }
  
  .btn-ghost {
    @apply text-secondary-700 hover:text-primary-600 hover:bg-secondary-50 font-medium py-2 px-4 rounded-lg transition-all duration-200;
  }

  /* Card variants */
  .card {
    @apply bg-white rounded-xl shadow-soft hover:shadow-medium transition-all duration-300 border border-secondary-100;
  }
  
  .card-elevated {
    @apply bg-white rounded-xl shadow-medium hover:shadow-strong transition-all duration-300 border border-secondary-100;
  }
  
  .card-interactive {
    @apply bg-white rounded-xl shadow-soft hover:shadow-medium hover:-translate-y-1 transition-all duration-300 border border-secondary-100 cursor-pointer;
  }

  /* Badge variants */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-primary {
    @apply bg-primary-100 text-primary-800;
  }
  
  .badge-secondary {
    @apply bg-secondary-100 text-secondary-800;
  }
  
  .badge-accent {
    @apply bg-accent-100 text-accent-800;
  }
  
  .badge-success {
    @apply bg-success-50 text-success-700;
  }

  /* Text utilities */
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-accent-500 bg-clip-text text-transparent;
  }
  
  .heading-xl {
    @apply text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight;
  }
  
  .heading-lg {
    @apply text-3xl md:text-4xl font-bold tracking-tight;
  }
  
  .heading-md {
    @apply text-2xl md:text-3xl font-bold tracking-tight;
  }
  
  .body-lg {
    @apply text-lg md:text-xl leading-relaxed;
  }

  /* Layout utilities */
  .container-page {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  .container-narrow {
    @apply max-w-4xl mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  .section-spacing {
    @apply py-12 md:py-16 lg:py-20;
  }

  /* Animation utilities */
  .animate-fade-in-up {
    @apply animate-slide-up;
  }

  /* Focus styles */
  .focus-outline {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  /* Responsive image container */
  .aspect-ratio-16-9 {
    @apply relative;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
  }
  
  .aspect-ratio-16-9 img,
  .aspect-ratio-16-9 video {
    @apply absolute inset-0 w-full h-full object-cover;
  }

  /* Grid layouts */
  .grid-cards {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8;
  }
  
  .grid-features {
    @apply grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8;
  }

  /* Navigation styles */
  .nav-link {
    @apply text-secondary-700 hover:text-primary-600 font-medium transition-colors duration-200;
  }
  
  .nav-link-active {
    @apply text-primary-600 font-semibold;
  }

  /* Article content styles */
  .prose-custom {
    @apply prose prose-lg prose-secondary max-w-none;
  }
  
  .prose-custom h1 {
    @apply text-secondary-900;
  }
  
  .prose-custom h2 {
    @apply text-secondary-900;
  }
  
  .prose-custom h3 {
    @apply text-secondary-900;
  }
  
  .prose-custom a {
    @apply text-primary-600 no-underline hover:underline font-medium;
  }

  /* Loading states */
  .skeleton {
    @apply animate-pulse bg-secondary-200 rounded;
  }
  
  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-secondary-300 border-t-primary-600;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Selection styles */
::selection {
  background-color: rgba(59, 130, 246, 0.2);
  color: inherit;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-break-inside-avoid {
    break-inside: avoid;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .card {
    @apply border-2 border-secondary-400;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
} 