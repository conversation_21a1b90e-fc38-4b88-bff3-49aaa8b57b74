# API Contract Documentation

本文档定义了自动化内容生成系统的微服务接口规范。

## 概述

系统包含三个核心微服务：
- **Discovery Service** (`/discover`) - 关键词发现和站点质量评估
- **Crawl Service** (`/crawl`) - 内容抓取和预处理
- **Write Service** (`/write`) - AI内容生成和文章创建

## 1. Discovery Service

### 1.1 发现趋势关键词

**Endpoint:** `POST /discover/trends`

**描述:** 基于SerpAPI获取行业趋势关键词

**请求体:**
```json
{
  "topics": ["aerospace technology", "defense systems"],
  "maxKeywords": 10,
  "industryFilter": true,
  "trendPeriod": "7d"
}
```

**响应:**
```json
{
  "success": true,
  "data": {
    "keywords": [
      {
        "keyword": "hypersonic missile technology",
        "trendScore": 85,
        "searchVolume": 1200,
        "competition": "medium",
        "relatedQueries": ["hypersonic weapons", "missile defense"],
        "source": "trends"
      }
    ],
    "apiCallsUsed": 3,
    "quotaRemaining": 97
  },
  "timestamp": "2024-12-19T10:30:00Z"
}
```

### 1.2 评估站点质量

**Endpoint:** `POST /discover/evaluate-site`

**描述:** 评估目标站点的内容质量和相关性

**请求体:**
```json
{
  "url": "https://example.com/article",
  "keyword": "aerospace technology",
  "includeContent": true
}
```

**响应:**
```json
{
  "success": true,
  "data": {
    "url": "https://example.com/article",
    "qualityScore": 78,
    "factors": {
      "contentLength": 1500,
      "hasTitle": true,
      "hasStructure": true,
      "contentQuality": 0.8,
      "isAccessible": true
    },
    "recommendation": "good",
    "extractedContent": {
      "title": "Advanced Aerospace Technologies",
      "preview": "Recent developments in aerospace...",
      "wordCount": 1500
    }
  },
  "processingTime": 2.3
}
```

### 1.3 批量站点发现

**Endpoint:** `POST /discover/sites`

**描述:** 基于关键词批量发现相关站点

**请求体:**
```json
{
  "keywords": ["radar technology", "missile guidance"],
  "maxSitesPerKeyword": 5,
  "qualityThreshold": 70,
  "excludeDomains": ["wikipedia.org", "youtube.com"]
}
```

**响应:**
```json
{
  "success": true,
  "data": {
    "sites": [
      {
        "url": "https://defense-tech.com/radar-advances",
        "domain": "defense-tech.com",
        "title": "Latest Radar Technology Advances",
        "qualityScore": 82,
        "keyword": "radar technology",
        "discoveredAt": "2024-12-19T10:30:00Z"
      }
    ],
    "totalFound": 15,
    "qualityFiltered": 8
  }
}
```

## 2. Crawl Service

### 2.1 抓取单个URL

**Endpoint:** `POST /crawl/single`

**描述:** 抓取指定URL的内容

**请求体:**
```json
{
  "url": "https://example.com/article",
  "options": {
    "includeMarkdown": true,
    "includeMetadata": true,
    "onlyMainContent": true,
    "timeout": 30
  }
}
```

**响应:**
```json
{
  "success": true,
  "data": {
    "url": "https://example.com/article",
    "title": "Advanced Aerospace Technologies",
    "content": "<html content>",
    "markdown": "# Advanced Aerospace Technologies\n\nContent...",
    "metadata": {
      "title": "Advanced Aerospace Technologies",
      "description": "Exploring cutting-edge aerospace innovations",
      "author": "John Smith",
      "publishDate": "2024-12-15",
      "wordCount": 1500
    },
    "images": [
      {
        "src": "https://example.com/image.jpg",
        "alt": "Aerospace technology diagram"
      }
    ]
  },
  "processingTime": 3.2,
  "timestamp": "2024-12-19T10:30:00Z"
}
```

### 2.2 批量抓取

**Endpoint:** `POST /crawl/batch`

**描述:** 批量抓取多个URL

**请求体:**
```json
{
  "urls": [
    "https://example1.com/article1",
    "https://example2.com/article2"
  ],
  "options": {
    "includeMarkdown": true,
    "maxConcurrent": 3,
    "retryFailed": true
  }
}
```

**响应:**
```json
{
  "success": true,
  "data": {
    "results": [
      {
        "url": "https://example1.com/article1",
        "success": true,
        "title": "Article 1 Title",
        "markdown": "Content...",
        "processingTime": 2.1
      },
      {
        "url": "https://example2.com/article2",
        "success": false,
        "error": "Timeout after 30 seconds",
        "statusCode": 0
      }
    ],
    "summary": {
      "total": 2,
      "successful": 1,
      "failed": 1
    }
  }
}
```

### 2.3 健康检查

**Endpoint:** `GET /crawl/health`

**响应:**
```json
{
  "status": "ok",
  "services": {
    "firecrawl": {
      "status": "ok",
      "responseTime": 150
    },
    "fallback": {
      "status": "ok",
      "responseTime": 80
    }
  },
  "timestamp": "2024-12-19T10:30:00Z"
}
```

## 3. Write Service

### 3.1 生成文章

**Endpoint:** `POST /write/article`

**描述:** 基于源内容生成SEO优化的文章

**请求体:**
```json
{
  "keyword": "hypersonic missile technology",
  "sourceContent": {
    "title": "Source Article Title",
    "content": "Source article content...",
    "url": "https://source.com/article"
  },
  "options": {
    "targetWordCount": 1000,
    "includeImages": true,
    "relatedProducts": ["missile-guidance-system", "radar-detector"],
    "seoOptimization": true,
    "customPrompt": "Focus on technical specifications"
  }
}
```

**响应:**
```json
{
  "success": true,
  "data": {
    "article": {
      "title": "Advanced Hypersonic Missile Technology: Latest Developments",
      "slug": "advanced-hypersonic-missile-technology-latest-developments",
      "content": "# Advanced Hypersonic Missile Technology\n\nContent...",
      "frontMatter": {
        "title": "Advanced Hypersonic Missile Technology: Latest Developments",
        "description": "Explore the latest developments in hypersonic missile technology...",
        "date": "2024-12-19",
        "category": "technology",
        "tags": ["hypersonic", "missile", "defense", "technology"],
        "author": "Technical Editorial Team",
        "readTime": 5,
        "heroImage": "https://images.unsplash.com/photo-123456789",
        "relatedProducts": ["missile-guidance-system", "radar-detector"]
      },
      "metadata": {
        "wordCount": 1050,
        "readTime": 5,
        "seoScore": 85,
        "keywordDensity": 2.1
      }
    },
    "generatedImage": "https://images.unsplash.com/photo-987654321",
    "processingTime": 15.7
  },
  "timestamp": "2024-12-19T10:30:00Z"
}
```

### 3.2 执行任务

**Endpoint:** `POST /write/execute-task`

**描述:** 执行完整的任务流程（抓取+生成+保存）

**请求体:**
```json
{
  "task": {
    "id": "task_1234567890",
    "keyword": "aerospace navigation systems",
    "targetUrl": "https://source.com/navigation-article",
    "relatedProducts": ["gyroscope-advanced", "navigation-computer"],
    "priority": "high"
  },
  "options": {
    "dryRun": false,
    "skipDuplicateCheck": false,
    "forceRegenerate": false,
    "publishImmediately": false
  }
}
```

**响应:**
```json
{
  "success": true,
  "data": {
    "taskId": "task_1234567890",
    "result": {
      "success": true,
      "outputPath": "./src/content/news/technology/2024-12-19-aerospace-navigation-systems.md",
      "generatedImageUrl": "https://images.unsplash.com/photo-123456789",
      "wordCount": 1200,
      "processingTimeMs": 18500,
      "metadata": {
        "titleGenerated": "Advanced Aerospace Navigation Systems: Technology Overview",
        "categoryAssigned": "technology",
        "seoScore": 88,
        "readabilityScore": 75
      }
    }
  }
}
```

### 3.3 批量任务执行

**Endpoint:** `POST /write/execute-batch`

**描述:** 批量执行多个任务

**请求体:**
```json
{
  "tasks": [
    {
      "id": "task_001",
      "keyword": "radar technology",
      "targetUrl": "https://source1.com/radar"
    },
    {
      "id": "task_002", 
      "keyword": "missile guidance",
      "targetUrl": "https://source2.com/guidance"
    }
  ],
  "options": {
    "maxConcurrent": 2,
    "stopOnError": false,
    "dryRun": false
  }
}
```

**响应:**
```json
{
  "success": true,
  "data": {
    "results": [
      {
        "taskId": "task_001",
        "success": true,
        "outputPath": "./src/content/news/technology/2024-12-19-radar-technology.md",
        "processingTimeMs": 16200
      },
      {
        "taskId": "task_002",
        "success": false,
        "error": "Failed to scrape target URL",
        "processingTimeMs": 5000
      }
    ],
    "summary": {
      "total": 2,
      "successful": 1,
      "failed": 1,
      "totalProcessingTime": 21200
    }
  }
}
```

## 4. 通用错误响应

所有服务在出错时返回统一格式：

```json
{
  "success": false,
  "error": {
    "code": "INVALID_REQUEST",
    "message": "Missing required field: keyword",
    "details": {
      "field": "keyword",
      "expected": "string",
      "received": "undefined"
    }
  },
  "timestamp": "2024-12-19T10:30:00Z"
}
```

### 错误代码

| 代码 | 描述 |
|------|------|
| `INVALID_REQUEST` | 请求参数无效 |
| `API_QUOTA_EXCEEDED` | API配额超限 |
| `SCRAPING_FAILED` | 内容抓取失败 |
| `GENERATION_FAILED` | 内容生成失败 |
| `DUPLICATE_CONTENT` | 重复内容检测 |
| `RATE_LIMITED` | 请求频率超限 |
| `INTERNAL_ERROR` | 内部服务错误 |

## 5. 认证和限制

### 5.1 API密钥认证

所有请求需要在Header中包含API密钥：

```
Authorization: Bearer your-api-key-here
Content-Type: application/json
```

### 5.2 速率限制

| 服务 | 限制 |
|------|------|
| Discovery | 100 请求/小时 |
| Crawl | 200 请求/小时 |
| Write | 50 请求/小时 |

### 5.3 响应头

```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1703001600
X-Request-ID: req_1234567890abcdef
```

## 6. Webhook通知

### 6.1 任务完成通知

当任务完成时，系统可以发送webhook通知：

**Webhook URL:** 在系统配置中设置

**请求体:**
```json
{
  "event": "task.completed",
  "taskId": "task_1234567890",
  "status": "success",
  "data": {
    "keyword": "aerospace technology",
    "outputPath": "./src/content/news/technology/2024-12-19-aerospace-technology.md",
    "wordCount": 1200,
    "seoScore": 85
  },
  "timestamp": "2024-12-19T10:30:00Z"
}
```

### 6.2 错误通知

```json
{
  "event": "task.failed",
  "taskId": "task_1234567890",
  "status": "failed",
  "error": {
    "code": "SCRAPING_FAILED",
    "message": "Unable to access target URL"
  },
  "timestamp": "2024-12-19T10:30:00Z"
}
```

## 7. 使用示例

### 7.1 完整工作流程

```bash
# 1. 发现关键词
curl -X POST http://localhost:3000/discover/trends \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "topics": ["aerospace technology"],
    "maxKeywords": 5
  }'

# 2. 评估站点质量
curl -X POST http://localhost:3000/discover/evaluate-site \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://example.com/aerospace-article",
    "keyword": "aerospace technology"
  }'

# 3. 抓取内容
curl -X POST http://localhost:3000/crawl/single \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://example.com/aerospace-article",
    "options": {
      "includeMarkdown": true,
      "onlyMainContent": true
    }
  }'

# 4. 生成文章
curl -X POST http://localhost:3000/write/article \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "keyword": "aerospace technology",
    "sourceContent": {
      "title": "Source Title",
      "content": "Source content...",
      "url": "https://example.com/aerospace-article"
    },
    "options": {
      "targetWordCount": 1000,
      "includeImages": true
    }
  }'
```

### 7.2 n8n集成示例

在n8n中使用HTTP Request节点调用这些API：

```json
{
  "method": "POST",
  "url": "http://localhost:3000/write/execute-task",
  "headers": {
    "Authorization": "Bearer {{ $env.API_KEY }}",
    "Content-Type": "application/json"
  },
  "body": {
    "task": {
      "id": "{{ $json.taskId }}",
      "keyword": "{{ $json.keyword }}",
      "targetUrl": "{{ $json.url }}",
      "relatedProducts": "{{ $json.products }}"
    },
    "options": {
      "dryRun": false
    }
  }
}
```

## 8. 部署和配置

### 8.1 环境变量

```bash
# API Keys
SERP_API_KEY=your_serp_api_key
FIRECRAWL_API_KEY=your_firecrawl_api_key
OPENAI_API_KEY=your_openai_api_key
UNSPLASH_API_KEY=your_unsplash_api_key

# Service Configuration
PORT=3000
NODE_ENV=production
API_KEY=your_service_api_key

# Rate Limiting
RATE_LIMIT_WINDOW=3600000
RATE_LIMIT_MAX=100

# Content Settings
MAX_WORD_COUNT=3000
MIN_WORD_COUNT=800
DEFAULT_CATEGORY=technology
```

### 8.2 Docker部署

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
```

这个API契约为前后端开发、n8n集成和系统测试提供了清晰的接口规范。 