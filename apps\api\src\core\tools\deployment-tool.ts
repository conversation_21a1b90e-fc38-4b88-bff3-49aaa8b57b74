import { exec } from 'child_process';
import { promisify } from 'util';
import { join, resolve } from 'path';
import { existsSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import { heroImageValidator } from './hero-image-validator.js';

const execAsync = promisify(exec);
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// 获取项目根目录路径
const projectRoot = resolve(__dirname, '../../../../..');

export interface DeploymentConfig {
  projectPath: string;
  buildCommand: string;
  deployCommand: string;
  preDeployChecks: boolean;
  heroImageValidation: boolean;
  environment: 'production' | 'preview';
  timeout: number;
}

export interface DeploymentResult {
  success: boolean;
  deploymentUrl?: string;
  buildTime?: number;
  deployTime?: number;
  logs: string[];
  error?: string;
}

export class DeploymentTool {
  private config: DeploymentConfig;
  private logs: string[] = [];

  constructor(config: Partial<DeploymentConfig> = {}) {
    this.config = {
      projectPath: join(projectRoot, 'apps/web'),
      buildCommand: 'pnpm build',
      deployCommand: 'vercel --prod',
      preDeployChecks: true,
      heroImageValidation: true,
      environment: 'production',
      timeout: 300000, // 5分钟
      ...config
    };
  }

  async deploy(): Promise<DeploymentResult> {
    const startTime = Date.now();
    this.logs = [];

    try {
      this.log('🚀 Starting deployment process...');

      // 预检查
      if (this.config.preDeployChecks) {
        await this.runPreDeployChecks();
      }

      // 构建项目
      const buildStartTime = Date.now();
      await this.buildProject();
      const buildTime = Date.now() - buildStartTime;
      this.log(`✅ Build completed in ${buildTime}ms`);

      // 部署到Vercel
      const deployStartTime = Date.now();
      const deploymentUrl = await this.deployToVercel();
      const deployTime = Date.now() - deployStartTime;
      this.log(`🎉 Deployment completed in ${deployTime}ms`);
      this.log(`🌐 Deployment URL: ${deploymentUrl}`);

      return {
        success: true,
        deploymentUrl,
        buildTime,
        deployTime,
        logs: [...this.logs]
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.log(`❌ Deployment failed: ${errorMessage}`);
      
      return {
        success: false,
        logs: [...this.logs],
        error: errorMessage
      };
    }
  }

  private async runPreDeployChecks(): Promise<void> {
    this.log('🔍 Running pre-deployment checks...');

    // 检查项目目录是否存在
    if (!existsSync(this.config.projectPath)) {
      throw new Error(`Project path does not exist: ${this.config.projectPath}`);
    }

    // 检查package.json是否存在
    const packageJsonPath = join(this.config.projectPath, 'package.json');
    if (!existsSync(packageJsonPath)) {
      throw new Error('package.json not found in project directory');
    }

    // 检查是否有新的内容文件
    const contentPath = join(this.config.projectPath, 'src/content/news');
    if (!existsSync(contentPath)) {
      this.log('⚠️  Warning: No content directory found');
    }

    // heroImage验证和修复
    if (this.config.heroImageValidation) {
      this.log('🖼️  Validating and fixing hero images...');
      try {
        const validationResult = await heroImageValidator.validateAndFix();
        
        if (validationResult.success) {
          if (validationResult.fixedFiles > 0) {
            this.log(`✅ Hero image validation completed: ${validationResult.fixedFiles} files fixed, ${validationResult.validFiles} files valid`);
          } else {
            this.log(`✅ Hero image validation completed: All ${validationResult.validFiles} files have valid hero images`);
          }
        } else {
          this.log(`⚠️  Hero image validation completed with errors: ${validationResult.errors.length} files had issues`);
          // 不阻止部署，只是警告
        }
      } catch (error) {
        this.log(`⚠️  Hero image validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        // 不阻止部署，只是警告
      }
    }

    // 检查Vercel CLI是否可用
    try {
      await execAsync('vercel --version', { cwd: this.config.projectPath });
      this.log('✅ Vercel CLI is available');
    } catch (error) {
      throw new Error('Vercel CLI not found. Please install it with: npm i -g vercel');
    }

    this.log('✅ Pre-deployment checks passed');
  }

  private async buildProject(): Promise<void> {
    this.log(`🔨 Building project with command: ${this.config.buildCommand}`);

    try {
      const { stdout, stderr } = await execAsync(this.config.buildCommand, {
        cwd: this.config.projectPath,
        timeout: this.config.timeout
      });

      if (stdout) {
        this.log(`Build output: ${stdout}`);
      }
      if (stderr) {
        this.log(`Build warnings: ${stderr}`);
      }
    } catch (error) {
      throw new Error(`Build failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async deployToVercel(): Promise<string> {
    this.log(`🚀 Deploying to Vercel with command: ${this.config.deployCommand}`);

    try {
      const { stdout, stderr } = await execAsync(this.config.deployCommand, {
        cwd: this.config.projectPath,
        timeout: this.config.timeout
      });

      if (stderr) {
        this.log(`Deploy warnings: ${stderr}`);
      }

      // 从输出中提取部署URL
      const deploymentUrl = this.extractDeploymentUrl(stdout);
      
      if (!deploymentUrl) {
        throw new Error('Could not extract deployment URL from Vercel output');
      }

      return deploymentUrl;
    } catch (error) {
      throw new Error(`Deployment failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private extractDeploymentUrl(output: string): string | null {
    // Vercel输出中通常包含部署URL
    const urlMatch = output.match(/https:\/\/[^\s]+\.vercel\.app/);
    return urlMatch ? urlMatch[0] : null;
  }

  private log(message: string): void {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ${message}`;
    this.logs.push(logMessage);
    console.log(logMessage);
  }

  // 获取部署状态
  async getDeploymentStatus(deploymentUrl: string): Promise<{
    status: 'ready' | 'building' | 'error' | 'unknown';
    lastChecked: string;
  }> {
    try {
      const response = await fetch(deploymentUrl, { method: 'HEAD' });
      return {
        status: response.ok ? 'ready' : 'error',
        lastChecked: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'unknown',
        lastChecked: new Date().toISOString()
      };
    }
  }

  // 获取部署日志
  getLogs(): string[] {
    return [...this.logs];
  }
}

// 创建默认实例
export const deploymentTool = new DeploymentTool(); 