# 图片服务架构详解

## 🔄 服务回退链架构

### 优先级设计
```
Workers (优先级1) → Fal.ai (优先级2) → Unsplash (优先级3，兜底)
```

## 🎯 各服务特点

### 1. **Cloudflare Workers** (优先级1)
- **输入**: 提示词 (prompt)
- **处理**: 通过您的Worker处理图片生成/获取
- **输出**: 直接返回图片URL
- **特点**: 最快速，可自定义逻辑

```typescript
// Worker期望的请求格式
{
  prompt: "aerospace technology industrial",
  keyword: "aerospace",
  title: "Latest Aerospace Innovations",
  category: "industry",
  dimensions: { width: 1200, height: 675 },
  orientation: "landscape"
}

// Worker期望的响应格式  
{
  success: true,
  url: "https://your-images.domain.com/generated-image.jpg",
  provider: "worker",
  metadata: { ... }
}
```

### 2. **Fal.ai** (优先级2)
- **输入**: 提示词 (prompt)
- **处理**: AI生成图片
- **输出**: 返回生成的图片URL
- **特点**: 高质量AI生成，但较慢

### 3. **Unsplash** (优先级3，兜底)
- **输入**: 关键词 (keyword)
- **处理**: 搜索相关的真实照片
- **输出**: 返回匹配的照片URL
- **特点**: 可靠稳定，真实照片库

```typescript
// Unsplash搜索逻辑
buildSearchTerms(request) {
  const terms = [];
  
  // 1. 优先使用文章关键词
  if (request.keyword) terms.push(request.keyword);
  
  // 2. 基于分类添加相关术语
  if (request.category === 'industry') {
    terms.push('industrial technology', 'manufacturing');
  }
  
  // 3. 回退搜索词确保有结果
  terms.push('aerospace technology', 'technology');
  
  return terms; // 依次尝试搜索
}
```

## 🛠️ 工作流程

### 图片生成请求处理：
```
1. 接收请求 { keyword: "space exploration", category: "research" }

2. 尝试 Workers:
   - 构建prompt: "space exploration scientific research laboratory"
   - 调用 Worker API
   - 成功 → 返回URL ✅
   - 失败 → 继续下一个

3. 尝试 Fal.ai:
   - 使用相同prompt
   - AI生成图片
   - 成功 → 返回URL ✅
   - 失败 → 继续下一个

4. 尝试 Unsplash:
   - 搜索关键词: "space exploration"
   - 找到相关照片
   - 成功 → 返回照片URL ✅
   - 失败 → 所有服务失败 ❌
```

## 🔧 关键配置

### 环境变量映射：
```bash
# Workers配置
CLOUDFLARE_WORKER_URL=https://your-worker.domain.workers.dev
CLOUDFLARE_API_KEY=your_worker_token

# Fal.ai配置  
FAL_API_KEY=your_fal_key

# Unsplash配置
UNSPLASH_ACCESS_KEY=your_unsplash_key

# 服务启用
ENABLE_IMAGE_GENERATION=true
IMAGE_PROVIDER=workers  # 指定主要服务，但仍会启用回退
```

### 服务启用逻辑：
```typescript
// 每个服务根据API密钥自动启用
workers: { enabled: !!CLOUDFLARE_WORKER_URL },
fal: { enabled: !!FAL_API_KEY },
unsplash: { enabled: !!UNSPLASH_ACCESS_KEY }
```

## 🎨 图片内容确保

### 所有服务都生成**文章相关**的图片：

- **Workers**: 您可以在Worker中实现任何逻辑（AI生成/图库搜索/API调用）
- **Fal.ai**: 基于文章内容的AI生成
- **Unsplash**: 基于关键词的相关照片搜索

### ❌ 绝不使用固定图片！

每个请求都会根据文章的：
- `keyword` (关键词)
- `title` (标题)
- `category` (分类)
- `prompt` (自定义提示词)

生成或搜索**相关的、独特的**图片内容。

## 🚀 实际使用示例

### 文章：《AI在航空航天领域的应用》
```typescript
const request = {
  keyword: "aerospace AI",
  title: "AI Applications in Aerospace Industry", 
  category: "industry"
};

// Workers尝试: prompt = "aerospace AI industrial technology manufacturing"
// Fal.ai尝试: 相同prompt，AI生成相关图片
// Unsplash尝试: 搜索 "aerospace AI" → "industrial technology" → "aerospace technology"
```

每个服务都会为这篇特定文章生成不同但相关的图片！ 