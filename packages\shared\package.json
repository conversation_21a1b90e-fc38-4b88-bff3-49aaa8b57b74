{"name": "@news-site/shared", "version": "0.1.0", "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.js"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.js"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint src/", "type-check": "tsc --noEmit"}, "dependencies": {"zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0"}}