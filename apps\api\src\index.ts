import { join, resolve } from 'path';
import { config } from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// 获取项目根目录路径（从 apps/api/src 向上三级到项目根目录）
const projectRoot = resolve(__dirname, '../../../');

// 加载环境变量：优先加载 .env.local，然后 .env
console.log('🔧 加载环境变量从:', projectRoot);
config({ path: join(projectRoot, '.env.local') });
config({ path: join(projectRoot, '.env') });

// 验证关键环境变量
console.log('🔑 OPENAI_API_KEY:', process.env.OPENAI_API_KEY ? '已设置' : '未设置');
console.log('🔑 SERP_API_KEY:', process.env.SERP_API_KEY ? '已设置' : '未设置');
console.log('🔑 TAVILY_API_KEY:', process.env.TAVILY_API_KEY ? '已设置' : '未设置');
console.log('🔑 FIRECRAWL_API_KEY:', process.env.FIRECRAWL_API_KEY ? '已设置' : '未设置');
console.log('🔑 NEWS_API_KEY:', process.env.NEWS_API_KEY ? '已设置' : '未设置');
console.log('🔑 IMAGE_API_KEY:', process.env.IMAGE_API_KEY ? '已设置' : '未设置');
console.log('🔑 CLOUDFLARE_WORKER_URL:', process.env.CLOUDFLARE_WORKER_URL ? '已设置' : '未设置');
console.log('🔑 NODE_ENV:', process.env.NODE_ENV || 'development');
import Fastify from 'fastify';
import cors from '@fastify/cors';
import rateLimit from '@fastify/rate-limit';
import swagger from '@fastify/swagger';
import swaggerUi from '@fastify/swagger-ui';

// 数据库
import { initDatabase } from './core/database/connection.js';

// 路由
import planRoutes from './routes/v1/plan.js';
//import taskRoutes from './routes/v1/task.js';
import contentRoutes from './routes/v1/content.js';
import imageRoutes from './routes/v1/image.js';
//import adminRoutes from './routes/v1/admin.js';
import { contentRoutes as newContentRoutes } from './routes/content.js';
import { schedulerRoutes } from './routes/scheduler.js';
import { productsRoutes } from './routes/products.js';
import automationRoutes from './routes/automation.js';
import n8nRoutes from './routes/n8n.js';


// 插件
//import errorHandler from './plugins/error-handler.js';
//import validation from './plugins/validation.js';

const PORT = Number(process.env.PORT) || 3002;
const HOST = process.env.HOST || '0.0.0.0';

async function createApp() {
  const app = Fastify({
    logger: {
      level: process.env.LOG_LEVEL || 'info',
      transport: process.env.NODE_ENV === 'development' 
        ? { target: 'pino-pretty' }
        : undefined
    }
  });

  // 注册插件
  await app.register(cors, {
    origin: process.env.NODE_ENV === 'production' 
      ? ['https://yourdomain.com']
      : true
  });

  await app.register(rateLimit, {
    max: 100,
    timeWindow: '1 minute'
  });

  // Swagger文档
  await app.register(swagger, {
    openapi: {
      info: {
        title: 'News Site API',
        description: 'Aerospace News Platform API',
        version: '1.0.0'
      },
      servers: [
        {
          url: `http://localhost:${PORT}`,
          description: 'Development server'
        }
      ]
    }
  });

  await app.register(swaggerUi, {
    routePrefix: '/docs',
    uiConfig: {
      docExpansion: 'list',
      deepLinking: false
    }
  });

  // 错误处理和验证插件
  //await app.register(errorHandler);
  //await app.register(validation);

  // 健康检查
  app.get('/health', async () => {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      uptime: process.uptime()
    };
  });

  // 注册路由
  await app.register(planRoutes, { prefix: '/api/v1/plans' });
  //await app.register(taskRoutes, { prefix: '/api/v1/tasks' });
  await app.register(contentRoutes, { prefix: '/api/v1/content' });
  await app.register(imageRoutes, { prefix: '/api/v1/image' });
  //await app.register(adminRoutes, { prefix: '/api/v1/admin' });
  
  // 新的Agent路由
  await app.register(newContentRoutes, { prefix: '/api/content' });
  await app.register(schedulerRoutes, { prefix: '/api/scheduler' });
  await app.register(productsRoutes, { prefix: '/api/products' });
  await app.register(automationRoutes, { prefix: '/api' });
  await app.register(n8nRoutes, { prefix: '/api' });


  return app;
}

async function start() {
  try {
    // 初始化数据库
    await initDatabase();
    
    const app = await createApp();
    
    await app.listen({
      port: PORT,
      host: HOST
    });

    console.log(`🚀 API服务已启动`);
    console.log(`📍 服务地址: http://${HOST}:${PORT}`);
    console.log(`📚 API文档: http://${HOST}:${PORT}/docs`);
    console.log(`❤️  健康检查: http://${HOST}:${PORT}/health`);
    
  } catch (error) {
    console.error('启动失败:', error);
    process.exit(1);
  }
}

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('收到SIGTERM信号，正在关闭服务...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('收到SIGINT信号，正在关闭服务...');
  process.exit(0);
});

start(); 