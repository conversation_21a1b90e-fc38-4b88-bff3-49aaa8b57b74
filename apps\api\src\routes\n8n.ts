import { FastifyInstance } from 'fastify';
import { planService } from '../core/services/plan-service.js';
import { contentService } from '../core/services/content-service.js';
import { linkDiscoveryTool } from '../core/tools/link-discovery-tool.js';
import { createImageService } from '../core/tools/image/image-service.js';
import { deploymentTool } from '../core/tools/deployment-tool.js';
import { automationConfigService } from '../core/services/automation-config-service.js';
import { logger } from '../utils/logger.js';

export default async function n8nRoutes(fastify: FastifyInstance) {
  // 图片服务
  const imageService = createImageService((level, message, data) => {
    logger[level as keyof typeof logger](message, data);
  });

  // n8n路由前缀钩子 - 验证n8n模式
  fastify.addHook('preHandler', async (request, reply) => {
    if (!automationConfigService.isN8nMode()) {
      return reply.status(403).send({
        success: false,
        error: 'N8N routes are only available when AUTOMATION_MODE=n8n'
      });
    }

    // 可选：验证n8n API密钥
    const n8nConfig = automationConfigService.getN8nConfig();
    if (n8nConfig.apiKey) {
      const authHeader = request.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return reply.status(401).send({
          success: false,
          error: 'Authorization header required'
        });
      }

      const token = authHeader.substring(7);
      if (token !== n8nConfig.apiKey) {
        return reply.status(401).send({
          success: false,
          error: 'Invalid API key'
        });
      }
    }
  });

  // 1. 创建计划 (n8n step 1)
  fastify.post<{
    Body: { 
      date?: string;
      maxTasks?: number;
      categories?: string[];
      forceRegenerate?: boolean;
    }
  }>('/n8n/create-plan', async (request, reply) => {
    try {
      const { 
        date = new Date().toISOString().split('T')[0],
        maxTasks = 3,
        categories = ['industry', 'research', 'frontier'],
        forceRegenerate = true
      } = request.body;

      logger.info('[N8N] Creating plan', { date, maxTasks, categories });

      const plan = await planService.createPlan({
        date,
        maxTasks,
        categories,
        forceRegenerate
      });

      return {
        success: true,
        data: {
          planId: plan.id,
          tasksCount: plan.tasks.length,
          tasks: plan.tasks.map(task => ({
            id: task.id,
            title: task.title,
            category: task.category,
            keyword: task.keyword,
            status: task.status
          }))
        }
      };
    } catch (error) {
      logger.error('[N8N] Plan creation failed:', error);
      return reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // 2. 执行单个任务 (n8n step 2)
  fastify.post<{
    Params: { taskId: string }
  }>('/n8n/execute-task/:taskId', async (request, reply) => {
    try {
      const { taskId } = request.params;

      logger.info('[N8N] Executing task', { taskId });

      const result = await planService.executeTask(taskId);

      return {
        success: true,
        data: {
          taskId,
          status: result.status,
          outputPath: result.outputPath,
          metadata: result.metadata,
          error: result.error
        }
      };
    } catch (error) {
      logger.error('[N8N] Task execution failed:', error);
      return reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // 3. 批量执行计划中的任务 (n8n step 2 alternative)
  fastify.post<{
    Params: { planId: string };
    Body: { 
      maxConcurrent?: number;
      taskIds?: string[];
    }
  }>('/n8n/execute-plan/:planId', async (request, reply) => {
    try {
      const { planId } = request.params;
      const { maxConcurrent = 1, taskIds } = request.body;

      logger.info('[N8N] Executing plan', { planId, maxConcurrent, taskIds });

      let results;
      if (taskIds && taskIds.length > 0) {
        // 执行指定的任务
        results = {
          completed: 0,
          failed: 0,
          results: []
        };
        
        for (const taskId of taskIds) {
          try {
            const result = await planService.executeTask(taskId);
            results.results.push(result);
            if (result.status === 'completed') {
              results.completed++;
            } else {
              results.failed++;
            }
          } catch (error) {
            results.failed++;
            results.results.push({
              taskId,
              status: 'failed',
              error: error instanceof Error ? error.message : 'Unknown error'
            });
          }
        }
      } else {
        // 执行整个计划
        results = await planService.executePlan(planId, maxConcurrent);
      }

      return {
        success: true,
        data: results
      };
    } catch (error) {
      logger.error('[N8N] Plan execution failed:', error);
      return reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // 4. 生成图片 (n8n step 3)
  fastify.post<{
    Body: { 
      keyword: string;
      title?: string;
      category?: string;
      style?: string;
    }
  }>('/n8n/generate-image', async (request, reply) => {
    try {
      const { keyword, title, category, style } = request.body;

      if (!keyword) {
        return reply.status(400).send({
          success: false,
          error: 'Keyword is required'
        });
      }

      logger.info('[N8N] Generating image', { keyword, title, category });

      const result = await imageService.generateImage({
        keyword,
        title,
        category,
        style,
        dimensions: { width: 1200, height: 675 },
        orientation: 'landscape'
      });

      return {
        success: result.success,
        data: result.success ? {
          url: result.url,
          provider: result.provider,
          metadata: result.metadata
        } : null,
        error: result.success ? null : result.error
      };
    } catch (error) {
      logger.error('[N8N] Image generation failed:', error);
      return reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // 5. 部署到Vercel (n8n step 4)
  fastify.post<{
    Body: { 
      environment?: 'production' | 'preview';
      message?: string;
    }
  }>('/n8n/deploy', async (request, reply) => {
    try {
      const { environment = 'production', message } = request.body;

      logger.info('[N8N] Starting deployment', { environment, message });

      const result = await deploymentTool.deploy();

      return {
        success: result.success,
        data: result.success ? {
          deploymentUrl: result.deploymentUrl,
          buildTime: result.buildTime,
          deployTime: result.deployTime,
          environment
        } : null,
        error: result.success ? null : result.error
      };
    } catch (error) {
      logger.error('[N8N] Deployment failed:', error);
      return reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // 6. 内容生成 (供n8n直接调用)
  fastify.post<{
    Body: { 
      keyword: string;
      relatedProducts?: string[];
      category?: string;
    }
  }>('/n8n/generate-content', async (request, reply) => {
    try {
      const { keyword, relatedProducts = [], category } = request.body;

      if (!keyword) {
        return reply.status(400).send({
          success: false,
          error: 'Keyword is required'
        });
      }

      logger.info('[N8N] Generating content', { keyword, category });

      const content = await contentService.generateFromKeyword(keyword, relatedProducts);

      return {
        success: true,
        data: {
          title: content.title,
          content: content.content,
          metadata: content.metadata,
          tags: content.tags,
          category: content.category
        }
      };
    } catch (error) {
      logger.error('[N8N] Content generation failed:', error);
      return reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // 7. 链接发现 (供n8n调用)
  fastify.post<{
    Body: { 
      keyword: string;
      maxUrls?: number;
    }
  }>('/n8n/discover-links', async (request, reply) => {
    try {
      const { keyword, maxUrls = 10 } = request.body;

      if (!keyword) {
        return reply.status(400).send({
          success: false,
          error: 'Keyword is required'
        });
      }

      logger.info('[N8N] Discovering links', { keyword, maxUrls });

      const results = await linkDiscoveryTool.discoverLinks(keyword);

      return {
        success: true,
        data: {
          keyword,
          count: results.length,
          links: results.slice(0, maxUrls)
        }
      };
    } catch (error) {
      logger.error('[N8N] Link discovery failed:', error);
      return reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // 8. 获取状态 (n8n监控)
  fastify.get('/n8n/status', async (request, reply) => {
    try {
      const automationConfig = automationConfigService.getConfig();
      const n8nValidation = automationConfigService.validateN8nConfig();
      
      return {
        success: true,
        data: {
          mode: automationConfig.mode,
          n8nConfigValid: n8nValidation.valid,
          n8nConfigMessage: n8nValidation.message,
          services: {
            imageService: await imageService.healthCheck(),
            deploymentTool: true, // 简化检查
            contentService: true,
            linkDiscovery: linkDiscoveryTool.getStatus()
          }
        }
      };
    } catch (error) {
      logger.error('[N8N] Status check failed:', error);
      return reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });
} 