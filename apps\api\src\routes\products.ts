import type { FastifyInstance } from 'fastify';
import { ProductManagerAgent } from '../core/agents/product-manager-agent.js';
import { ProductMatchingService } from '../core/services/product-matching-service.js';

export async function productsRoutes(fastify: FastifyInstance) {
  const productManager = new ProductManagerAgent();
  await productManager.initialize();

  const productMatchingService = new ProductMatchingService();

  // 获取所有产品
  fastify.get('/', async (request, reply) => {
    try {
      const result = await productManager.execute({
        action: 'get_products',
        options: request.query
      }, {
        requestId: request.id,
        userId: 'api-user',
        dryRun: false,
        metadata: { source: 'api', endpoint: '/api/products' }
      });

      return {
        success: true,
        data: result
      };
    } catch (error) {
      reply.code(500);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  // 产品匹配
  fastify.post('/match', async (request, reply) => {
    try {
      const { title, keyword, description, extractedContent } = request.body as {
        title: string;
        keyword: string;
        description: string;
        extractedContent: string;
      };

      if (!title && !keyword && !description) {
        reply.code(400);
        return {
          success: false,
          error: 'At least one of title, keyword, or description is required'
        };
      }

      const matches = await productMatchingService.matchProducts({
        title: title || '',
        keyword: keyword || '',
        description: description || '',
        extractedContent: extractedContent || description || ''
      });

      return {
        success: true,
        data: matches
      };
    } catch (error) {
      reply.code(500);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  // 搜索产品
  fastify.get('/search', async (request, reply) => {
    try {
      const { q: query, ...options } = request.query as { q: string; [key: string]: any };
      
      if (!query) {
        reply.code(400);
        return {
          success: false,
          error: 'Query parameter "q" is required'
        };
      }

      const result = await productManager.execute({
        action: 'search_products',
        data: { query },
        options
      }, {
        requestId: request.id,
        userId: 'api-user',
        dryRun: false,
        metadata: { source: 'api', endpoint: '/api/products/search' }
      });

      return {
        success: true,
        data: result
      };
    } catch (error) {
      reply.code(500);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  // 获取相关产品
  fastify.post('/related', async (request, reply) => {
    try {
      const { productIds, ...options } = request.body as { productIds: string[]; [key: string]: any };
      
      if (!productIds || !Array.isArray(productIds)) {
        reply.code(400);
        return {
          success: false,
          error: 'productIds array is required'
        };
      }

      const result = await productManager.execute({
        action: 'get_related_products',
        data: { productIds },
        options
      }, {
        requestId: request.id,
        userId: 'api-user',
        dryRun: false,
        metadata: { source: 'api', endpoint: '/api/products/related' }
      });

      return {
        success: true,
        data: result
      };
    } catch (error) {
      reply.code(500);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  // 添加产品
  fastify.post('/', async (request, reply) => {
    try {
      const result = await productManager.execute({
        action: 'add_product',
        data: request.body
      }, {
        requestId: request.id,
        userId: 'api-user',
        dryRun: false,
        metadata: { source: 'api', endpoint: '/api/products' }
      });

      reply.code(201);
      return {
        success: true,
        data: result
      };
    } catch (error) {
      reply.code(400);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  // 更新产品
  fastify.put('/:productId', async (request, reply) => {
    try {
      const { productId } = request.params as { productId: string };
      
      const result = await productManager.execute({
        action: 'update_product',
        data: { id: productId, ...(request.body as object) }
      }, {
        requestId: request.id,
        userId: 'api-user',
        dryRun: false,
        metadata: { source: 'api', endpoint: '/api/products/:productId' }
      });

      return {
        success: true,
        data: result
      };
    } catch (error) {
      reply.code(400);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  // 删除产品
  fastify.delete('/:productId', async (request, reply) => {
    try {
      const { productId } = request.params as { productId: string };
      
      const result = await productManager.execute({
        action: 'delete_product',
        data: { id: productId }
      }, {
        requestId: request.id,
        userId: 'api-user',
        dryRun: false,
        metadata: { source: 'api', endpoint: '/api/products/:productId' }
      });

      return {
        success: true,
        data: result
      };
    } catch (error) {
      reply.code(400);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  // 获取站点列表
  fastify.get('/sites', async (request, reply) => {
    try {
      const sites = await productManager.getSites();
      return {
        success: true,
        data: { sites }
      };
    } catch (error) {
      reply.code(500);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  // 获取分类列表
  fastify.get('/categories', async (request, reply) => {
    try {
      const categories = await productManager.getCategories();
      return {
        success: true,
        data: { categories }
      };
    } catch (error) {
      reply.code(500);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  // 验证数据
  fastify.get('/validate', async (request, reply) => {
    try {
      const result = await productManager.execute({
        action: 'validate_data'
      }, {
        requestId: request.id,
        userId: 'api-user',
        dryRun: false,
        metadata: { source: 'api', endpoint: '/api/products/validate' }
      });

      return {
        success: true,
        data: result
      };
    } catch (error) {
      reply.code(500);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  // 导出CSV
  fastify.post('/export', async (request, reply) => {
    try {
      const { outputPath, ...options } = request.body as { outputPath: string; [key: string]: any };
      
      const result = await productManager.execute({
        action: 'export_csv',
        data: { outputPath },
        options
      }, {
        requestId: request.id,
        userId: 'api-user',
        dryRun: false,
        metadata: { source: 'api', endpoint: '/api/products/export' }
      });

      return {
        success: true,
        data: result
      };
    } catch (error) {
      reply.code(400);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  // 产品管理Agent健康检查
  fastify.get('/health', async (request, reply) => {
    try {
      const health = await productManager.healthCheck();
      return {
        success: true,
        data: health
      };
    } catch (error) {
      reply.code(500);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });
} 