# 自动化模式配置指南

## 🎯 两种自动化模式

### 模式1: **内置调度器模式** (默认)
- **特点**: 服务内部管理调度，简单易用
- **适用场景**: 单一定时任务，简单部署
- **优势**: 配置简单，零依赖

### 模式2: **n8n编排模式**
- **特点**: 外部n8n工作流调用独立API端点
- **适用场景**: 复杂工作流，多种触发条件，灵活编排
- **优势**: 可视化编排，多触发器，高度灵活

## 🔧 配置方式

### 内置调度器模式 (默认)

```bash
# .env 配置
AUTOMATION_MODE=scheduler  # 或不设置，默认为scheduler

# 调度器配置
SCHEDULER_ENABLED=true
SCHEDULER_CRON=0 9 * * *    # 每天上午9点
SCHEDULER_MAX_TASKS=3
SCHEDULER_AUTO_DEPLOY=true
```

**工作方式**:
```
启动服务 → 自动创建调度任务 → 定时执行全流程
```

### n8n编排模式

```bash
# .env 配置
AUTOMATION_MODE=n8n

# n8n配置 (可选)
N8N_WEBHOOK_URL=https://your-n8n.domain.com/webhook/content-automation
N8N_API_KEY=your_secure_api_key  # 可选，用于API认证
```

**工作方式**:
```
n8n工作流 → 调用API端点 → 执行特定步骤 → 组合成完整流程
```

## 🚀 使用指南

### 内置调度器模式使用

#### 1. 启动服务
```bash
# 确保配置
AUTOMATION_MODE=scheduler
SCHEDULER_ENABLED=true

# 启动
pnpm dev
```

#### 2. 监控调度
```bash
# 查看调度状态
curl http://localhost:3002/api/automation/scheduler/status

# 手动触发
curl -X POST http://localhost:3002/api/automation/scheduler/jobs/daily-content-generation/trigger
```

### n8n编排模式使用

#### 1. 启动服务
```bash
# 确保配置
AUTOMATION_MODE=n8n

# 启动
pnpm dev
```

#### 2. 配置n8n工作流

**步骤1: 创建计划**
```
HTTP Request Node:
URL: http://localhost:3002/api/n8n/create-plan
Method: POST
Body: {
  "date": "2024-12-07",
  "maxTasks": 3,
  "categories": ["industry", "research"]
}
```

**步骤2: 执行任务**
```
HTTP Request Node:
URL: http://localhost:3002/api/n8n/execute-plan/{{ $json.data.planId }}
Method: POST
Body: {
  "maxConcurrent": 2
}
```

**步骤3: 部署**
```
HTTP Request Node:
URL: http://localhost:3002/api/n8n/deploy
Method: POST
Body: {
  "environment": "production"
}
```

## 📋 n8n API端点参考

### 基础端点

| 端点 | 方法 | 描述 | 用途 |
|------|------|------|------|
| `/api/n8n/status` | GET | 服务状态 | 健康检查 |
| `/api/n8n/create-plan` | POST | 创建计划 | 工作流步骤1 |
| `/api/n8n/execute-plan/:planId` | POST | 执行计划 | 工作流步骤2 |
| `/api/n8n/execute-task/:taskId` | POST | 执行单任务 | 精细控制 |
| `/api/n8n/deploy` | POST | 部署到Vercel | 工作流步骤3 |

### 独立功能端点

| 端点 | 方法 | 描述 | 用途 |
|------|------|------|------|
| `/api/n8n/generate-content` | POST | 生成内容 | 单独内容生成 |
| `/api/n8n/generate-image` | POST | 生成图片 | 单独图片生成 |
| `/api/n8n/discover-links` | POST | 发现链接 | 链接发现 |

## 🎨 n8n工作流示例

### 示例1: 基础自动化
```
触发器(定时/webhook) → 创建计划 → 执行计划 → 部署
```

### 示例2: 分时段执行
```
08:00: 创建计划 (行业类)
10:00: 创建计划 (研究类) 
12:00: 执行所有计划
14:00: 部署
```

### 示例3: 条件触发
```
RSS监控 → 检测新技术 → 生成相关内容 → 立即发布
```

### 示例4: 批处理模式
```
每周一: 创建整周计划
每天: 执行当天任务 + 部署
周末: 汇总分析
```

## 🔄 模式切换

### 运行时切换 (临时)
```bash
# 切换到n8n模式
curl -X POST http://localhost:3002/api/automation/config/mode \
  -H "Content-Type: application/json" \
  -d '{"mode": "n8n", "n8nWebhookUrl": "https://your-n8n.com/webhook"}'

# 切换回调度器模式
curl -X POST http://localhost:3002/api/automation/config/mode \
  -H "Content-Type: application/json" \
  -d '{"mode": "scheduler"}'
```

### 永久切换 (推荐)
```bash
# 修改 .env 文件
AUTOMATION_MODE=n8n  # 或 scheduler

# 重启服务
pnpm dev
```

## 🎯 选择建议

### 使用内置调度器，如果：
- ✅ 需要简单的定时执行
- ✅ 不需要复杂的流程控制
- ✅ 希望零配置启动
- ✅ 单一服务器部署

### 使用n8n编排，如果：
- ✅ 需要多种触发条件
- ✅ 需要复杂的流程控制
- ✅ 需要可视化工作流管理
- ✅ 需要与其他系统集成
- ✅ 需要不同时间调用不同功能

## 🔧 故障排除

### 调度器模式问题
```bash
# 检查调度器状态
curl http://localhost:3002/api/automation/scheduler/status

# 检查日志
tail -f logs/app.log | grep scheduler
```

### n8n模式问题
```bash
# 检查n8n状态
curl http://localhost:3002/api/n8n/status

# 验证模式
curl http://localhost:3002/api/automation/config
```

### 权限问题
确保n8n工作流有正确的API访问权限，如果设置了`N8N_API_KEY`，需要在请求头中包含：
```
Authorization: Bearer your_n8n_api_key
``` 