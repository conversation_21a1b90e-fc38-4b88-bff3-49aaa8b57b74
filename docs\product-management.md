# Product Management System

本文档介绍了如何使用新的产品管理系统来管理跨站点的产品数据。

## 系统架构

### 1. 数据结构

产品数据存储在 `src/data/products.json` 中，包含三个主要部分：

- **products**: 产品信息
- **sites**: 站点配置
- **categories**: 产品分类

### 2. 核心组件

- `src/utils/productService.ts`: 产品服务工具类
- `src/components/RelatedProducts.astro`: 重构后的相关产品组件
- `tools/product-manager.js`: 产品管理CLI工具

## 主要特性

### ✅ 跨站点支持
- 支持内部和外部链接
- 自动处理不同站点的URL
- 外部链接标识

### ✅ 可扩展性
- 支持70+产品的管理
- 分离的数据存储
- 优先级排序

### ✅ 管理工具
- CLI工具用于批量管理
- CSV导入/导出功能
- 数据验证

### ✅ 用户体验
- 响应式设计
- 延迟加载
- 分类颜色编码

## 使用方法

### 1. 在文章中添加相关产品

在你的Markdown文件frontmatter中：

```yaml
---
title: "Your Article Title"
relatedProducts: ["conference-tech", "networking-tools", "space-propulsion"]
---
```

### 2. 在页面中使用组件

```astro
---
import RelatedProducts from '../components/RelatedProducts.astro';
import { getEntry } from 'astro:content';

const entry = await getEntry('news', 'your-article-slug');
const relatedProducts = entry.data.relatedProducts || [];
---

<!-- 桌面端固定位置 -->
<RelatedProducts products={relatedProducts} position="top-right" />

<!-- 移动端底部 -->
<RelatedProducts products={relatedProducts} position="bottom" />

<!-- 侧边栏 -->
<RelatedProducts products={relatedProducts} position="sidebar" maxProducts={5} />
```

### 3. 管理产品数据

#### 使用CLI工具
```bash
node tools/product-manager.js
```

#### 手动编辑JSON文件
直接编辑 `src/data/products.json`

#### 批量导入CSV
1. 生成CSV模板：选择选项9
2. 填写产品信息
3. 导入：选择选项10

## 产品数据格式

### 产品对象
```json
{
  "id": "unique-product-id",
  "name": "Product Name",
  "description": "Product description",
  "image": "https://example.com/image.jpg",
  "category": "Technology",
  "site": "main",
  "url": "/products/product-name",
  "priority": 1
}
```

### 站点配置
```json
{
  "site-id": {
    "name": "Site Name",
    "baseUrl": "https://site.example.com",
    "isExternal": true
  }
}
```

### 分类配置
```json
{
  "category-id": {
    "name": "Category Name",
    "color": "blue"
  }
}
```

## 最佳实践

### 1. 产品ID命名
- 使用小写字母和连字符
- 保持简洁且具有描述性
- 例如：`hypersonic-systems`, `radar-detection`

### 2. 图片选择
- 使用高质量图片（推荐400x300像素）
- 确保图片加载速度
- 考虑使用CDN

### 3. 优先级设置
- 1: 最重要的产品（优先显示）
- 2: 重要产品
- 3: 一般产品

### 4. 描述撰写
- 保持简洁（建议50-100字）
- 突出核心价值
- 避免过于技术性的术语

## 高级功能

### 1. 动态产品推荐
```javascript
// 根据分类获取相关产品
const categoryProducts = productService.getProductsByCategory('Propulsion', 5);

// 搜索产品
const searchResults = productService.searchProducts('aerospace', 10);
```

### 2. 多站点URL处理
```javascript
// 获取完整URL
const fullUrl = productService.getProductUrl(product);

// 检查是否为外部链接
const isExternal = productService.isExternalProduct(product);
```

### 3. 数据验证
定期运行数据验证以确保数据完整性：
```bash
node tools/product-manager.js
# 选择选项8：Validate data
```

## 维护建议

### 1. 定期检查
- 每月验证链接有效性
- 检查图片是否正常加载
- 更新产品信息

### 2. 性能优化
- 监控组件加载时间
- 优化图片尺寸
- 考虑使用图片预加载

### 3. 分析跟踪
利用现有的Plausible Analytics跟踪：
- 产品点击率
- 热门产品分析
- 跨站点流量

## 故障排除

### 常见问题

1. **产品不显示**
   - 检查产品ID是否正确
   - 验证JSON格式
   - 确认站点配置

2. **外部链接无法打开**
   - 检查URL格式
   - 验证站点isExternal设置
   - 确认目标站点可访问

3. **图片加载失败**
   - 验证图片URL
   - 检查图片尺寸
   - 考虑使用备用图片

### 日志调试
在开发环境中启用详细日志：
```javascript
// 在productService中添加调试信息
console.log('Loading products:', productIds);
console.log('Found products:', relatedProducts);
```

## 扩展计划

### 短期计划
- [ ] 添加产品评分系统
- [ ] 实现产品收藏功能
- [ ] 添加产品比较功能

### 长期计划
- [ ] 集成CMS管理界面
- [ ] 实现实时产品数据同步
- [ ] 添加个性化推荐算法

---

如需更多帮助，请参考源代码中的注释或联系开发团队。 