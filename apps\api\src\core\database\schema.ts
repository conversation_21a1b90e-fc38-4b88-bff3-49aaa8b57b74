import { sqliteTable, text, integer, real, primaryKey } from 'drizzle-orm/sqlite-core';
import { sql } from 'drizzle-orm';

// 计划表
export const plans = sqliteTable('plans', {
  id: text('id').primaryKey(),
  date: text('date').notNull().unique(),
  status: text('status', { enum: ['draft', 'active', 'completed'] }).notNull().default('draft'),
  maxTasks: integer('max_tasks').notNull().default(10),
  categories: text('categories', { mode: 'json' }).$type<string[]>().default(['industry', 'research', 'frontier']),
  customPrompt: text('custom_prompt'),
  createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`),
  updatedAt: text('updated_at').notNull().default(sql`CURRENT_TIMESTAMP`)
});

// 任务表
export const tasks = sqliteTable('tasks', {
  id: text('id').primaryKey(),
  planId: text('plan_id').notNull().references(() => plans.id, { onDelete: 'cascade' }),
  title: text('title').notNull(),
  description: text('description'),
  keyword: text('keyword').notNull(),
  sourceUrl: text('source_url'),
  targetCategory: text('target_category', { 
    enum: ['industry', 'research', 'events', 'frontier', 'insight', 'misc'] 
  }).notNull(),
  priority: integer('priority').notNull().default(3),
  status: text('status', { 
    enum: ['pending', 'in-progress', 'completed', 'failed'] 
  }).notNull().default('pending'),
  relatedProducts: text('related_products', { mode: 'json' }).$type<string[]>().default([]),
  estimatedDuration: integer('estimated_duration').notNull().default(60), // minutes
  actualDuration: integer('actual_duration'), // minutes
  errorMessage: text('error_message'),
  createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`),
  updatedAt: text('updated_at').notNull().default(sql`CURRENT_TIMESTAMP`)
});

// 内容表
export const contents = sqliteTable('contents', {
  id: text('id').primaryKey(),
  taskId: text('task_id').notNull().references(() => tasks.id, { onDelete: 'cascade' }),
  title: text('title').notNull(),
  slug: text('slug').notNull().unique(),
  markdown: text('markdown').notNull(),
  frontMatter: text('front_matter', { mode: 'json' }).$type<Record<string, any>>().notNull(),
  filePath: text('file_path'),
  githubUrl: text('github_url'),
  wordCount: integer('word_count').notNull().default(0),
  readTime: integer('read_time').notNull().default(1), // minutes
  seoScore: real('seo_score').default(0),
  keywordDensity: real('keyword_density').default(0),
  generatedImageUrl: text('generated_image_url'),
  processingTimeMs: integer('processing_time_ms'),
  createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`),
  updatedAt: text('updated_at').notNull().default(sql`CURRENT_TIMESTAMP`)
});

// 系统配置表
export const config = sqliteTable('config', {
  key: text('key').primaryKey(),
  value: text('value', { mode: 'json' }).$type<any>().notNull(),
  description: text('description'),
  updatedAt: text('updated_at').notNull().default(sql`CURRENT_TIMESTAMP`)
});

// 日志表 
export const logs = sqliteTable('logs', {
  id: text('id').primaryKey(),
  level: text('level', { enum: ['debug', 'info', 'warn', 'error'] }).notNull(),
  message: text('message').notNull(),
  data: text('data', { mode: 'json' }).$type<any>(),
  source: text('source'), // e.g., 'api', 'agent', 'tool'
  createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`)
});

// 导出类型
export type Plan = typeof plans.$inferSelect;
export type NewPlan = typeof plans.$inferInsert;

export type Task = typeof tasks.$inferSelect;
export type NewTask = typeof tasks.$inferInsert;

export type Content = typeof contents.$inferSelect;
export type NewContent = typeof contents.$inferInsert;

export type Config = typeof config.$inferSelect;
export type NewConfig = typeof config.$inferInsert;

export type Log = typeof logs.$inferSelect;
export type NewLog = typeof logs.$inferInsert; 