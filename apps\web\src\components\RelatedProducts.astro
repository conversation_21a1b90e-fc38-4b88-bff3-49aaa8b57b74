---
import productService, { type Product } from '../utils/productService';
import serverProductService from '../utils/serverProductService';

export interface Props {
  products: string[];
  position?: 'top-right' | 'bottom' | 'sidebar';
  className?: string;
  maxProducts?: number;
}

const { 
  products = [], 
  position = 'top-right',
  className = '',
  maxProducts = 3
} = Astro.props;

// 在服务端渲染时使用服务端产品服务，在客户端使用普通产品服务
const relatedProducts = typeof window === 'undefined' 
  ? await serverProductService.getRelatedProducts(products, maxProducts)
  : productService.getRelatedProducts(products, maxProducts);

if (relatedProducts.length === 0) {
  return null;
}

const positionClasses = {
  'top-right': 'fixed top-20 right-4 w-80 z-40 hidden lg:block',
  'bottom': 'w-full mt-8',
  'sidebar': 'w-full'
};

const containerClass = `${positionClasses[position]} ${className}`;

// 选择合适的产品服务
const currentProductService = typeof window === 'undefined' ? serverProductService : productService;

// 为每个产品生成链接属性
const getProductLinkProps = (product: Product) => {
  const isExternal = currentProductService.isExternalProduct(product);
  const url = currentProductService.getProductUrl(product);
  
  return {
    href: url,
    target: isExternal ? '_blank' : '_self',
    rel: isExternal ? 'noopener noreferrer' : undefined,
    'data-external': isExternal
  };
};

// 获取分类颜色
const getCategoryColorClass = (categoryId: string) => {
  const category = currentProductService.getCategory(categoryId);
  const colorMap = {
    blue: 'bg-blue-100 text-blue-800',
    green: 'bg-green-100 text-green-800',
    purple: 'bg-purple-100 text-purple-800',
    orange: 'bg-orange-100 text-orange-800',
    indigo: 'bg-indigo-100 text-indigo-800'
  };
  
  return colorMap[category?.color as keyof typeof colorMap] || 'bg-primary-100 text-primary-800';
};
---

{relatedProducts.length > 0 && (
  <div class={containerClass}>
    <!-- Desktop: Fixed Card (top-right) -->
    {position === 'top-right' && (
      <div class="bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden">
        <div class="bg-gradient-to-r from-primary-600 to-primary-700 px-4 py-3">
          <h3 class="text-white font-semibold text-sm">Related Products</h3>
        </div>
        <div class="p-4 space-y-4 max-h-96 overflow-y-auto">
          {relatedProducts.map(product => {
            const linkProps = getProductLinkProps(product);
            const categoryClass = getCategoryColorClass(product.category);
            const site = currentProductService.getSite(product.site);
            
            return (
              <a 
                {...linkProps}
                class="block group hover:bg-gray-50 rounded-lg p-2 transition-colors"
                onclick={`plausible('Product Click', {props: {product: '${product.id}', position: '${position}', site: '${product.site}'}})`}
              >
                <div class="flex space-x-3">
                  <div class="relative">
                    <img 
                      src={product.image}
                      alt={product.name}
                      class="w-16 h-12 object-cover rounded"
                      loading="lazy"
                    />
                    {linkProps['data-external'] && (
                      <div class="absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full border border-white">
                        <svg class="w-2 h-2 text-white ml-0.5 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z"></path>
                          <path d="M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5V7h3a1 1 0 000-2H5z"></path>
                        </svg>
                      </div>
                    )}
                  </div>
                  <div class="flex-1 min-w-0">
                    <h4 class="text-sm font-medium text-gray-900 group-hover:text-primary-600 transition-colors line-clamp-2">
                      {product.name}
                    </h4>
                    <p class="text-xs text-gray-500 mt-1 line-clamp-2">
                      {product.description}
                    </p>
                    <div class="flex items-center justify-between mt-2">
                      <span class={`inline-block text-xs px-2 py-1 rounded ${categoryClass}`}>
                        {product.category}
                      </span>
                      {site && site.isExternal && (
                        <span class="text-xs text-gray-400">{site.name}</span>
                      )}
                    </div>
                  </div>
                </div>
              </a>
            );
          })}
        </div>
      </div>
    )}

    <!-- Mobile: Collapsible Banner -->
    {position === 'bottom' && (
      <div 
        class="bg-gradient-to-r from-primary-50 to-primary-100 border border-primary-200 rounded-lg overflow-hidden"
        x-data="{ expanded: false }"
      >
        <button 
          @click="expanded = !expanded"
          class="w-full px-4 py-3 flex items-center justify-between bg-primary-100 hover:bg-primary-200 transition-colors"
        >
          <span class="font-semibold text-primary-900">Related Products</span>
          <svg 
            class="w-5 h-5 text-primary-700 transform transition-transform"
            :class="{ 'rotate-180': expanded }"
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
          </svg>
        </button>
        
        <div 
          x-show="expanded"
          x-transition:enter="transition ease-out duration-200"
          x-transition:enter-start="opacity-0 max-h-0"
          x-transition:enter-end="opacity-100 max-h-96"
          x-transition:leave="transition ease-in duration-150"
          x-transition:leave-start="opacity-100 max-h-96"
          x-transition:leave-end="opacity-0 max-h-0"
          class="overflow-hidden"
        >
          <div class="p-4 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {relatedProducts.map(product => {
              const linkProps = getProductLinkProps(product);
              const categoryClass = getCategoryColorClass(product.category);
              const site = currentProductService.getSite(product.site);
              
              return (
                <a 
                  {...linkProps}
                  class="block group bg-white rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow"
                  onclick={`plausible('Product Click', {props: {product: '${product.id}', position: '${position}', site: '${product.site}'}})`}
                >
                  <div class="relative">
                    <img 
                      src={product.image}
                      alt={product.name}
                      class="w-full h-32 object-cover rounded mb-3"
                      loading="lazy"
                    />
                    {linkProps['data-external'] && (
                      <div class="absolute top-2 right-2 bg-blue-500 rounded-full p-1">
                        <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z"></path>
                          <path d="M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5V7h3a1 1 0 000-2H5z"></path>
                        </svg>
                      </div>
                    )}
                  </div>
                  <h4 class="font-semibold text-gray-900 group-hover:text-primary-600 transition-colors mb-2">
                    {product.name}
                  </h4>
                  <p class="text-sm text-gray-600 mb-3 line-clamp-2">
                    {product.description}
                  </p>
                  <div class="flex items-center justify-between">
                    <span class={`inline-block text-xs px-2 py-1 rounded ${categoryClass}`}>
                      {product.category}
                    </span>
                    {site && site.isExternal && (
                      <span class="text-xs text-gray-400">{site.name}</span>
                    )}
                  </div>
                </a>
              );
            })}
          </div>
        </div>
      </div>
    )}

    <!-- Sidebar Layout -->
    {position === 'sidebar' && (
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div class="bg-gray-50 px-4 py-3 border-b border-gray-200">
          <h3 class="font-semibold text-gray-900">Related Products</h3>
        </div>
        <div class="p-4 space-y-4">
          {relatedProducts.map(product => {
            const linkProps = getProductLinkProps(product);
            const categoryClass = getCategoryColorClass(product.category);
            const site = currentProductService.getSite(product.site);
            
            return (
              <a 
                {...linkProps}
                class="block group hover:bg-gray-50 rounded-lg p-3 transition-colors"
                onclick={`plausible('Product Click', {props: {product: '${product.id}', position: '${position}', site: '${product.site}'}})`}
              >
                <div class="flex space-x-3">
                  <div class="relative">
                    <img 
                      src={product.image}
                      alt={product.name}
                      class="w-20 h-15 object-cover rounded"
                      loading="lazy"
                    />
                    {linkProps['data-external'] && (
                      <div class="absolute -top-1 -right-1 w-4 h-4 bg-blue-500 rounded-full border border-white">
                        <svg class="w-2.5 h-2.5 text-white ml-0.5 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z"></path>
                          <path d="M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5V7h3a1 1 0 000-2H5z"></path>
                        </svg>
                      </div>
                    )}
                  </div>
                  <div class="flex-1">
                    <h4 class="font-medium text-gray-900 group-hover:text-primary-600 transition-colors mb-1">
                      {product.name}
                    </h4>
                    <p class="text-sm text-gray-600 line-clamp-2 mb-2">
                      {product.description}
                    </p>
                    <div class="flex items-center justify-between">
                      <span class={`inline-block text-xs px-2 py-1 rounded ${categoryClass}`}>
                        {product.category}
                      </span>
                      {site && site.isExternal && (
                        <span class="text-xs text-gray-400">{site.name}</span>
                      )}
                    </div>
                  </div>
                </div>
              </a>
            );
          })}
        </div>
      </div>
    )}

    <!-- JSON-LD Structured Data -->
    <script type="application/ld+json" set:html={JSON.stringify({
      "@context": "https://schema.org",
      "@type": "ItemList",
      "name": "Related Products",
      "numberOfItems": relatedProducts.length,
      "itemListElement": relatedProducts.map((product, index) => ({
        "@type": "ListItem",
        "position": index + 1,
        "item": {
          "@type": "Product",
          "@id": currentProductService.getProductUrl(product),
          "name": product.name,
          "description": product.description,
          "category": product.category,
          "image": product.image,
          "url": currentProductService.getProductUrl(product)
        }
      }))
    })} />
  </div>
)}

<style>
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style> 