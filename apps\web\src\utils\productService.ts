import productsData from '../data/products.json';
import { readFileSync, readdirSync } from 'fs';
import { join } from 'path';

export interface Product {
  id: string;
  name: string;
  description: string;
  image: string;
  category: string;
  site: string;
  url: string;
  priority: number;
  keywords?: string[];
  tags?: string[];
  useCases?: string[];
}

export interface Site {
  name: string;
  baseUrl: string;
  isExternal: boolean;
}

export interface Category {
  name: string;
  color: string;
}

export interface ProductData {
  products: Record<string, Product>;
  sites: Record<string, Site>;
  categories: Record<string, Category>;
}

export interface ExternalProductData {
  siteInfo: {
    siteName: string;
    siteBaseUrl: string;
  };
  products: Array<{
    title: string;
    description: string;
    keywords: string[];
    tags?: string[];
    useCases?: string[];
    image: string;
    url: string;
    date?: string | null;
  }>;
}

class ProductService {
  private data: ProductData;
  private externalProducts: Map<string, ExternalProductData> = new Map();

  constructor() {
    this.data = productsData as ProductData;
    this.loadExternalProducts();
  }

  /**
   * 加载外部产品数据（从/data/products目录）
   */
  private loadExternalProducts() {
    try {
      // 在浏览器环境中，我们无法直接读取文件系统
      // 这个方法主要用于服务端渲染时
      if (typeof window === 'undefined') {
        const productsDir = join(process.cwd(), '../../data/products');
        const files = readdirSync(productsDir).filter(file => file.endsWith('.json'));
        
        files.forEach(file => {
          try {
            const filePath = join(productsDir, file);
            const content = readFileSync(filePath, 'utf-8');
            const data = JSON.parse(content) as ExternalProductData;
            const siteKey = file.replace('.json', '');
            this.externalProducts.set(siteKey, data);
          } catch (error) {
            console.warn(`Failed to load product data from ${file}:`, error);
          }
        });
      }
    } catch (error) {
      console.warn('Failed to load external products:', error);
    }
  }

  /**
   * 根据产品ID获取产品信息
   */
  getProduct(productId: string): Product | null {
    return this.data.products[productId] || null;
  }

  /**
   * 根据产品ID列表获取相关产品
   */
  getRelatedProducts(productIds: string[], maxResults: number = 3): Product[] {
    const products = productIds
      .map(id => this.getProduct(id))
      .filter((product): product is Product => product !== null)
      .sort((a, b) => a.priority - b.priority)
      .slice(0, maxResults);

    return products;
  }

  /**
   * 智能产品推荐 - 根据文章内容推荐相关产品
   */
  recommendProductsForContent(
    articleContent: string, 
    articleTitle: string = '', 
    maxResults: number = 3
  ): Product[] {
    const text = (articleTitle + ' ' + articleContent).toLowerCase();
    const words = text.split(/\s+/);
    const productScores = new Map<string, number>();

    // 1. 从内部产品数据中匹配
    Object.values(this.data.products).forEach(product => {
      let score = 0;
      
      // 关键词匹配（权重最高）
      if (product.keywords) {
        product.keywords.forEach(keyword => {
          if (text.includes(keyword.toLowerCase())) {
            score += 10;
          }
        });
      }
      
      // 产品名称匹配
      const productNameWords = product.name.toLowerCase().split(/\s+/);
      productNameWords.forEach(word => {
        if (words.includes(word) && word.length > 3) {
          score += 5;
        }
      });
      
      // 描述匹配
      const descWords = product.description.toLowerCase().split(/\s+/);
      descWords.forEach(word => {
        if (words.includes(word) && word.length > 4) {
          score += 2;
        }
      });
      
      // 分类匹配
      if (text.includes(product.category.toLowerCase())) {
        score += 3;
      }
      
      if (score > 0) {
        productScores.set(product.id, score);
      }
    });

    // 2. 从外部产品数据中匹配并转换为内部格式
    this.externalProducts.forEach((siteData, siteKey) => {
      siteData.products.forEach((extProduct, index) => {
        let score = 0;
        const productId = `${siteKey}-${index}`;
        
        // 关键词匹配
        extProduct.keywords.forEach(keyword => {
          if (text.includes(keyword.toLowerCase())) {
            score += 10;
          }
        });
        
        // 标题匹配
        const titleWords = extProduct.title.toLowerCase().split(/\s+/);
        titleWords.forEach(word => {
          if (words.includes(word) && word.length > 3) {
            score += 5;
          }
        });
        
        // 描述匹配
        const descWords = extProduct.description.toLowerCase().split(/\s+/);
        descWords.forEach(word => {
          if (words.includes(word) && word.length > 4) {
            score += 2;
          }
        });
        
        if (score > 0) {
          // 创建临时产品对象
          const tempProduct: Product = {
            id: productId,
            name: extProduct.title,
            description: extProduct.description,
            image: extProduct.image,
            category: this.inferCategoryFromKeywords(extProduct.keywords),
            site: siteKey,
            url: extProduct.url,
            priority: 2, // 外部产品优先级稍低
            keywords: extProduct.keywords,
            tags: extProduct.tags,
            useCases: extProduct.useCases
          };
          
          // 临时添加到产品数据中
          this.data.products[productId] = tempProduct;
          productScores.set(productId, score);
        }
      });
    });

    // 3. 按分数排序并返回结果
    const sortedProducts = Array.from(productScores.entries())
      .sort(([, scoreA], [, scoreB]) => scoreB - scoreA)
      .slice(0, maxResults)
      .map(([productId]) => this.getProduct(productId))
      .filter((product): product is Product => product !== null);

    return sortedProducts;
  }

  /**
   * 根据关键词推断产品分类
   */
  private inferCategoryFromKeywords(keywords: string[]): string {
    const categoryMappings: Record<string, string> = {
      'actuator': 'Propulsion',
      'battery': 'Propulsion', 
      'thermal': 'Propulsion',
      'radar': 'Detection',
      'sensor': 'Detection',
      'detector': 'Detection',
      'imaging': 'Detection',
      'camera': 'Detection',
      'gyroscope': 'Navigation',
      'accelerometer': 'Navigation',
      'navigation': 'Navigation',
      'gps': 'Navigation',
      'communication': 'Communication',
      'antenna': 'Communication',
      'satellite': 'Communication'
    };

    for (const keyword of keywords) {
      const lowerKeyword = keyword.toLowerCase();
      for (const [key, category] of Object.entries(categoryMappings)) {
        if (lowerKeyword.includes(key)) {
          return category;
        }
      }
    }
    
    return 'Technology';
  }

  /**
   * 在文章内容中插入产品软链接
   */
  insertProductLinksInContent(
    content: string,
    recommendedProducts: Product[],
    maxLinks: number = 2  // 减少链接密度
  ): string {
    let modifiedContent = content;
    let linksInserted = 0;

    recommendedProducts.slice(0, maxLinks).forEach(product => {
      if (linksInserted >= maxLinks) return;

      // 寻找合适的插入点（产品名称或相关关键词）
      const insertionCandidates = [
        product.name,
        ...(product.keywords || []),
        ...(product.tags || [])
      ];

      for (const candidate of insertionCandidates) {
        if (linksInserted >= maxLinks) break;
        
        const regex = new RegExp(`\\b(${candidate.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})\\b`, 'i');
        const match = modifiedContent.match(regex);
        
        if (match && !modifiedContent.includes(`[${match[1]}]`)) {
          const productUrl = this.getProductUrl(product);
          const isExternal = this.isExternalProduct(product);
          const linkText = match[1];
          const linkMarkdown = isExternal 
            ? `[${linkText}](${productUrl}){:target="_blank" rel="noopener noreferrer"}`
            : `[${linkText}](${productUrl})`;
          
          modifiedContent = modifiedContent.replace(match[0], linkMarkdown);
          linksInserted++;
          break;
        }
      }
    });

    return modifiedContent;
  }

  /**
   * 获取站点信息
   */
  getSite(siteId: string): Site | null {
    return this.data.sites[siteId] || null;
  }

  /**
   * 获取分类信息
   */
  getCategory(categoryId: string): Category | null {
    return this.data.categories[categoryId] || null;
  }

  /**
   * 根据分类获取产品列表
   */
  getProductsByCategory(categoryId: string, maxResults: number = 10): Product[] {
    return Object.values(this.data.products)
      .filter(product => product.category === categoryId)
      .sort((a, b) => a.priority - b.priority)
      .slice(0, maxResults);
  }

  /**
   * 根据站点获取产品列表
   */
  getProductsBySite(siteId: string, maxResults: number = 10): Product[] {
    return Object.values(this.data.products)
      .filter(product => product.site === siteId)
      .sort((a, b) => a.priority - b.priority)
      .slice(0, maxResults);
  }

  /**
   * 搜索产品
   */
  searchProducts(query: string, maxResults: number = 10): Product[] {
    const lowerQuery = query.toLowerCase();
    return Object.values(this.data.products)
      .filter(product => 
        product.name.toLowerCase().includes(lowerQuery) ||
        product.description.toLowerCase().includes(lowerQuery) ||
        product.category.toLowerCase().includes(lowerQuery) ||
        (product.keywords && product.keywords.some(k => k.toLowerCase().includes(lowerQuery)))
      )
      .sort((a, b) => a.priority - b.priority)
      .slice(0, maxResults);
  }

  /**
   * 获取所有分类
   */
  getAllCategories(): Category[] {
    return Object.values(this.data.categories);
  }

  /**
   * 获取所有站点
   */
  getAllSites(): Site[] {
    return Object.values(this.data.sites);
  }

  /**
   * 生成产品的完整URL（处理内部和外部链接）
   */
  getProductUrl(product: Product): string {
    const site = this.getSite(product.site);
    if (!site) return product.url;

    if (site.isExternal) {
      return product.url; // 外部链接已经是完整URL
    } else {
      return `${site.baseUrl}${product.url}`;
    }
  }

  /**
   * 检查产品链接是否为外部链接
   */
  isExternalProduct(product: Product): boolean {
    const site = this.getSite(product.site);
    return site?.isExternal || false;
  }
}

export const productService = new ProductService();
export default productService; 