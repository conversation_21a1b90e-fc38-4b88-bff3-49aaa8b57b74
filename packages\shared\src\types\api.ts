import { z } from 'zod';

// ============= 基础类型 =============
export const TaskStatusSchema = z.enum([
  'pending',
  'in-progress', 
  'completed',
  'failed'
]);

export const PlanStatusSchema = z.enum([
  'draft',
  'active',
  'completed'
]);

export const CategorySchema = z.enum([
  'industry',
  'research', 
  'events',
  'frontier',
  'insight',
  'misc'
]);

// ============= 配置相关 =============
export const ConfigKeySchema = z.enum([
  'openai.apiKey',
  'openai.model',
  'openai.maxTokens',
  'content.outputDir',
  'content.defaultCategory',
  'content.minWordCount',
  'content.maxWordCount',
  'scheduler.enabled',
  'scheduler.interval',
  'scheduler.maxTasksPerRun',
  'image.enabled',
  'image.provider',
  'image.unsplashApiKey',
  'seo.minKeywordDensity',
  'seo.maxKeywordDensity',
  'seo.targetWordCount'
]);

export const ConfigValueSchema = z.union([
  z.string(),
  z.number(),
  z.boolean(),
  z.null()
]);

export const ConfigSchema = z.object({
  key: ConfigKeySchema,
  value: z.string(),
  description: z.string().nullable(),
  updatedAt: z.string()
});

// ============= 内容生成请求 =============
export const ContentGenerationRequestSchema = z.object({
  keyword: z.string(),
  sourceContent: z.string().optional(),
  sourceTitle: z.string().optional(),
  relatedProducts: z.array(z.string()).default([]),
  context: z.any().optional()
});

export const ContentOutputSchema = z.object({
  title: z.string(),
  slug: z.string(),
  content: z.string(),
  excerpt: z.string(),
  tags: z.array(z.string()),
  category: z.string(),
  imageUrl: z.string().optional(),
  metadata: z.object({
    wordCount: z.number(),
    readTime: z.number(),
    seoScore: z.number(),
    keywordDensity: z.number(),
    relatedProducts: z.array(z.object({
      id: z.string(),
      title: z.string(),
      description: z.string(),
      url: z.string(),
      image: z.string(),
      matchScore: z.number(),
      isRecommended: z.boolean()
    })).optional(),
    productLinksCount: z.number().optional()
  })
});

// ============= 计划主题 =============
export const PlanTopicSchema = z.object({
  title: z.string(),
  category: z.string(),
  description: z.string(),
  sources: z.array(z.string()),
  deadline: z.string()
});

export const TrendAnalysisSchema = z.object({
  trending_keywords: z.array(z.object({
    keyword: z.string(),
    growth: z.string(),
    relevance: z.string()
  })),
  seasonal_trends: z.object({
    current_season: z.string(),
    focus_areas: z.array(z.string())
  }),
  competitive_analysis: z.object({
    top_topics: z.array(z.string()),
    content_gaps: z.array(z.string())
  })
});

export const PlanDataSchema = z.object({
  id: z.string(),
  generated_at: z.string(),
  trends_analysis: TrendAnalysisSchema,
  content_tasks: z.array(z.object({
    id: z.string(),
    title: z.string(),
    category: z.string(),
    description: z.string(),
    sources: z.array(z.string()),
    deadline: z.string(),
    priority: z.enum(['high', 'medium', 'low']),
    estimated_words: z.number(),
    related_products: z.array(z.string()),
    seo_keywords: z.array(z.string()),
    status: z.string()
  })),
  next_generation: z.object({
    scheduled_for: z.string(),
    focus_areas: z.array(z.string())
  })
});

// ============= 任务相关 =============
export const TaskSchema = z.object({
  id: z.string(),
  planId: z.string(),
  title: z.string(),
  description: z.string(),
  keyword: z.string(),
  sourceUrl: z.string().url().optional(),
  targetCategory: CategorySchema,
  priority: z.number().min(1).max(5),
  status: TaskStatusSchema,
  relatedProducts: z.array(z.string()),
  estimatedDuration: z.number(), // minutes
  createdAt: z.date(),
  updatedAt: z.date()
});

export const CreateTaskDtoSchema = z.object({
  title: z.string().min(1),
  description: z.string().optional(),
  keyword: z.string().min(1),
  sourceUrl: z.string().url().optional(),
  targetCategory: CategorySchema,
  priority: z.number().min(1).max(5).default(3),
  relatedProducts: z.array(z.string()).default([]),
  estimatedDuration: z.number().default(60)
});

export const UpdateTaskDtoSchema = CreateTaskDtoSchema.partial().extend({
  status: TaskStatusSchema.optional()
});

// ============= 计划相关 =============
export const PlanSchema = z.object({
  id: z.string(),
  date: z.string(),
  tasks: z.array(TaskSchema),
  status: PlanStatusSchema,
  createdAt: z.date(),
  updatedAt: z.date()
});

export const CreatePlanDtoSchema = z.object({
  date: z.string().optional(),
  maxTasks: z.number().min(1).max(50).default(10),
  categories: z.array(CategorySchema).default(['industry', 'research', 'frontier']),
  customPrompt: z.string().optional()
});

// ============= 内容生成相关 =============
export const ContentMetadataSchema = z.object({
  wordCount: z.number(),
  readTime: z.number(), // minutes
  seoScore: z.number().min(0).max(100),
  keywordDensity: z.number()
});

export const ContentSchema = z.object({
  id: z.string(),
  taskId: z.string(),
  title: z.string(),
  slug: z.string(),
  markdown: z.string(),
  frontMatter: z.record(z.any()),
  metadata: ContentMetadataSchema,
  filePath: z.string(),
  githubUrl: z.string().url().optional()
});

export const GenerateContentDtoSchema = z.object({
  taskId: z.string(),
  customPrompt: z.string().optional(),
  targetWordCount: z.number().min(500).max(5000).default(1000),
  includeImages: z.boolean().default(true)
});

// ============= API响应类型 =============
export const ApiResponseSchema = z.object({
  success: z.boolean(),
  data: z.any().optional(),
  error: z.object({
    code: z.string(),
    message: z.string(),
    details: z.any().optional()
  }).optional(),
  meta: z.object({
    timestamp: z.string(),
    requestId: z.string().optional(),
    version: z.string().optional()
  }).optional()
});

export const PaginationSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20),
  total: z.number(),
  totalPages: z.number()
});

export const PaginatedTasksSchema = z.object({
  tasks: z.array(TaskSchema),
  pagination: PaginationSchema
});

// ============= 系统统计 =============
export const SystemStatsSchema = z.object({
  totalTasks: z.number(),
  completedTasks: z.number(),
  pendingTasks: z.number(),
  successRate: z.number(),
  avgProcessingTime: z.number(), // minutes
  lastGeneration: z.string().optional()
});

// ============= 导出类型 =============
export type TaskStatus = z.infer<typeof TaskStatusSchema>;
export type PlanStatus = z.infer<typeof PlanStatusSchema>;
export type Category = z.infer<typeof CategorySchema>;

export type Task = z.infer<typeof TaskSchema>;
export type CreateTaskDto = z.infer<typeof CreateTaskDtoSchema>;
export type UpdateTaskDto = z.infer<typeof UpdateTaskDtoSchema>;

export type Plan = z.infer<typeof PlanSchema>;
export type CreatePlanDto = z.infer<typeof CreatePlanDtoSchema>;

export type ContentMetadata = z.infer<typeof ContentMetadataSchema>;
export type Content = z.infer<typeof ContentSchema>;
export type GenerateContentDto = z.infer<typeof GenerateContentDtoSchema>;

export type ApiResponse<T = any> = z.infer<typeof ApiResponseSchema> & {
  data?: T;
};

export type Pagination = z.infer<typeof PaginationSchema>;
export type PaginatedTasks = z.infer<typeof PaginatedTasksSchema>;
export type SystemStats = z.infer<typeof SystemStatsSchema>;

// ============= 新增导出类型 =============
export type ConfigKey = z.infer<typeof ConfigKeySchema>;
export type ConfigValue = z.infer<typeof ConfigValueSchema>;
export type Config = z.infer<typeof ConfigSchema>;
export type ContentGenerationRequest = z.infer<typeof ContentGenerationRequestSchema>;
export type ContentOutput = z.infer<typeof ContentOutputSchema>;
export type PlanTopic = z.infer<typeof PlanTopicSchema>;
export type TrendAnalysis = z.infer<typeof TrendAnalysisSchema>;
export type PlanData = z.infer<typeof PlanDataSchema>; 