version: '3.8'

services:
  # 新的API服务 (Fastify)
  api:
    build:
      context: .
      dockerfile: Dockerfile.api
    ports:
      - "3002:3002"
    environment:
      - NODE_ENV=development
      - PORT=3002
      - DATABASE_URL=/app/data/database.sqlite
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - SERP_API_KEY=${SERP_API_KEY}
      - FIRECRAWL_API_KEY=${FIRECRAWL_API_KEY}
      - UNSPLASH_API_KEY=${UNSPLASH_API_KEY}
      - GITHUB_TOKEN=${GITHUB_TOKEN}
    volumes:
      - ./data:/app/data
      - ./tasks:/app/tasks
      - ./src/content:/app/content
    restart: unless-stopped
    networks:
      - news-network

  # Astro Web服务
  web:
    build:
      context: .
      dockerfile: Dockerfile.web
    ports:
      - "4321:4321"
    environment:
      - NODE_ENV=development
      - ASTRO_TELEMETRY_DISABLED=1
    volumes:
      - ./src:/app/src
      - ./public:/app/public
    restart: unless-stopped
    networks:
      - news-network
    depends_on:
      - api

  # 轻量级调度服务 (可选)
  scheduler:
    build:
      context: .
      dockerfile: Dockerfile.scheduler
    environment:
      - NODE_ENV=development
      - API_BASE_URL=http://api:3002/api/v1
      - SCHEDULE_ENABLED=${SCHEDULE_ENABLED:-true}
      - CRON_PLAN_GENERATE=${CRON_PLAN_GENERATE:-0 9 * * *}
      - CRON_CONTENT_GENERATE=${CRON_CONTENT_GENERATE:-0 */4 * * *}
    volumes:
      - ./tasks:/app/tasks
      - ./logs:/app/logs
    restart: unless-stopped
    networks:
      - news-network
    depends_on:
      - api

networks:
  news-network:
    driver: bridge

volumes:
  data:
  logs: 