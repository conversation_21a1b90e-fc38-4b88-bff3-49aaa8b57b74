#!/bin/bash

# 自动化内容生成系统设置脚本
# 用于快速设置开发环境

set -e

echo "🚀 开始设置自动化内容生成系统..."

# 检查必要的工具
check_dependencies() {
    echo "📋 检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        echo "❌ Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        echo "❌ Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    if ! command -v node &> /dev/null; then
        echo "❌ Node.js 未安装，请先安装 Node.js 18+"
        exit 1
    fi
    
    echo "✅ 依赖检查完成"
}

# 创建必要的目录
create_directories() {
    echo "📁 创建目录结构..."
    
    mkdir -p tasks
    mkdir -p data
    mkdir -p logs
    mkdir -p n8n/workflows
    mkdir -p n8n/credentials
    mkdir -p database/init
    mkdir -p monitoring
    mkdir -p nginx/conf.d
    mkdir -p nginx/ssl
    
    echo "✅ 目录创建完成"
}

# 创建环境变量文件
create_env_file() {
    if [ ! -f .env ]; then
        echo "📝 创建环境变量文件..."
        cat > .env << 'EOF'
# API Keys - 必须配置
SERP_API_KEY=your_serp_api_key_here
FIRECRAWL_API_KEY=your_firecrawl_api_key_here
OPENAI_API_KEY=your_openai_api_key_here
UNSPLASH_API_KEY=your_unsplash_api_key_here

# 服务配置
API_KEY=dev-api-key-12345
NODE_ENV=development
PORT=3000

# 数据库配置
DATABASE_URL=postgresql://postgres:password@localhost:5432/content_db
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=redis123

# n8n 配置
N8N_PASSWORD=admin123
N8N_ENCRYPTION_KEY=n8n-encryption-key-change-me

# 监控配置
GRAFANA_PASSWORD=admin123

# 文件存储配置
MINIO_USER=minioadmin
MINIO_PASSWORD=minioadmin123

# 调度配置
SCHEDULE_ENABLED=true
CRON_PLAN_GENERATE=0 9 * * *
CRON_CONTENT_GENERATE=0 */4 * * *

# 内容生成配置
MAX_KEYWORDS_PER_RUN=10
TREND_TOPICS=aerospace technology,defense systems,military technology
IMAGE_GENERATION_ENABLED=true
IMAGE_PROVIDER=unsplash
OPENAI_MODEL=gpt-4o-mini

# SEO 配置
MIN_WORD_COUNT=800
MAX_WORD_COUNT=3000
DEFAULT_CATEGORY=technology

# 速率限制
RATE_LIMIT_WINDOW=3600000
RATE_LIMIT_MAX=100
EOF
        echo "✅ 环境变量文件已创建 (.env)"
        echo "⚠️  请编辑 .env 文件，填入您的 API 密钥"
    else
        echo "✅ 环境变量文件已存在"
    fi
}

# 创建数据库初始化脚本
create_db_init() {
    echo "🗄️ 创建数据库初始化脚本..."
    
    cat > database/init/01-init.sql << 'EOF'
-- 创建 n8n 数据库
CREATE DATABASE n8n_db;

-- 创建内容数据库表
\c content_db;

CREATE TABLE IF NOT EXISTS tasks (
    id VARCHAR(255) PRIMARY KEY,
    keyword VARCHAR(500) NOT NULL,
    trend_score INTEGER,
    source VARCHAR(50) NOT NULL,
    status VARCHAR(50) NOT NULL,
    priority VARCHAR(20) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    scheduled_for TIMESTAMP,
    target_url TEXT,
    related_products TEXT[],
    retry_count INTEGER DEFAULT 0,
    failure_reason TEXT,
    output_path TEXT,
    metadata JSONB
);

CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks(status);
CREATE INDEX IF NOT EXISTS idx_tasks_created_at ON tasks(created_at);
CREATE INDEX IF NOT EXISTS idx_tasks_keyword ON tasks(keyword);

-- 创建内容哈希表
CREATE TABLE IF NOT EXISTS content_hashes (
    id SERIAL PRIMARY KEY,
    content_hash VARCHAR(64) UNIQUE NOT NULL,
    title VARCHAR(500) NOT NULL,
    keyword VARCHAR(500) NOT NULL,
    file_path TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    content_preview TEXT,
    word_count INTEGER,
    metadata JSONB
);

CREATE INDEX IF NOT EXISTS idx_content_hash ON content_hashes(content_hash);
CREATE INDEX IF NOT EXISTS idx_content_keyword ON content_hashes(keyword);
EOF

    echo "✅ 数据库初始化脚本已创建"
}

# 创建 Nginx 配置
create_nginx_config() {
    echo "🌐 创建 Nginx 配置..."
    
    cat > nginx/nginx.conf << 'EOF'
events {
    worker_connections 1024;
}

http {
    upstream content_generator {
        server content-generator:3000;
    }
    
    upstream n8n {
        server n8n:5678;
    }
    
    upstream astro {
        server astro-dev:4321;
    }
    
    upstream grafana {
        server grafana:3000;
    }

    server {
        listen 80;
        server_name localhost;

        # 内容生成 API
        location /api/ {
            proxy_pass http://content_generator;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # n8n 工作流
        location /n8n/ {
            proxy_pass http://n8n/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header Connection "upgrade";
            proxy_set_header Upgrade $http_upgrade;
        }

        # Grafana 监控
        location /monitoring/ {
            proxy_pass http://grafana/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Astro 站点 (默认)
        location / {
            proxy_pass http://astro;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
EOF

    echo "✅ Nginx 配置已创建"
}

# 创建 Prometheus 配置
create_prometheus_config() {
    echo "📊 创建 Prometheus 配置..."
    
    cat > monitoring/prometheus.yml << 'EOF'
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'content-generator'
    static_configs:
      - targets: ['content-generator:3000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
    scrape_interval: 30s

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s

  - job_name: 'n8n'
    static_configs:
      - targets: ['n8n:5678']
    scrape_interval: 30s
EOF

    echo "✅ Prometheus 配置已创建"
}

# 创建 Dockerfile
create_dockerfiles() {
    echo "🐳 创建 Dockerfile..."
    
    # 主服务 Dockerfile
    cat > Dockerfile << 'EOF'
FROM node:18-alpine

WORKDIR /app

# 安装依赖
COPY package*.json ./
RUN npm ci --only=production

# 复制源代码
COPY . .

# 创建必要目录
RUN mkdir -p /app/data /app/logs /app/tasks

# 设置权限
RUN chown -R node:node /app
USER node

EXPOSE 3000

CMD ["npm", "start"]
EOF

    # Astro Dockerfile
    cat > Dockerfile.astro << 'EOF'
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci

COPY . .

EXPOSE 4321

CMD ["npm", "run", "dev", "--", "--host", "0.0.0.0"]
EOF

    # 调度器 Dockerfile
    cat > Dockerfile.scheduler << 'EOF'
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY tools/ ./tools/
COPY services/ ./services/
COPY tasks/ ./tasks/

RUN mkdir -p /app/logs
RUN chown -R node:node /app
USER node

CMD ["node", "tools/scheduler.js"]
EOF

    echo "✅ Dockerfile 已创建"
}

# 安装依赖
install_dependencies() {
    echo "📦 安装 Node.js 依赖..."
    
    if [ ! -f package.json ]; then
        echo "❌ package.json 不存在，请确保在正确的项目目录中运行此脚本"
        exit 1
    fi
    
    npm install
    echo "✅ 依赖安装完成"
}

# 创建示例任务文件
create_sample_tasks() {
    echo "📋 创建示例任务文件..."
    
    cat > tasks/tasks.json << 'EOF'
{
  "tasks": [
    {
      "id": "sample_task_001",
      "keyword": "aerospace navigation systems",
      "trendScore": 75,
      "source": "manual",
      "status": "new",
      "priority": "normal",
      "createdAt": "2024-12-19T10:00:00Z",
      "relatedProducts": ["gyroscope-advanced", "navigation-computer"],
      "metadata": {
        "category": "technology",
        "estimatedWordCount": 1000
      }
    }
  ],
  "lastUpdated": "2024-12-19T10:00:00Z",
  "version": "1.0"
}
EOF

    echo "✅ 示例任务文件已创建"
}

# 主函数
main() {
    echo "🎯 自动化内容生成系统设置"
    echo "================================"
    
    check_dependencies
    create_directories
    create_env_file
    create_db_init
    create_nginx_config
    create_prometheus_config
    create_dockerfiles
    install_dependencies
    create_sample_tasks
    
    echo ""
    echo "🎉 设置完成！"
    echo ""
    echo "📋 下一步操作："
    echo "1. 编辑 .env 文件，填入您的 API 密钥"
    echo "2. 运行 'docker-compose up -d' 启动所有服务"
    echo "3. 访问 http://localhost 查看 Astro 站点"
    echo "4. 访问 http://localhost/n8n 配置工作流"
    echo "5. 访问 http://localhost/monitoring 查看监控"
    echo ""
    echo "🔧 常用命令："
    echo "- 启动服务: docker-compose up -d"
    echo "- 查看日志: docker-compose logs -f"
    echo "- 停止服务: docker-compose down"
    echo "- 重建服务: docker-compose up -d --build"
    echo ""
    echo "📚 更多信息请查看 docs/api_contract.md"
}

# 运行主函数
main "$@" 