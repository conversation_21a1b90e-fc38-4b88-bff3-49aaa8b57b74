{"name": "aerospace-news-platform", "type": "module", "version": "0.0.1", "private": true, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "scripts": {"dev": "turbo run dev", "dev:api": "pnpm --filter @news-site/api dev", "dev:web": "pnpm --filter @news-site/web dev", "dev:shared": "pnpm --filter @news-site/shared dev", "build": "turbo run build", "build:api": "pnpm --filter @news-site/api build", "build:web": "pnpm --filter @news-site/web build", "build:shared": "pnpm --filter @news-site/shared build", "lint": "turbo run lint", "type-check": "turbo run type-check", "clean": "turbo run clean", "install:all": "pnpm install", "start": "pnpm --filter @news-site/web dev", "preview": "pnpm --filter @news-site/web preview", "astro": "pnpm --filter @news-site/web astro", "build:skip-check": "pnpm --filter @news-site/web build", "format": "prettier --write src/", "seo:lint": "node src/seo-lint.js", "plan:generate": "pnpm --filter @news-site/api run plan:generate", "dedup:check": "node scripts/hash-dedup.cjs check", "dedup:record": "node scripts/hash-dedup.cjs record", "dedup:stats": "node scripts/hash-dedup.cjs stats", "test:automation": "node scripts/test-automation.js", "test:automation:real": "USE_MOCK=false node scripts/test-automation.js", "fix:hero-images": "node scripts/fix-hero-images.js", "test:hero-images": "node scripts/test-hero-image-validation.js"}, "dependencies": {"@types/node": "^22.15.29", "better-sqlite3": "^11.10.0", "dotenv": "^16.5.0", "express": "^5.1.0", "node-fetch": "^3.3.2", "openai": "^4.73.1"}, "devDependencies": {"@astrojs/check": "^0.9.4", "@types/alpinejs": "^3.13.10", "@types/better-sqlite3": "^7.6.13", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.57.1", "prettier": "^3.3.3", "tsx": "^4.7.0", "turbo": "^1.12.4", "typescript": "^5.7.2"}}