#!/usr/bin/env node

/**
 * 生成每日任务文件脚本
 * 为指定日期生成新闻内容任务
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

// 配置
const CONFIG = {
  tasksDir: join(projectRoot, 'tasks'),
  apiBaseUrl: 'http://localhost:3002'
};

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function success(message) { log(`✅ ${message}`, 'green'); }
function error(message) { log(`❌ ${message}`, 'red'); }
function warning(message) { log(`⚠️  ${message}`, 'yellow'); }
function info(message) { log(`ℹ️  ${message}`, 'blue'); }
function highlight(message) { log(`🔍 ${message}`, 'cyan'); }

/**
 * 生成热门关键词
 */
function generateTrendingKeywords() {
  const keywords = [
    'quantum computing defense',
    'hypersonic missile technology', 
    'space-based radar systems',
    'autonomous drone swarms',
    'next-generation fighter jets',
    'satellite constellation networks',
    'advanced thermal imaging',
    'cyber warfare defense',
    'artificial intelligence military',
    'stealth technology innovations'
  ];
  
  // 随机选择3-5个关键词
  const count = Math.floor(Math.random() * 3) + 3;
  const selected = [];
  const used = new Set();
  
  while (selected.length < count && selected.length < keywords.length) {
    const index = Math.floor(Math.random() * keywords.length);
    if (!used.has(index)) {
      selected.push(keywords[index]);
      used.add(index);
    }
  }
  
  return selected;
}

/**
 * 生成内容任务
 */
function generateContentTasks(date, keywords) {
  const categories = ['industry', 'research', 'frontier', 'insight'];
  const sources = [
    'https://defensenews.com/',
    'https://www.janes.com/',
    'https://www.aviationweek.com/',
    'https://spacenews.com/'
  ];
  
  const tasks = [];
  
  keywords.forEach((keyword, index) => {
    const category = categories[index % categories.length];
    const priority = Math.floor(Math.random() * 3) + 2; // 2-4
    const source = sources[index % sources.length];
    
    // 生成标题
    const titleTemplates = [
      `${keyword.charAt(0).toUpperCase() + keyword.slice(1)} Breakthrough in Military Applications - ${date.replace(/-/g, '/')}`,
      `Advanced ${keyword.charAt(0).toUpperCase() + keyword.slice(1)} Integration in Defense Systems - 2025`,
      `${keyword.charAt(0).toUpperCase() + keyword.slice(1)} Technology for Enhanced Military Capabilities - ${date.replace(/-/g, '/')}`
    ];
    
    const title = titleTemplates[index % titleTemplates.length];
    
    // 生成相关产品
    const relatedProducts = getRelatedProducts(keyword);
    
    const task = {
      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      keyword: keyword,
      title: title,
      category: category,
      priority: priority,
      status: 'planned',
      sources: [source],
      related_products: relatedProducts,
      created_at: new Date().toISOString(),
      scheduled_for: new Date().toISOString()
    };
    
    tasks.push(task);
  });
  
  return tasks;
}

/**
 * 获取相关产品
 */
function getRelatedProducts(keyword) {
  const productMappings = {
    'quantum': ['radar-systems'],
    'hypersonic': ['hypersonic-systems', 'radar-systems'],
    'radar': ['radar-systems'],
    'drone': ['navigation-computer'],
    'fighter': ['navigation-computer', 'radar-systems'],
    'satellite': ['satellite-communication'],
    'thermal': ['thermal-battery'],
    'cyber': ['navigation-computer'],
    'artificial intelligence': ['navigation-computer'],
    'stealth': ['radar-systems']
  };
  
  const products = [];
  
  for (const [key, productList] of Object.entries(productMappings)) {
    if (keyword.toLowerCase().includes(key)) {
      products.push(...productList);
    }
  }
  
  // 去重并限制数量
  return [...new Set(products)].slice(0, 2);
}

/**
 * 生成任务文件
 */
function generateTaskFile(date) {
  const keywords = generateTrendingKeywords();
  const contentTasks = generateContentTasks(date, keywords);
  
  const taskFile = {
    date: date,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    plan_id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    content_tasks: contentTasks
  };
  
  return taskFile;
}

/**
 * 保存任务文件
 */
function saveTaskFile(date, taskData) {
  // 确保任务目录存在
  if (!fs.existsSync(CONFIG.tasksDir)) {
    fs.mkdirSync(CONFIG.tasksDir, { recursive: true });
    info('创建任务目录');
  }
  
  const filename = `${date}.json`;
  const filepath = join(CONFIG.tasksDir, filename);
  
  // 检查文件是否已存在
  if (fs.existsSync(filepath)) {
    warning(`任务文件已存在: ${filename}`);
    const backup = `${date}-backup-${Date.now()}.json`;
    const backupPath = join(CONFIG.tasksDir, backup);
    fs.copyFileSync(filepath, backupPath);
    info(`已备份现有文件: ${backup}`);
  }
  
  // 写入新文件
  fs.writeFileSync(filepath, JSON.stringify(taskData, null, 2), 'utf8');
  success(`任务文件已保存: ${filename}`);
  
  return filepath;
}

/**
 * 主函数
 */
async function main() {
  const args = process.argv.slice(2);
  const date = args[0] || new Date().toISOString().split('T')[0];
  
  console.log('📋 生成每日任务文件');
  console.log('==================\n');
  
  highlight(`生成日期: ${date}`);
  
  try {
    // 生成任务数据
    info('生成热门关键词...');
    const taskData = generateTaskFile(date);
    
    info(`生成了 ${taskData.content_tasks.length} 个内容任务:`);
    taskData.content_tasks.forEach((task, index) => {
      console.log(`  ${index + 1}. ${task.keyword} (${task.category})`);
    });
    
    // 保存文件
    info('保存任务文件...');
    const filepath = saveTaskFile(date, taskData);
    
    // 显示摘要
    console.log('\n📊 任务文件摘要:');
    console.log(`文件路径: ${filepath}`);
    console.log(`任务数量: ${taskData.content_tasks.length}`);
    console.log(`计划ID: ${taskData.plan_id}`);
    
    console.log('\n📋 下一步建议:');
    console.log('1. 运行完整的自动化工作流测试');
    console.log('2. 检查生成的任务内容');
    console.log('3. 执行内容生成流程');
    
    success('🎉 每日任务文件生成完成！');
    
  } catch (err) {
    error(`生成失败: ${err.message}`);
    process.exit(1);
  }
}

// 运行主函数
main().catch(console.error);
