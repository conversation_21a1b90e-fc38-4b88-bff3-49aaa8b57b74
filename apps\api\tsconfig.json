{"compilerOptions": {"target": "ES2022", "module": "ES2022", "moduleResolution": "node", "outDir": "./dist", "rootDir": "./src", "composite": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "strict": false, "noImplicitAny": false, "allowJs": true, "declaration": true, "declarationMap": true, "sourceMap": true}, "include": ["src/**/*"], "exclude": ["dist", "node_modules"], "references": [{"path": "../../packages/shared"}]}