import { productDataLoader } from './productDataLoader.js';
import productService, { type Product } from './productService.js';

/**
 * 服务端产品服务
 * 在Astro构建时使用，能够加载外部产品数据
 */
class ServerProductService {
  private initialized = false;
  private externalProducts: Product[] = [];

  /**
   * 初始化服务端产品服务
   */
  async initialize() {
    if (this.initialized) return;

    try {
      // 加载外部产品数据
      productDataLoader.loadExternalProducts();
      this.externalProducts = productDataLoader.convertExternalToInternalProducts();

      console.log(`Server product service initialized with ${this.externalProducts.length} external products`);
      this.initialized = true;
    } catch (error) {
      console.warn('Failed to initialize server product service:', error);
      this.initialized = true; // 即使失败也标记为已初始化，避免重复尝试
    }
  }

  /**
   * 强制重新初始化服务（清除缓存）
   */
  async forceReinitialize() {
    this.initialized = false;
    this.externalProducts = [];

    // 清除产品数据加载器的缓存
    productDataLoader.clearCache();

    await this.initialize();
  }

  /**
   * 智能产品推荐 - 结合内部和外部产品数据
   */
  async recommendProductsForContent(
    articleContent: string, 
    articleTitle: string = '', 
    maxResults: number = 3
  ): Promise<Product[]> {
    await this.initialize();

    // 1. 从内部产品数据推荐
    const internalRecommendations = productService.recommendProductsForContent(
      articleContent,
      articleTitle,
      maxResults
    );

    // 2. 从外部产品数据推荐
    const externalRecommendations = productDataLoader.recommendProductsForContent(
      articleContent,
      articleTitle,
      maxResults
    );

    // 3. 合并并去重
    const allRecommendations = new Map<string, Product>();
    
    // 添加内部推荐（优先级更高）
    internalRecommendations.forEach(product => {
      allRecommendations.set(product.id, product);
    });

    // 添加外部推荐
    externalRecommendations.forEach(product => {
      if (!allRecommendations.has(product.id)) {
        allRecommendations.set(product.id, product);
      }
    });

    // 4. 按优先级排序并返回
    return Array.from(allRecommendations.values())
      .sort((a, b) => a.priority - b.priority)
      .slice(0, maxResults);
  }

  /**
   * 获取相关产品（兼容现有接口）
   */
  async getRelatedProducts(productIds: string[], maxResults: number = 3): Promise<Product[]> {
    await this.initialize();

    const products: Product[] = [];
    
    // 先从内部产品数据获取
    productIds.forEach(id => {
      const product = productService.getProduct(id);
      if (product) {
        products.push(product);
      }
    });

    // 如果需要更多产品，从外部产品数据补充
    if (products.length < maxResults) {
      const remainingCount = maxResults - products.length;
      const externalProductIds = productIds.filter(id => !productService.getProduct(id));
      
      externalProductIds.slice(0, remainingCount).forEach(id => {
        const externalProduct = this.externalProducts.find(p => p.id === id);
        if (externalProduct) {
          products.push(externalProduct);
        }
      });
    }

    return products
      .sort((a, b) => a.priority - b.priority)
      .slice(0, maxResults);
  }

  /**
   * 在文章内容中插入产品软链接 - 优化版本，解决分词问题
   */
  async insertProductLinksInContent(
    content: string,
    recommendedProducts: Product[],
    maxLinks: number = 2  // 减少链接密度，保持1-3个链接
  ): Promise<string> {
    await this.initialize();

    let modifiedContent = content;
    let linksInserted = 0;
    const linkedTerms = new Set<string>();
    const linkedProducts = new Set<string>(); // 跟踪已链接的产品，避免重复

    // 减少产品数量，降低链接密度
    recommendedProducts.slice(0, Math.min(maxLinks, recommendedProducts.length)).forEach(product => {
      if (linksInserted >= maxLinks) return;

      // 避免重复链接同一个产品
      if (linkedProducts.has(product.id)) return;

      // 构建候选词列表，优先长词组
      const allCandidates = [
        product.name,
        ...(product.keywords || [])
      ].filter(candidate => candidate && candidate.length > 2);

      // 按长度排序，优先匹配长词组
      const sortedCandidates = allCandidates.sort((a, b) => b.length - a.length);

      for (const candidate of sortedCandidates) {
        if (linksInserted >= maxLinks) break;

        const candidateLower = candidate.toLowerCase();

        // 跳过已链接的术语
        if (linkedTerms.has(candidateLower)) {
          continue;
        }

        // 检查是否已存在链接
        if (this.hasExistingLink(modifiedContent, candidate)) {
          continue;
        }

        // 创建更精确的正则表达式
        const escapedCandidate = candidate.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        const regex = new RegExp(`(?<!\\[)(?<!\\]\\()\\b(${escapedCandidate})\\b(?!\\])(?!\\]\\()(?![\\w\\s]*\\])`, 'i');

        const match = modifiedContent.match(regex);

        if (match) {
          const productUrl = this.getProductUrl(product);
          const linkText = match[1];
          // 外部产品链接在新窗口打开
          const linkMarkdown = `[${linkText}](${productUrl}){:target="_blank" rel="noopener noreferrer"}`;

          modifiedContent = modifiedContent.replace(match[0], linkMarkdown);

          // 记录已链接的术语，包括其子词
          linkedTerms.add(candidateLower);
          linkedProducts.add(product.id); // 记录已链接的产品

          // 防止子词被重复链接
          const words = candidateLower.split(/\s+/);
          words.forEach(word => {
            if (word.length > 3) {
              linkedTerms.add(word);
            }
          });

          linksInserted++;
          break;
        }
      }
    });

    return modifiedContent;
  }

  /**
   * 检查内容中是否已存在指定术语的链接
   */
  private hasExistingLink(content: string, term: string): boolean {
    const linkPattern = new RegExp(`\\[([^\\]]*${term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}[^\\]]*)\\]\\([^)]+\\)`, 'i');
    return linkPattern.test(content);
  }

  /**
   * 搜索产品（结合内部和外部数据）
   */
  async searchProducts(query: string, maxResults: number = 10): Promise<Product[]> {
    await this.initialize();

    // 从内部产品搜索
    const internalResults = productService.searchProducts(query, maxResults);
    
    // 从外部产品搜索
    const lowerQuery = query.toLowerCase();
    const externalResults = this.externalProducts.filter(product => 
      product.name.toLowerCase().includes(lowerQuery) ||
      product.description.toLowerCase().includes(lowerQuery) ||
      product.category.toLowerCase().includes(lowerQuery) ||
      (product.keywords && product.keywords.some(k => k.toLowerCase().includes(lowerQuery)))
    );

    // 合并结果并去重
    const allResults = new Map<string, Product>();
    
    internalResults.forEach(product => {
      allResults.set(product.id, product);
    });

    externalResults.forEach(product => {
      if (!allResults.has(product.id)) {
        allResults.set(product.id, product);
      }
    });

    return Array.from(allResults.values())
      .sort((a, b) => a.priority - b.priority)
      .slice(0, maxResults);
  }

  /**
   * 获取所有产品（内部+外部）
   */
  async getAllProducts(): Promise<Product[]> {
    await this.initialize();
    
    const internalProducts = Object.values(productService['data'].products);
    return [...internalProducts, ...this.externalProducts];
  }

  /**
   * 获取产品 - 优先使用外部产品数据
   */
  getProduct(productId: string): Product | null {
    // 优先从外部产品数据查找
    const externalProduct = this.externalProducts.find(p => p.id === productId);
    if (externalProduct) return externalProduct;

    // 如果外部没找到，再从内部查找
    return productService.getProduct(productId);
  }

  getSite(siteId: string) {
    return productService.getSite(siteId);
  }

  getCategory(categoryId: string) {
    return productService.getCategory(categoryId);
  }

  getProductsByCategory(categoryId: string, maxResults: number = 10): Product[] {
    return productService.getProductsByCategory(categoryId, maxResults);
  }

  getProductsBySite(siteId: string, maxResults: number = 10): Product[] {
    return productService.getProductsBySite(siteId, maxResults);
  }

  getAllCategories() {
    return productService.getAllCategories();
  }

  getAllSites() {
    return productService.getAllSites();
  }

  getProductUrl(product: Product): string {
    return productService.getProductUrl(product);
  }

  isExternalProduct(product: Product): boolean {
    return productService.isExternalProduct(product);
  }
}

// 导出单例实例
export const serverProductService = new ServerProductService();
export default serverProductService; 