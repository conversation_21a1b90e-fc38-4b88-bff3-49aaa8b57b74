import type { FastifyInstance } from 'fastify';

export default async function taskRoutes(fastify: FastifyInstance) {
  // GET /api/v1/tasks - 获取任务列表
  fastify.get('/', async (request, reply) => {
    return {
      success: true,
      data: [],
      message: '任务列表获取成功 (临时响应)'
    };
  });

  // POST /api/v1/tasks - 创建新任务
  fastify.post('/', async (request, reply) => {
    return {
      success: true,
      data: { id: 'temp-task-id' },
      message: '任务创建成功 (临时响应)'
    };
  });

  // PUT /api/v1/tasks/:id - 更新任务
  fastify.put('/:id', async (request, reply) => {
    const { id } = request.params as { id: string };
    return {
      success: true,
      data: { id },
      message: '任务更新成功 (临时响应)'
    };
  });

  // DELETE /api/v1/tasks/:id - 删除任务
  fastify.delete('/:id', async (request, reply) => {
    const { id } = request.params as { id: string };
    return {
      success: true,
      message: '任务删除成功 (临时响应)'
    };
  });
} 