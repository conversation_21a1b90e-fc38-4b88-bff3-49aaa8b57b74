#!/bin/bash

echo "🚀 Ubuntu 系统构建脚本"
echo "========================"

# 检查环境
echo "📋 检查环境..."
node --version
pnpm --version

# 设置错误时退出
set -e

# 清理旧的构建文件
echo "🧹 清理旧的构建文件..."
rm -rf packages/shared/dist
rm -rf apps/api/dist

# 安装依赖（如果需要）
if [ ! -d "node_modules" ]; then
    echo "📦 安装根目录依赖..."
    pnpm install
fi

# 构建 shared 包
echo "🏗️  构建 shared 包..."
cd packages/shared
if [ ! -d "node_modules" ]; then
    pnpm install
fi
pnpm run build

# 验证 shared 包构建结果
if [ ! -f "dist/index.js" ] || [ ! -f "dist/index.d.ts" ]; then
    echo "❌ shared 包构建失败"
    exit 1
fi
echo "✅ shared 包构建成功"

# 返回根目录
cd ../..

# 构建 API
echo "🏗️  构建 API..."
cd apps/api
if [ ! -d "node_modules" ]; then
    pnpm install
fi
pnpm run build

# 验证 API 构建结果
if [ ! -f "dist/index.js" ]; then
    echo "❌ API 构建失败 - 缺少 dist/index.js"
    exit 1
fi
echo "✅ API 构建成功"

# 返回根目录
cd ../..

echo "🎉 构建完成！"
echo ""
echo "启动命令："
echo "cd apps/api && pnpm start"
echo "或者："
echo "cd apps/api && node dist/index.js"
