# 智能产品推荐系统

本文档介绍了新闻站的智能产品推荐系统，该系统能够根据文章内容自动推荐相关产品，并在内容中自然地插入产品软链接。

## 🎯 功能概述

### 核心功能
- **智能产品匹配**: 根据文章标题和内容，自动从产品数据库中匹配最相关的产品
- **软链接插入**: 在文章内容中自然地插入产品链接，不影响阅读体验
- **多数据源支持**: 支持从 `/data/products` 目录加载各个产品站的JSON数据
- **实时推荐**: 在内容生成过程中实时推荐相关产品

### 技术特点
- **语义匹配**: 基于关键词、产品名称、描述等多维度匹配
- **权重算法**: 不同匹配类型有不同的权重分数
- **去重机制**: 自动去除重复推荐
- **优先级排序**: 按产品优先级和匹配分数排序

## 📁 数据结构

### 产品数据格式

每个产品站的JSON文件应遵循以下格式：

```json
{
  "siteInfo": {
    "siteName": "GNC Tech",
    "siteBaseUrl": "https://gnc-tech.com"
  },
  "products": [
    {
      "title": "High-Precision Linear Electric Actuator",
      "description": "Advanced linear electric actuator system with high precision control",
      "keywords": ["actuator", "linear", "electric", "precision", "control"],
      "tags": ["aerospace", "defense", "automation"],
      "useCases": ["missile guidance", "aircraft control", "precision positioning"],
      "image": "https://gnc-tech.com/images/products/actuator.webp",
      "url": "https://gnc-tech.com/products/linear-actuator-xz60d200",
      "date": "2024-01-15"
    }
  ]
}
```

### 数据字段说明

| 字段 | 类型 | 必需 | 说明 |
|------|------|------|------|
| `siteInfo.siteName` | string | ✅ | 产品站名称 |
| `siteInfo.siteBaseUrl` | string | ✅ | 产品站基础URL |
| `products[].title` | string | ✅ | 产品标题 |
| `products[].description` | string | ✅ | 产品描述 |
| `products[].keywords` | string[] | ✅ | 关键词数组 |
| `products[].tags` | string[] | ❌ | 标签数组 |
| `products[].useCases` | string[] | ❌ | 使用场景数组 |
| `products[].image` | string | ✅ | 产品图片URL |
| `products[].url` | string | ✅ | 产品页面URL |
| `products[].date` | string | ❌ | 产品发布日期 |

## 🔧 系统架构

### 组件结构

```
智能产品推荐系统
├── 数据加载层
│   ├── ProductDataLoader (服务端)
│   └── 外部产品数据 (/data/products/*.json)
├── 推荐引擎
│   ├── 语义匹配算法
│   ├── 权重计算
│   └── 结果排序
├── 软链接插入
│   ├── 内容分析
│   ├── 链接生成
│   └── 自然插入
└── 集成接口
    ├── 内容生成API
    ├── Web组件
    └── 服务端渲染
```

### 匹配算法

推荐系统使用多维度匹配算法：

1. **关键词匹配** (权重: 10分)
   - 精确匹配文章内容中的产品关键词
   - 最高优先级匹配

2. **产品名称匹配** (权重: 5分)
   - 匹配产品名称中的单词
   - 要求单词长度 > 3个字符

3. **描述匹配** (权重: 2分)
   - 匹配产品描述中的单词
   - 要求单词长度 > 4个字符

4. **分类匹配** (权重: 3分)
   - 匹配产品分类

5. **用例匹配** (权重: 3分)
   - 匹配产品使用场景

## 🚀 使用方法

### 1. 添加产品数据

在 `/data/products` 目录下创建新的JSON文件：

```bash
# 创建新的产品站数据
touch data/products/your-site.json
```

### 2. 在内容生成中使用

系统会自动在内容生成过程中调用推荐功能：

```javascript
// API调用示例
const response = await fetch('/api/v1/content/generate', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    taskId: 'task_001',
    keyword: 'thermal battery technology',
    sourceContent: '...',
    relatedProducts: [], // 可选：手动指定产品
    generateImage: true
  })
});
```

### 3. 在Astro组件中使用

```astro
---
import RelatedProducts from '../components/RelatedProducts.astro';
import { getEntry } from 'astro:content';

const entry = await getEntry('news', 'article-slug');
const relatedProducts = entry.data.relatedProducts || [];
---

<RelatedProducts 
  products={relatedProducts} 
  position="top-right" 
  maxProducts={3} 
/>
```

## 📊 性能优化

### 缓存策略
- **数据加载缓存**: 产品数据在首次加载后缓存在内存中
- **推荐结果缓存**: 相同内容的推荐结果可以缓存
- **单例模式**: 使用单例模式避免重复初始化

### 性能指标
- **数据加载时间**: < 100ms
- **推荐计算时间**: < 50ms
- **软链接插入时间**: < 20ms

## 🧪 测试

### 运行测试脚本

```bash
# 测试智能产品推荐功能
node scripts/test-product-recommendation.js
```

### 测试覆盖范围
- ✅ 产品数据加载
- ✅ 智能推荐算法
- ✅ 软链接插入
- ✅ 多数据源支持
- ✅ 错误处理

## 🔍 故障排除

### 常见问题

1. **产品数据未加载**
   ```
   问题: Products directory not found
   解决: 确保 /data/products 目录存在且包含JSON文件
   ```

2. **推荐结果为空**
   ```
   问题: 没有匹配的产品
   解决: 检查产品关键词是否与文章内容相关
   ```

3. **软链接未插入**
   ```
   问题: 内容中没有找到匹配的关键词
   解决: 确保产品关键词在文章内容中出现
   ```

### 调试模式

启用详细日志：

```javascript
// 在内容生成时查看推荐日志
console.log('Product recommendations:', recommendations);
console.log('Content with links:', contentWithLinks);
```

## 📈 扩展功能

### 计划中的功能
- **机器学习优化**: 基于用户点击数据优化推荐算法
- **A/B测试**: 测试不同推荐策略的效果
- **实时更新**: 支持产品数据的实时更新
- **多语言支持**: 支持多语言产品推荐

### 自定义扩展

可以通过以下方式扩展系统：

1. **自定义匹配算法**
2. **添加新的数据源**
3. **自定义权重配置**
4. **集成外部API**

## 📝 最佳实践

### 产品数据管理
- **关键词优化**: 使用准确、相关的关键词
- **描述质量**: 编写清晰、详细的产品描述
- **定期更新**: 保持产品数据的时效性
- **数据验证**: 定期验证数据格式和完整性

### 内容优化
- **关键词密度**: 在文章中自然地使用相关关键词
- **语义相关性**: 确保文章内容与推荐产品相关
- **链接质量**: 避免过度插入链接影响阅读体验

### 监控和分析
- **推荐准确性**: 监控推荐结果的相关性
- **点击率**: 跟踪产品链接的点击率
- **用户体验**: 收集用户对推荐质量的反馈

## 🔗 相关文档

- [产品管理系统](./product-management.md)
- [API文档](./api-documentation.md)
- [设计文档](./design.md)

---

*最后更新: 2024年12月* 