import { FastifyInstance } from 'fastify';
import { planService } from '../core/services/plan-service.js';
import { contentService } from '../core/services/content-service.js';
import { linkDiscoveryTool } from '../core/tools/link-discovery-tool.js';
import { createImageService } from '../core/tools/image/image-service.js';
import { deploymentTool } from '../core/tools/deployment-tool.js';
import { heroImageValidator } from '../core/tools/hero-image-validator.js';
import { schedulerService } from '../core/services/scheduler-service.js';
import { automationConfigService } from '../core/services/automation-config-service.js';
import { logger } from '../utils/logger.js';

export default async function automationRoutes(fastify: FastifyInstance) {
  // 初始化图片服务
  const imageService = createImageService((level, message, data) => {
    logger[level as keyof typeof logger](message, data);
  });

  // 获取链接发现工具状态
  fastify.get('/automation/status', async (request, reply) => {
    try {
      const status = linkDiscoveryTool.getStatus();
      return {
        success: true,
        data: status
      };
    } catch (error) {
      logger.error('Failed to get automation status:', error);
      return reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // 测试链接发现功能
  fastify.post<{
    Body: { keyword: string; maxUrls?: number }
  }>('/automation/test-discovery', async (request, reply) => {
    try {
      const { keyword, maxUrls = 5 } = request.body;
      
      if (!keyword) {
        return reply.status(400).send({
          success: false,
          error: 'Keyword is required'
        });
      }

      logger.info(`Testing link discovery for keyword: ${keyword}`);
      const results = await linkDiscoveryTool.discoverLinks(keyword);
      
      return {
        success: true,
        data: {
          keyword,
          resultsCount: results.length,
          results: results.slice(0, maxUrls)
        }
      };
    } catch (error) {
      logger.error('Link discovery test failed:', error);
      return reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // 测试基于关键词的内容生成
  fastify.post<{
    Body: { keyword: string; relatedProducts?: string[] }
  }>('/automation/test-content', async (request, reply) => {
    try {
      const { keyword, relatedProducts = [] } = request.body;
      
      if (!keyword) {
        return reply.status(400).send({
          success: false,
          error: 'Keyword is required'
        });
      }

      logger.info(`Testing content generation for keyword: ${keyword}`);
      const content = await contentService.generateFromKeyword(keyword, relatedProducts);
      
      return {
        success: true,
        data: content
      };
    } catch (error) {
      logger.error('Content generation test failed:', error);
      return reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // 创建并执行完整的自动化计划
  fastify.post<{
    Body: { 
      date?: string;
      maxTasks?: number;
      categories?: string[];
      executeImmediately?: boolean;
    }
  }>('/automation/run-full-cycle', async (request, reply) => {
    try {
      const { 
        date = new Date().toISOString().split('T')[0],
        maxTasks = 3,
        categories = ['industry', 'research', 'frontier'],
        executeImmediately = true
      } = request.body;

      logger.info(`Starting full automation cycle for date: ${date}`);

      // 1. 创建计划
      const plan = await planService.createPlan({
        date,
        maxTasks,
        categories,
        forceRegenerate: true
      });

      logger.info(`Plan created with ${plan.tasks.length} tasks`);

      let executionResults = null;

      // 2. 如果需要立即执行
      if (executeImmediately) {
        logger.info('Executing plan immediately...');
        executionResults = await planService.executePlan(plan.id, 2); // 最多2个并发任务
      }

      return {
        success: true,
        data: {
          plan,
          execution: executionResults
        }
      };
    } catch (error) {
      logger.error('Full automation cycle failed:', error);
      return reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // 执行特定计划
  fastify.post<{
    Params: { planId: string };
    Body: { maxConcurrent?: number }
  }>('/automation/execute-plan/:planId', async (request, reply) => {
    try {
      const { planId } = request.params;
      const { maxConcurrent = 2 } = request.body;

      logger.info(`Executing plan: ${planId}`);
      const results = await planService.executePlan(planId, maxConcurrent);

      return {
        success: true,
        data: results
      };
    } catch (error) {
      logger.error('Plan execution failed:', error);
      return reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // 执行特定任务
  fastify.post<{
    Params: { taskId: string }
  }>('/automation/execute-task/:taskId', async (request, reply) => {
    try {
      const { taskId } = request.params;

      logger.info(`Executing task: ${taskId}`);
      const result = await planService.executeTask(taskId);

      return {
        success: true,
        data: result
      };
    } catch (error) {
      logger.error('Task execution failed:', error);
      return reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // 获取计划列表
  fastify.get('/automation/plans', async (request, reply) => {
    try {
      const plans = await planService.getPlans();
      return {
        success: true,
        data: plans
      };
    } catch (error) {
      logger.error('Failed to get plans:', error);
      return reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // 获取特定计划详情
  fastify.get<{
    Params: { planId: string }
  }>('/automation/plans/:planId', async (request, reply) => {
    try {
      const { planId } = request.params;
      const plan = await planService.getPlanById(planId);
      
      return {
        success: true,
        data: plan
      };
    } catch (error) {
      logger.error('Failed to get plan:', error);
      return reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // 测试图片生成功能
  fastify.post<{
    Body: { 
      keyword?: string; 
      title?: string; 
      category?: string;
      prompt?: string;
    }
  }>('/automation/test-image', async (request, reply) => {
    try {
      const { keyword, title, category, prompt } = request.body;
      
      if (!keyword && !title && !prompt) {
        return reply.status(400).send({
          success: false,
          error: 'At least one of keyword, title, or prompt is required'
        });
      }

      logger.info(`Testing image generation`, { keyword, title, category, prompt });
      
      const result = await imageService.generateImage({
        keyword,
        title,
        category,
        prompt,
        orientation: 'landscape',
        dimensions: { width: 1200, height: 675 }
      });

      return {
        success: true,
        data: result
      };
    } catch (error) {
      logger.error('Image generation test failed:', error);
      return reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // 获取图片服务健康状态
  fastify.get('/automation/image-health', async (request, reply) => {
    try {
      const healthStatus = await imageService.healthCheck();
      return {
        success: true,
        data: healthStatus
      };
    } catch (error) {
      logger.error('Image service health check failed:', error);
      return reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // 获取图片服务统计信息
  fastify.get('/automation/image-stats', async (request, reply) => {
    try {
      const stats = imageService.getStats();
      return {
        success: true,
        data: stats
      };
    } catch (error) {
      logger.error('Failed to get image service stats:', error);
      return reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // 验证和修复所有文章的heroImage
  fastify.post<{
    Body: { 
      validateUrls?: boolean;
      dryRun?: boolean;
    }
  }>('/automation/validate-hero-images', async (request, reply) => {
    try {
      const { validateUrls = true, dryRun = false } = request.body;

      logger.info('Starting hero image validation', { validateUrls, dryRun });

      // 如果是dry run，创建一个临时的validator实例，不修改文件
      const validationResult = await heroImageValidator.validateAndFix();

      if (validationResult.success) {
        logger.info('Hero image validation completed successfully', {
          totalFiles: validationResult.totalFiles,
          fixedFiles: validationResult.fixedFiles,
          validFiles: validationResult.validFiles
        });
      } else {
        logger.warn('Hero image validation completed with errors', {
          errors: validationResult.errors.length
        });
      }

      return {
        success: true,
        data: validationResult
      };
    } catch (error) {
      logger.error('Hero image validation failed:', error);
      return reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // 获取heroImage验证器日志
  fastify.get('/automation/hero-image-logs', async (request, reply) => {
    try {
      const logs = heroImageValidator.getLogs();
      
      return {
        success: true,
        data: {
          logs,
          count: logs.length
        }
      };
    } catch (error) {
      logger.error('Failed to get hero image validator logs:', error);
      return reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // 部署到Vercel
  fastify.post<{
    Body: { 
      environment?: 'production' | 'preview';
      skipChecks?: boolean;
    }
  }>('/automation/deploy', async (request, reply) => {
    try {
      const { environment = 'production', skipChecks = false } = request.body;

      logger.info(`Starting deployment to ${environment}`, { skipChecks });

      const result = await deploymentTool.deploy();

      if (result.success) {
        logger.info('Deployment completed successfully', {
          url: result.deploymentUrl,
          buildTime: result.buildTime,
          deployTime: result.deployTime
        });
      } else {
        logger.error('Deployment failed', { error: result.error });
      }

      return {
        success: result.success,
        data: result
      };
    } catch (error) {
      logger.error('Deployment process failed:', error);
      return reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // 获取部署状态
  fastify.get<{
    Querystring: { url: string }
  }>('/automation/deploy-status', async (request, reply) => {
    try {
      const { url } = request.query;
      
      if (!url) {
        return reply.status(400).send({
          success: false,
          error: 'Deployment URL is required'
        });
      }

      const status = await deploymentTool.getDeploymentStatus(url);
      
      return {
        success: true,
        data: status
      };
    } catch (error) {
      logger.error('Failed to get deployment status:', error);
      return reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // 获取部署日志
  fastify.get('/automation/deploy-logs', async (request, reply) => {
    try {
      const logs = deploymentTool.getLogs();
      
      return {
        success: true,
        data: {
          logs,
          count: logs.length
        }
      };
    } catch (error) {
      logger.error('Failed to get deployment logs:', error);
      return reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // 完整的自动化流程：生成内容 + 部署
  fastify.post<{
    Body: { 
      date?: string;
      maxTasks?: number;
      categories?: string[];
      autoDeploy?: boolean;
      deployEnvironment?: 'production' | 'preview';
    }
  }>('/automation/full-pipeline', async (request, reply) => {
    try {
      const { 
        date = new Date().toISOString().split('T')[0],
        maxTasks = 3,
        categories = ['industry', 'research', 'frontier'],
        autoDeploy = true,
        deployEnvironment = 'production'
      } = request.body;

      logger.info(`Starting full automation pipeline for date: ${date}`, {
        maxTasks,
        categories,
        autoDeploy,
        deployEnvironment
      });

      // 1. 创建并执行计划
      const plan = await planService.createPlan({
        date,
        maxTasks,
        categories,
        forceRegenerate: true
      });

      logger.info(`Plan created with ${plan.tasks.length} tasks`);

      const executionResults = await planService.executePlan(plan.id, 2);
      logger.info('Plan execution completed', { 
        completed: executionResults.completed,
        failed: executionResults.failed 
      });

      let deploymentResult = null;

      // 2. 如果有成功的任务且启用自动部署
      if (autoDeploy && executionResults.completed > 0) {
        logger.info('Starting automatic deployment...');
        deploymentResult = await deploymentTool.deploy();
        
        if (deploymentResult.success) {
          logger.info('Automatic deployment completed', {
            url: deploymentResult.deploymentUrl
          });
        } else {
          logger.error('Automatic deployment failed', {
            error: deploymentResult.error
          });
        }
      }

      return {
        success: true,
        data: {
          plan,
          execution: executionResults,
          deployment: deploymentResult
        }
      };
    } catch (error) {
      logger.error('Full automation pipeline failed:', error);
      return reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // 调度器管理端点
  
  // 获取所有调度任务状态
  fastify.get('/automation/scheduler/status', async (request, reply) => {
    try {
      const status = schedulerService.getJobStatus();
      return {
        success: true,
        data: status
      };
    } catch (error) {
      logger.error('Failed to get scheduler status:', error);
      return reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // 创建新的调度任务
  fastify.post<{
    Body: {
      id: string;
      enabled: boolean;
      cronExpression: string;
      maxTasks?: number;
      categories?: string[];
      autoDeploy?: boolean;
      deployEnvironment?: 'production' | 'preview';
      timezone?: string;
    }
  }>('/automation/scheduler/jobs', async (request, reply) => {
    try {
      const {
        id,
        enabled,
        cronExpression,
        maxTasks = 3,
        categories = ['industry', 'research', 'frontier'],
        autoDeploy = true,
        deployEnvironment = 'production',
        timezone = 'Asia/Shanghai'
      } = request.body;

      if (!id || !cronExpression) {
        return reply.status(400).send({
          success: false,
          error: 'Job ID and cron expression are required'
        });
      }

      const job = schedulerService.createJob(id, {
        enabled,
        cronExpression,
        maxTasks,
        categories,
        autoDeploy,
        deployEnvironment,
        timezone
      });

      logger.info(`Scheduler job created: ${id}`, { job });

      return {
        success: true,
        data: job
      };
    } catch (error) {
      logger.error('Failed to create scheduler job:', error);
      return reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // 启动调度任务
  fastify.post<{
    Params: { jobId: string }
  }>('/automation/scheduler/jobs/:jobId/start', async (request, reply) => {
    try {
      const { jobId } = request.params;
      const success = schedulerService.startJob(jobId);

      if (success) {
        logger.info(`Scheduler job started: ${jobId}`);
        return {
          success: true,
          message: `Job ${jobId} started successfully`
        };
      } else {
        return reply.status(404).send({
          success: false,
          error: `Job ${jobId} not found or cannot be started`
        });
      }
    } catch (error) {
      logger.error('Failed to start scheduler job:', error);
      return reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // 停止调度任务
  fastify.post<{
    Params: { jobId: string }
  }>('/automation/scheduler/jobs/:jobId/stop', async (request, reply) => {
    try {
      const { jobId } = request.params;
      const success = schedulerService.stopJob(jobId);

      if (success) {
        logger.info(`Scheduler job stopped: ${jobId}`);
        return {
          success: true,
          message: `Job ${jobId} stopped successfully`
        };
      } else {
        return reply.status(404).send({
          success: false,
          error: `Job ${jobId} not found`
        });
      }
    } catch (error) {
      logger.error('Failed to stop scheduler job:', error);
      return reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // 手动触发调度任务
  fastify.post<{
    Params: { jobId: string }
  }>('/automation/scheduler/jobs/:jobId/trigger', async (request, reply) => {
    try {
      const { jobId } = request.params;
      
      logger.info(`Manually triggering scheduler job: ${jobId}`);
      const result = await schedulerService.triggerJob(jobId);

      return {
        success: true,
        data: result
      };
    } catch (error) {
      logger.error('Failed to trigger scheduler job:', error);
      return reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // 删除调度任务
  fastify.delete<{
    Params: { jobId: string }
  }>('/automation/scheduler/jobs/:jobId', async (request, reply) => {
    try {
      const { jobId } = request.params;
      const success = schedulerService.deleteJob(jobId);

      if (success) {
        logger.info(`Scheduler job deleted: ${jobId}`);
        return {
          success: true,
          message: `Job ${jobId} deleted successfully`
        };
      } else {
        return reply.status(404).send({
          success: false,
          error: `Job ${jobId} not found`
        });
      }
    } catch (error) {
      logger.error('Failed to delete scheduler job:', error);
      return reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // 获取特定调度任务详情
  fastify.get<{
    Params: { jobId: string }
  }>('/automation/scheduler/jobs/:jobId', async (request, reply) => {
    try {
      const { jobId } = request.params;
      const job = schedulerService.getJob(jobId);

      if (job) {
        return {
          success: true,
          data: job
        };
      } else {
        return reply.status(404).send({
          success: false,
          error: `Job ${jobId} not found`
        });
      }
    } catch (error) {
      logger.error('Failed to get scheduler job:', error);
      return reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // === 自动化模式管理 ===

  // 获取自动化配置
  fastify.get('/automation/config', async (request, reply) => {
    try {
      const config = automationConfigService.getConfig();
      const validation = automationConfigService.validateN8nConfig();
      
      return {
        success: true,
        data: {
          ...config,
          validation
        }
      };
    } catch (error) {
      logger.error('Failed to get automation config:', error);
      return reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // 切换自动化模式
  fastify.post<{
    Body: {
      mode: 'scheduler' | 'n8n';
      n8nWebhookUrl?: string;
      n8nApiKey?: string;
    }
  }>('/automation/config/mode', async (request, reply) => {
    try {
      const { mode, n8nWebhookUrl, n8nApiKey } = request.body;

      if (!mode || !['scheduler', 'n8n'].includes(mode)) {
        return reply.status(400).send({
          success: false,
          error: 'Invalid mode. Must be "scheduler" or "n8n"'
        });
      }

      // 更新配置
      automationConfigService.updateConfig({
        mode,
        schedulerEnabled: mode === 'scheduler',
        n8nWebhookUrl,
        n8nApiKey
      });

      // 验证配置
      const validation = automationConfigService.validateN8nConfig();
      if (!validation.valid) {
        return reply.status(400).send({
          success: false,
          error: validation.message
        });
      }

      logger.info(`Automation mode switched to: ${mode}`);

      return {
        success: true,
        data: {
          mode,
          message: `Automation mode switched to ${mode}`,
          note: mode === 'scheduler' 
            ? 'Scheduler will be active on next restart' 
            : 'N8N routes are now available'
        }
      };
    } catch (error) {
      logger.error('Failed to switch automation mode:', error);
      return reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // 获取自动化状态概览
  fastify.get('/automation/overview', async (request, reply) => {
    try {
      const config = automationConfigService.getConfig();
      const schedulerStatus = schedulerService.getJobStatus();
      const imageHealthCheck = await imageService.healthCheck();

      return {
        success: true,
        data: {
          mode: config.mode,
          scheduler: {
            enabled: config.schedulerEnabled,
            status: schedulerStatus
          },
          n8n: {
            enabled: config.mode === 'n8n',
            validation: automationConfigService.validateN8nConfig(),
            config: automationConfigService.getN8nConfig()
          },
          services: {
            imageService: imageHealthCheck,
            deploymentTool: true, // 简化检查
            linkDiscovery: linkDiscoveryTool.getStatus()
          }
        }
      };
    } catch (error) {
      logger.error('Failed to get automation overview:', error);
      return reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });
} 