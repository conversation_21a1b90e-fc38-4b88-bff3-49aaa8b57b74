import { readFileSync, readdirSync, existsSync } from 'fs';
import { join } from 'path';

export interface ExternalProductData {
  siteInfo: {
    siteName: string;
    siteBaseUrl: string;
  };
  products: Array<{
    title: string;
    model?: string; // 新增：原始型号
    description: string;
    keywords: string[];
    tags?: string[];
    useCases?: string[];
    image: string;
    url: string;
    date?: string | null;
  }>;
}

export interface Product {
  id: string;
  name: string;
  model?: string; // 新增：原始型号
  description: string;
  image: string;
  category: string;
  site: string;
  url: string;
  priority: number;
  keywords?: string[];
  tags?: string[];
  useCases?: string[];
}

/**
 * 服务端产品数据加载器
 * 用于在构建时或服务端渲染时加载外部产品数据
 */
export class ProductDataLoader {
  private static instance: ProductDataLoader;
  private externalProducts: Map<string, ExternalProductData> = new Map();
  private loaded = false;

  private constructor() {}

  static getInstance(): ProductDataLoader {
    if (!ProductDataLoader.instance) {
      ProductDataLoader.instance = new ProductDataLoader();
    }
    return ProductDataLoader.instance;
  }

  /**
   * 加载所有外部产品数据
   */
  loadExternalProducts(): Map<string, ExternalProductData> {
    if (this.loaded) {
      return this.externalProducts;
    }

    try {
      // 尝试不同的路径，适应不同的运行环境
      const possiblePaths = [
        join(process.cwd(), 'data/products'),
        join(process.cwd(), '../../data/products'),
        join(process.cwd(), '../../../data/products')
      ];

      let productsDir = '';
      for (const path of possiblePaths) {
        if (existsSync(path)) {
          productsDir = path;
          break;
        }
      }

      if (!productsDir) {
        console.warn('Products directory not found in any of the expected locations');
        return this.externalProducts;
      }

      const files = readdirSync(productsDir).filter(file => file.endsWith('.json'));
      
      files.forEach(file => {
        try {
          const filePath = join(productsDir, file);
          const content = readFileSync(filePath, 'utf-8');
          const data = JSON.parse(content) as ExternalProductData;
          const siteKey = file.replace('.json', '');
          
          // 验证数据结构
          if (this.validateProductData(data)) {
            this.externalProducts.set(siteKey, data);
            console.log(`Loaded ${data.products.length} products from ${file}`);
          } else {
            console.warn(`Invalid product data structure in ${file}`);
          }
        } catch (error) {
          console.warn(`Failed to load product data from ${file}:`, error);
        }
      });

      this.loaded = true;
      console.log(`Total external product sources loaded: ${this.externalProducts.size}`);
    } catch (error) {
      console.warn('Failed to load external products:', error);
    }

    return this.externalProducts;
  }

  /**
   * 验证产品数据结构
   */
  private validateProductData(data: any): data is ExternalProductData {
    return (
      data &&
      typeof data === 'object' &&
      data.siteInfo &&
      typeof data.siteInfo.siteName === 'string' &&
      typeof data.siteInfo.siteBaseUrl === 'string' &&
      Array.isArray(data.products) &&
      data.products.every((product: any) => 
        typeof product.title === 'string' &&
        typeof product.description === 'string' &&
        Array.isArray(product.keywords) &&
        typeof product.image === 'string' &&
        typeof product.url === 'string'
      )
    );
  }

  /**
   * 获取特定站点的产品数据
   */
  getSiteProducts(siteKey: string): ExternalProductData | undefined {
    if (!this.loaded) {
      this.loadExternalProducts();
    }
    return this.externalProducts.get(siteKey);
  }

  /**
   * 获取所有外部产品数据
   */
  getAllExternalProducts(): Map<string, ExternalProductData> {
    if (!this.loaded) {
      this.loadExternalProducts();
    }
    return this.externalProducts;
  }

  /**
   * 将外部产品转换为内部产品格式
   */
  convertExternalToInternalProducts(): Product[] {
    const products: Product[] = [];
    
    this.externalProducts.forEach((siteData, siteKey) => {
      siteData.products.forEach((extProduct, index) => {
        const product: Product = {
          id: `${siteKey}-${index}`,
          name: extProduct.title,
          model: extProduct.model, // 新增：保存原始型号
          description: extProduct.description,
          image: extProduct.image,
          category: this.inferCategoryFromKeywords(extProduct.keywords),
          site: siteKey,
          url: extProduct.url,
          priority: 2, // 外部产品优先级稍低
          keywords: extProduct.keywords,
          tags: extProduct.tags,
          useCases: extProduct.useCases
        };
        products.push(product);
      });
    });

    return products;
  }

  /**
   * 根据关键词推断产品分类
   */
  private inferCategoryFromKeywords(keywords: string[]): string {
    const categoryMappings: Record<string, string> = {
      'actuator': 'Propulsion',
      'battery': 'Propulsion', 
      'thermal': 'Propulsion',
      'radar': 'Detection',
      'sensor': 'Detection',
      'detector': 'Detection',
      'imaging': 'Detection',
      'camera': 'Detection',
      'cmos': 'Detection',
      'infrared': 'Detection',
      'quadrant': 'Detection',
      'gyroscope': 'Navigation',
      'accelerometer': 'Navigation',
      'navigation': 'Navigation',
      'gps': 'Navigation',
      'imu': 'Navigation',
      'fiber optic': 'Navigation',
      'communication': 'Communication',
      'antenna': 'Communication',
      'satellite': 'Communication'
    };

    for (const keyword of keywords) {
      const lowerKeyword = keyword.toLowerCase();
      for (const [key, category] of Object.entries(categoryMappings)) {
        if (lowerKeyword.includes(key)) {
          return category;
        }
      }
    }
    
    return 'Technology';
  }

  /**
   * 智能产品推荐 - 根据文章内容推荐相关产品
   */
  recommendProductsForContent(
    articleContent: string, 
    articleTitle: string = '', 
    maxResults: number = 3
  ): Product[] {
    const text = (articleTitle + ' ' + articleContent).toLowerCase();
    const words = text.split(/\s+/);
    const productScores = new Map<string, number>();

    // 从外部产品数据中匹配
    this.externalProducts.forEach((siteData, siteKey) => {
      siteData.products.forEach((extProduct, index) => {
        let score = 0;
        const productId = `${siteKey}-${index}`;
        
        // 关键词匹配（权重最高）
        extProduct.keywords.forEach(keyword => {
          if (text.includes(keyword.toLowerCase())) {
            score += 10;
          }
        });
        
        // 标题匹配
        const titleWords = extProduct.title.toLowerCase().split(/\s+/);
        titleWords.forEach(word => {
          if (words.includes(word) && word.length > 3) {
            score += 5;
          }
        });
        
        // 描述匹配
        const descWords = extProduct.description.toLowerCase().split(/\s+/);
        descWords.forEach(word => {
          if (words.includes(word) && word.length > 4) {
            score += 2;
          }
        });

        // 用例匹配
        if (extProduct.useCases) {
          extProduct.useCases.forEach(useCase => {
            if (text.includes(useCase.toLowerCase())) {
              score += 3;
            }
          });
        }
        
        if (score > 0) {
          productScores.set(productId, score);
        }
      });
    });

    // 按分数排序并转换为产品对象
    const sortedProducts = Array.from(productScores.entries())
      .sort(([, scoreA], [, scoreB]) => scoreB - scoreA)
      .slice(0, maxResults)
      .map(([productId]) => {
        const [siteKey, indexStr] = productId.split('-');
        const index = parseInt(indexStr);
        const siteData = this.externalProducts.get(siteKey);
        
        if (siteData && siteData.products[index]) {
          const extProduct = siteData.products[index];
          return {
            id: productId,
            name: extProduct.title,
            model: extProduct.model, // 新增：保存原始型号
            description: extProduct.description,
            image: extProduct.image,
            category: this.inferCategoryFromKeywords(extProduct.keywords),
            site: siteKey,
            url: extProduct.url,
            priority: 2,
            keywords: extProduct.keywords,
            tags: extProduct.tags,
            useCases: extProduct.useCases
          } as Product;
        }
        return null;
      })
      .filter((product): product is Product => product !== null);

    return sortedProducts;
  }

  /**
   * 在文章内容中插入产品软链接 - 优化版本，解决分词问题
   */
  insertProductLinksInContent(
    content: string,
    recommendedProducts: Product[],
    maxLinks: number = 2  // 减少链接密度，保持1-3个链接
  ): string {
    let modifiedContent = content;
    let linksInserted = 0;
    const linkedTerms = new Set<string>(); // 跟踪已链接的术语，避免重复

    // 减少产品数量，降低链接密度
    recommendedProducts.slice(0, Math.min(maxLinks, recommendedProducts.length)).forEach(product => {
      if (linksInserted >= maxLinks) return;

      // 构建候选词列表，优先长词组（解决分词问题）
      const allCandidates = [
        product.name,
        ...(product.keywords || [])
      ].filter(candidate => candidate && candidate.length > 2);

      // 按长度排序，优先匹配长词组
      const sortedCandidates = allCandidates.sort((a, b) => b.length - a.length);

      for (const candidate of sortedCandidates) {
        if (linksInserted >= maxLinks) break;

        const candidateLower = candidate.toLowerCase();

        // 跳过已链接的术语
        if (linkedTerms.has(candidateLower)) {
          continue;
        }

        // 检查是否已经存在链接
        if (this.hasExistingLink(modifiedContent, candidate)) {
          continue;
        }

        // 创建更精确的正则表达式，优先匹配完整词组
        const escapedCandidate = candidate.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

        // 使用词边界确保完整匹配，避免部分匹配
        const regex = new RegExp(`(?<!\\[)(?<!\\]\\()\\b(${escapedCandidate})\\b(?!\\])(?!\\]\\()(?![\\w\\s]*\\])`, 'i');

        const match = modifiedContent.match(regex);

        if (match) {
          const linkText = match[1];
          const linkMarkdown = `[${linkText}](${product.url})`;

          // 只替换第一个匹配项
          modifiedContent = modifiedContent.replace(match[0], linkMarkdown);

          // 记录已链接的术语，包括其子词
          linkedTerms.add(candidateLower);

          // 防止子词被重复链接
          const words = candidateLower.split(/\s+/);
          words.forEach(word => {
            if (word.length > 3) {
              linkedTerms.add(word);
            }
          });

          linksInserted++;
          break;
        }
      }
    });

    return modifiedContent;
  }

  /**
   * 检查内容中是否已存在指定术语的链接
   */
  private hasExistingLink(content: string, term: string): boolean {
    const linkPattern = new RegExp(`\\[([^\\]]*${term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}[^\\]]*)\\]\\([^)]+\\)`, 'i');
    return linkPattern.test(content);
  }
}

// 导出单例实例
export const productDataLoader = ProductDataLoader.getInstance(); 