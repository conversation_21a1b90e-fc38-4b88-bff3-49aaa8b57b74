/**
 * 统一的frontmatter格式化工具
 * 确保所有脚本都使用相同的YAML格式标准
 */

export interface FrontmatterData {
  [key: string]: any;
}

/**
 * 格式化单个frontmatter字段
 * 遵循Astro/YAML标准：
 * - 字符串：加引号
 * - 数字/布尔值：不加引号  
 * - 数组：[\"item1\", \"item2\"]格式
 * - 日期：不加引号的YYYY-MM-DD格式
 */
export function formatFrontmatterField(key: string, value: any): string {
  if (Array.isArray(value)) {
    // 特殊处理productRecommendations - 只保存产品ID
    if (key === 'productRecommendations') {
      const productIds = value.map(item => {
        if (typeof item === 'string') return item;
        if (typeof item === 'object' && item !== null && item.id) return item.id;
        return String(item);
      });
      return `${key}: [${productIds.map(id => `"${String(id).replace(/"/g, '\\"')}"`).join(', ')}]`;
    }
    // 数组格式：["item1", "item2"]
    return `${key}: [${value.map(v => typeof v === 'string' ? `"${v.replace(/"/g, '\\"')}"` : v).join(', ')}]`;
  } else if (key === 'date' && (typeof value === 'string' || typeof value === 'number')) {
    // 日期：不加引号，确保是YYYY-MM-DD格式
    const dateStr = typeof value === 'string' ? value : new Date(value).toISOString().split('T')[0];
    return `${key}: ${dateStr}`;
  } else if (typeof value === 'boolean') {
    // 布尔值：不加引号
    return `${key}: ${value}`;
  } else if (typeof value === 'number') {
    // 数字：不加引号
    return `${key}: ${value}`;
  } else if (typeof value === 'string') {
    // 字符串：加引号，转义内部引号
    return `${key}: "${value.replace(/"/g, '\\"')}"`;
  } else if (typeof value === 'object' && value !== null) {
    // 嵌套对象：递归处理
    const nestedFields = Object.entries(value)
      .map(([k, v]) => `  ${k}: "${String(v).replace(/"/g, '\\"')}"`)
      .join('\n');
    return `${key}:\n${nestedFields}`;
  } else {
    // 其他值：转换为字符串并加引号
    return `${key}: "${String(value).replace(/"/g, '\\"')}"`;
  }
}

/**
 * 生成完整的frontmatter
 * @param frontmatter frontmatter数据对象
 * @param fieldOrder 字段顺序（可选）
 */
export function generateFrontmatter(
  frontmatter: FrontmatterData, 
  fieldOrder: string[] = []
): string {
  const lines = ['---'];
  
  // 定义默认字段顺序
  const defaultFieldOrder = [
    'title', 'description', 'date', 'category', 'tags', 'author', 
    'featured', 'draft', 'heroImage', 'relatedProducts', 'lang', 
    'readTime', 'summary', 'seo'
  ];
  
  const order = fieldOrder.length > 0 ? fieldOrder : defaultFieldOrder;
  
  // 创建frontmatter副本以避免修改原对象
  const frontmatterCopy = { ...frontmatter };
  
  // 按顺序添加字段
  for (const field of order) {
    if (frontmatterCopy.hasOwnProperty(field)) {
      const value = frontmatterCopy[field];
      // 跳过undefined、null或空字符串值
      if (value !== undefined && value !== null && value !== '' && value !== 'undefined') {
        lines.push(formatFrontmatterField(field, value));
      }
      delete frontmatterCopy[field];
    }
  }
  
  // 添加剩余字段
  for (const [key, value] of Object.entries(frontmatterCopy)) {
    // 跳过undefined、null或空字符串值
    if (value !== undefined && value !== null && value !== '' && value !== 'undefined') {
      lines.push(formatFrontmatterField(key, value));
    }
  }
  
  lines.push('---');
  return lines.join('\n');
}

/**
 * 解析frontmatter
 * @param content markdown文件内容
 */
export function parseFrontmatter(content: string): {
  frontmatter: FrontmatterData;
  content: string;
} {
  const frontmatterMatch = content.match(/^---\n([\s\S]*?)\n---/);
  if (!frontmatterMatch) {
    return { frontmatter: {}, content: content };
  }

  const frontmatterText = frontmatterMatch[1];
  const remainingContent = content.substring(frontmatterMatch[0].length);

  const lines = frontmatterText.split('\n');
  const frontmatter: FrontmatterData = {};
  
  for (const line of lines) {
    const trimmedLine = line.trim();
    
    // 跳过空行和无效的分隔符（如 ------）
    if (!trimmedLine || trimmedLine.match(/^-+$/) || trimmedLine === '------') {
      continue;
    }
    
    const match = trimmedLine.match(/^(\w+):\s*(.*)$/);
    if (match) {
      const [, key, value] = match;
      
      // 如果key已存在，使用最后一个值（覆盖重复字段）
      if (value.startsWith('"') && value.endsWith('"')) {
        frontmatter[key] = value.slice(1, -1);
      } else if (value.startsWith('[') && value.endsWith(']')) {
        frontmatter[key] = value;
      } else if (value.toLowerCase() === 'true') {
        frontmatter[key] = true;
      } else if (value.toLowerCase() === 'false') {
        frontmatter[key] = false;
      } else if (!isNaN(Number(value)) && value !== '' && value !== 'undefined') {
        frontmatter[key] = Number(value);
      } else if (value === 'undefined' || value.trim() === '') {
        // 跳过undefined或空值
        continue;
      } else {
        frontmatter[key] = value;
      }
    }
  }

  return { frontmatter, content: remainingContent };
}

/**
 * 验证frontmatter格式是否正确
 * @param frontmatter frontmatter数据
 */
export function validateFrontmatter(frontmatter: FrontmatterData): {
  valid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  
  // 检查必需字段
  const requiredFields = ['title', 'description', 'date', 'category'];
  for (const field of requiredFields) {
    if (!frontmatter[field]) {
      errors.push(`缺少必需字段: ${field}`);
    }
  }
  
  // 检查字段类型
  if (frontmatter.featured !== undefined && typeof frontmatter.featured !== 'boolean') {
    errors.push('featured字段必须是布尔值');
  }
  
  if (frontmatter.draft !== undefined && typeof frontmatter.draft !== 'boolean') {
    errors.push('draft字段必须是布尔值');
  }
  
  if (frontmatter.readTime !== undefined && typeof frontmatter.readTime !== 'number') {
    errors.push('readTime字段必须是数字');
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
} 