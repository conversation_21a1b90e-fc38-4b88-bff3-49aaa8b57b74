---
import { type CollectionEntry } from 'astro:content';
import BaseLayout from './BaseLayout.astro';
import Header from '../components/Header.astro';
import Footer from '../components/Footer.astro';
import ProductLinkPreview from '../components/ProductLinkPreview.astro';
import RelatedArticles from '../components/RelatedArticles.astro';

export interface Props {
  post: CollectionEntry<'news'>;
}

const { post } = Astro.props;
const { title, date: rawDate, category, tags, description, author, relatedProducts } = post.data;
const heroImage = post.data.heroImage;

// 统一日期处理 - 项目标准：frontmatter使用 `date: 2025-06-09` (无引号)
// Astro content collection通过 z.string().transform((str) => new Date(str)) 自动转换
// 这确保了rawDate通常已经是正确的Date对象
const date = (() => {
  // 标准情况：Astro content collection已将字符串转换为Date对象
  if (rawDate instanceof Date && !isNaN(rawDate.getTime())) {
    return rawDate;
  }
  
  // 向后兼容：处理可能的字符串格式（手动导入或旧数据）
  if (typeof rawDate === 'string') {
    // 处理标准YYYY-MM-DD格式，添加T12:00:00避免时区偏移
    if (/^\d{4}-\d{2}-\d{2}$/.test(rawDate)) {
      return new Date(`${rawDate}T12:00:00`);
    }
    // 处理其他ISO日期格式
    const parsed = new Date(rawDate);
    if (!isNaN(parsed.getTime())) {
      return parsed;
    }
  }
  
  // 异常情况：无效日期，使用当前日期并记录警告
  console.warn('Invalid date format in post:', rawDate, '- using current date as fallback');
  return new Date();
})();

// Generate JSON-LD structured data
const jsonLd = {
  "@context": "https://schema.org",
  "@type": "Article",
  "headline": title,
  "description": description || title,
  "image": heroImage ? new URL(heroImage, Astro.site).toString() : undefined,
  "datePublished": date.toISOString(),
  "dateModified": date.toISOString(),
  "author": {
    "@type": "Person",
    "name": author
  },
  "publisher": {
    "@type": "Organization",
    "name": "Aerospace & Defense News",
    "logo": {
      "@type": "ImageObject",
      "url": new URL("/logo.png", Astro.site).toString()
    }
  },
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": new URL(Astro.url.pathname, Astro.site).toString()
  },
  "articleSection": category,
  "keywords": tags?.join(", ") || ""
};

const articleStructuredData = {
  "@context": "https://schema.org",
  "@type": "NewsArticle",
  "headline": title,
  "description": description || `Read about ${title} in our latest aerospace and defense industry coverage.`,
  "image": heroImage ? [
    new URL(heroImage, Astro.site).toString()
  ] : [
    new URL("/og-default.jpg", Astro.site).toString()
  ],
  "datePublished": date.toISOString(),
  "dateModified": date.toISOString(),
  "author": {
    "@type": "Organization",
    "name": author || "Technical Editorial Team",
    "url": new URL("/", Astro.site).toString()
  },
  "publisher": {
    "@type": "Organization",
    "name": "Aerospace & Defense News",
    "url": new URL("/", Astro.site).toString(),
    "logo": {
      "@type": "ImageObject",
      "url": new URL("/logo.png", Astro.site).toString()
    }
  },
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": new URL(Astro.url.pathname, Astro.site).toString()
  },
  "articleSection": category,
  "keywords": tags?.join(", ") || "",
  "about": [
    {
      "@type": "Thing",
      "name": "Aerospace Technology"
    },
    {
      "@type": "Thing", 
      "name": "Defense Industry"
    }
  ],
  "genre": "Technology News"
};

// Additional Product Schema for related products
const productSchemas = relatedProducts && relatedProducts.length > 0 ? 
  relatedProducts.slice(0, 3).map((productName: string) => ({
    "@context": "https://schema.org",
    "@type": "Product",
    "name": productName,
    "category": "Aerospace Technology",
    "manufacturer": {
      "@type": "Organization",
      "name": "GNC Technology"
    }
  })) : [];
---

<BaseLayout 
  title={`${title} | Aerospace & Defense News`}
  description={description || `Read about ${title} in our latest aerospace and defense industry coverage.`}
  image={heroImage}
>
  <Header />
  
  <!-- 产品链接悬停预览功能 -->
  <ProductLinkPreview />

  <main class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Breadcrumb -->
    <nav class="mb-8" aria-label="Breadcrumb">
      <ol class="flex items-center space-x-2 text-sm text-gray-500">
        <li><a href="/" class="hover:text-primary-600">Home</a></li>
        <li><span class="mx-2">/</span></li>
        <li><a href={`/category/${category}`} class="hover:text-primary-600 capitalize">{category}</a></li>
        <li><span class="mx-2">/</span></li>
        <li class="text-gray-900 truncate" aria-current="page">{title}</li>
      </ol>
    </nav>

    <!-- Article Header -->
    <header class="mb-8">
      {heroImage && (
        <div class="aspect-w-16 aspect-h-9 mb-6 rounded-lg overflow-hidden">
          <img 
            src={heroImage}
            alt={title}
            class="w-full h-64 md:h-96 object-cover"
            loading="eager"
          />
        </div>
      )}
      
      <div class="space-y-4">
        <!-- Category and Date -->
        <div class="flex items-center space-x-4 text-sm">
          <span class="bg-primary-100 text-primary-800 font-medium px-3 py-1 rounded-full">
            {category}
          </span>
          <time datetime={date.toISOString()} class="text-gray-500">
            {date.toLocaleDateString('en-US', { 
              year: 'numeric', 
              month: 'long', 
              day: 'numeric' 
            })}
          </time>
          <span class="text-gray-300">•</span>
          <span class="text-gray-500">By {author}</span>
        </div>

        <!-- Tags -->
        {tags.length > 0 && (
          <div class="flex flex-wrap gap-2 pt-4">
            {tags.map(tag => (
              <span class="bg-gray-100 text-gray-700 text-sm px-3 py-1 rounded-full">
                #{tag}
              </span>
            ))}
          </div>
        )}
      </div>
    </header>

    <!-- Article Content -->
    <article class="prose prose-lg prose-gray max-w-none prose-headings:text-gray-900 prose-a:text-primary-600 prose-a:no-underline hover:prose-a:underline prose-img:rounded-lg prose-img:shadow-md">
      <slot />
    </article>

    <!-- 相关文章推荐 -->
    <RelatedArticles currentPost={post} limit={3} />

    <!-- Article Navigation -->
    <nav class="flex justify-between items-center mt-12 pt-8 border-t border-gray-200">
      <div class="flex space-x-4">
        <a 
          href={`/category/${category}`}
          class="text-primary-600 hover:text-primary-700 font-medium flex items-center"
        >
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
          More {category} articles
        </a>
        
        <a 
          href="/archive"
          class="text-primary-600 hover:text-primary-700 font-medium flex items-center"
        >
          All articles
          <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7-7 7" />
          </svg>
        </a>
      </div>
    </nav>
  </main>

  <Footer />

  <!-- JSON-LD Structured Data -->
  <script type="application/ld+json" set:html={JSON.stringify(jsonLd)}></script>
  <script type="application/ld+json" set:html={JSON.stringify(articleStructuredData)}></script>
  <script type="application/ld+json" set:html={JSON.stringify(productSchemas)}></script>

  <!-- Analytics Page View -->
  <script define:vars={{ postId: post.id, category }}>
    // Track page view with article details
    plausible('pageview', {
      props: {
        article: postId,
        category: category
      }
    });
  </script>

  <!-- Auto-open external links in new tab -->
  <script>
    // Wait for DOM to be ready
    document.addEventListener('DOMContentLoaded', function() {
      // Get all links in the article content
      const article = document.querySelector('article.prose');
      if (article) {
        const links = article.querySelectorAll('a[href]');
        
        for (const link of links) {
          const href = link.getAttribute('href');
          
          // Check if it's an external link (starts with http:// or https://)
          if (href && (href.startsWith('http://') || href.startsWith('https://'))) {
            // Don't open internal links in new tab
            if (!href.includes(window.location.hostname)) {
              link.setAttribute('target', '_blank');
              link.setAttribute('rel', 'noopener noreferrer');
              
              // Optional: Add visual indicator for external links
              if (!link.querySelector('.external-link-icon')) {
                const icon = document.createElement('span');
                icon.className = 'external-link-icon ml-1 inline-block w-3 h-3 opacity-60';
                icon.innerHTML = '↗';
                icon.title = 'Opens in new tab';
                link.appendChild(icon);
              }
            }
          }
        }
      }
    });
  </script>
</BaseLayout>

<style>
  .aspect-w-16 {
    position: relative;
    padding-bottom: calc(9 / 16 * 100%);
  }
  
  .aspect-w-16 img {
    position: absolute;
    height: 100%;
    width: 100%;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    object-fit: cover;
  }

  /* Custom prose styles */
  .prose h2 {
    @apply text-2xl font-bold mt-8 mb-4;
  }
  
  .prose h3 {
    @apply text-xl font-semibold mt-6 mb-3;
  }
  
  .prose p {
    @apply mb-4 leading-relaxed;
  }
  
  .prose blockquote {
    @apply border-l-4 border-primary-500 pl-6 py-2 my-6 bg-gray-50 rounded-r;
  }
  
  .prose ul, .prose ol {
    @apply mb-4;
  }
  
  .prose li {
    @apply mb-2;
  }
</style> 