#!/usr/bin/env node

/**
 * 完整的自动化写作流程测试和修复脚本
 * 参考test-automation-base.js的正确模式
 * 
 * 解决的主要问题：
 * 1. 标题重复问题 - 修复文章中的重复标题
 * 2. 任务文件每日生成 - 确保每天生成对应的任务文件到/tasks目录
 * 3. 配图逻辑优先级 - 验证worker->fal->unsplash的配图优先级
 * 4. 产品推荐匹配 - 确保从/products目录精确匹配相关产品
 * 5. 产品链接嵌入 - 验证AI改写时自然嵌入产品链接
 * 6. heroImage死链检查和修复 - 检查并修复死链图片
 * 7. 完整的端到端自动化流程测试
 */

import { config } from 'dotenv';
import { join, resolve } from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import fetch from 'node-fetch';
import { spawn } from 'child_process';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// 获取项目根目录路径
const projectRoot = resolve(__dirname, '../');

// 加载环境变量
config({ path: join(projectRoot, '.env.local') });
config({ path: join(projectRoot, '.env') });

const API_BASE = 'http://localhost:3002';
const USE_MOCK = process.env.USE_MOCK !== 'false';

// 自动化配置
const AUTOMATION_CONFIG = {
  // 测试模式：2分钟后启动，然后每20分钟执行一次
  startDelay: 2 * 60 * 1000,  // 2分钟后启动，方便观察
  interval: 20 * 60 * 1000,   // 20分钟执行一次
  
  maxTasksPerRun: USE_MOCK ? 2 : 3,
  categories: ['research', 'industry', 'frontier'],
  enableImageGeneration: true,  // 启用图片生成（现在使用简化的Worker服务）
  enableAsyncImageGeneration: false,  // 已移除：异步图片生成模式
  enableImageReplacement: false,  // 已移除：构建前图片替换
  enableAstroBuild: true,
  enableVercelDeploy: true,
  
  // 新增配置
  enableTitleDuplicationFix: true,
  enableTaskFileGeneration: true,
  enableProductLinkInsertion: true,
  enableHeroImageValidation: true,
}

// 颜色输出工具
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  magenta: '\x1b[35m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function success(message) { log(`✅ ${message}`, 'green'); }
function error(message) { log(`❌ ${message}`, 'red'); }
function info(message) { log(`ℹ️  ${message}`, 'blue'); }
function warn(message) { log(`⚠️  ${message}`, 'yellow'); }
function highlight(message) { log(`🔥 ${message}`, 'magenta'); }

// API请求工具
async function apiRequest(endpoint, options = {}) {
  const url = `${API_BASE}${endpoint}`;
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    });
    
    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(`API Error: ${data.error || response.statusText}`);
    }
    
    return data;
  } catch (err) {
    throw new Error(`Request failed: ${err.message}`);
  }
}

// ==================== 问题修复功能 ====================

/**
 * 修复标题重复问题
 */
async function fixTitleDuplication() {
  info('🔧 检查并修复标题重复问题...');
  
  const newsDir = join(projectRoot, 'apps/web/src/content/news');
  
  if (!fs.existsSync(newsDir)) {
    warn('新闻目录不存在，跳过标题重复修复');
    return { fixed: 0, total: 0 };
  }
  
  let totalFiles = 0;
  let fixedFiles = 0;
  
  function scanDirectory(dir) {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        scanDirectory(fullPath);
      } else if (item.endsWith('.md')) {
        totalFiles++;
        
        try {
          const content = fs.readFileSync(fullPath, 'utf8');
          const lines = content.split('\n');
          
          // 查找frontmatter中的title
          let frontmatterTitle = null;
          let inFrontmatter = false;
          let frontmatterEnd = -1;
          
          for (let i = 0; i < lines.length; i++) {
            if (lines[i].trim() === '---') {
              if (!inFrontmatter) {
                inFrontmatter = true;
              } else {
                frontmatterEnd = i;
                break;
              }
            } else if (inFrontmatter && lines[i].startsWith('title:')) {
              // 解析title，处理引号
              const titleLine = lines[i].substring(6).trim();
              frontmatterTitle = titleLine.replace(/^["']|["']$/g, '');
            }
          }
          
          if (frontmatterTitle && frontmatterEnd > -1) {
            // 检查content部分是否有重复的标题
            const contentLines = lines.slice(frontmatterEnd + 1);
            let hasUpdate = false;
            
            for (let i = 0; i < contentLines.length; i++) {
              const line = contentLines[i].trim();
              
              // 检查是否是重复的一级标题
              if (line.startsWith('# ') && line.substring(2).trim() === frontmatterTitle) {
                // 删除这一行
                contentLines.splice(i, 1);
                hasUpdate = true;
                break; // 只删除第一个重复标题
              }
            }
            
            if (hasUpdate) {
              // 重新组合文件内容
              const newContent = [
                ...lines.slice(0, frontmatterEnd + 1),
                ...contentLines
              ].join('\n');
              
              fs.writeFileSync(fullPath, newContent, 'utf8');
              fixedFiles++;
              
              info(`  修复: ${item}`);
            }
          }
          
        } catch (err) {
          warn(`  处理文件失败: ${item} - ${err.message}`);
        }
      }
    }
  }
  
  scanDirectory(newsDir);
  
  success(`标题重复修复完成: ${fixedFiles}/${totalFiles} 个文件需要修复`);
  return { fixed: fixedFiles, total: totalFiles };
}

/**
 * 异步图片生成 - 使用新的异步图片服务
 */
async function startAsyncImageGeneration(tasks) {
  if (!AUTOMATION_CONFIG.enableAsyncImageGeneration) {
    return { started: 0, imageJobs: [] };
  }

  info('🖼️ 启动异步图片生成任务...');
  const imageJobs = [];
  let startedCount = 0;

  for (const task of tasks) {
    try {
      // 使用新的异步图片生成API
      const response = await apiRequest('/api/async-image/start', {
        method: 'POST',
        body: JSON.stringify({
          taskId: task.id,
          keyword: task.keyword,
          title: task.title,
          category: task.category
        })
      });

      if (response.success && response.data?.jobId) {
        imageJobs.push({
          jobId: response.data.jobId,
          taskId: task.id,
          keyword: task.keyword,
          title: task.title,
          startTime: Date.now()
        });
        startedCount++;
        info(`  🚀 已启动图片生成: ${task.title.substring(0, 50)}... (Job: ${response.data.jobId})`);
      } else {
        warn(`  ⚠️ 启动图片生成失败: ${task.title} - ${response.error || 'Unknown error'}`);
      }

      // 避免同时启动太多请求
      await new Promise(resolve => setTimeout(resolve, 500));

    } catch (err) {
      warn(`  ⚠️ 启动图片生成失败: ${task.title} - ${err.message}`);
    }
  }

  info(`✅ 异步图片生成已启动: ${startedCount} 个任务`);
  return { started: startedCount, imageJobs };
}

/**
 * 图片验证和替换 - 在Astro构建前执行，支持健壮的降级策略
 */
async function validateAndReplaceImages(imageJobs) {
  if (!AUTOMATION_CONFIG.enableImageReplacement || !imageJobs || imageJobs.length === 0) {
    return { replaced: 0, failed: 0 };
  }

  info('🔍 验证和替换生成的图片...');
  let replacedCount = 0;
  let failedCount = 0;

  // 等待所有图片生成完成（最多等待12分钟，考虑Worker的5分钟超时）
  const timeout = 12 * 60 * 1000; // 12分钟
  const startTime = Date.now();

  try {
    const results = await Promise.allSettled(
      imageJobs.map(job =>
        Promise.race([
          job,
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Image generation timeout')), timeout)
          )
        ])
      )
    );

    for (const [index, result] of results.entries()) {
      if (result.status === 'fulfilled' && result.value.result?.success) {
        const { taskId, result: imageResult } = result.value;

        try {
          // 查找对应的markdown文件并替换heroImage
          const newsDir = join(projectRoot, 'apps/web/src/content/news');
          const files = fs.readdirSync(newsDir, { recursive: true });
          const mdFiles = files.filter(f => f.endsWith('.md') && f.includes(taskId.split('-')[0]));

          for (const file of mdFiles) {
            const filePath = join(newsDir, file);
            let content = fs.readFileSync(filePath, 'utf8');

            // 验证图片URL有效性
            let imageUrl = imageResult.data?.url;
            let provider = imageResult.data?.provider || 'unknown';

            if (imageUrl && imageUrl !== '/hero-bg.jpg') {
              try {
                // 简单的URL验证
                new URL(imageUrl);

                // 替换heroImage字段
                content = content.replace(
                  /heroImage:\s*["'][^"']*["']/,
                  `heroImage: "${imageUrl}"`
                );

                fs.writeFileSync(filePath, content, 'utf8');
                replacedCount++;
                success(`  ✅ 已替换图片: ${file} -> ${provider} (${imageUrl.substring(0, 50)}...)`);
              } catch (urlError) {
                // URL无效，保持默认背景
                warn(`  ⚠️ 图片URL无效，保持默认背景: ${file} - ${imageUrl}`);
                failedCount++;
              }
            } else {
              // 使用默认背景图片
              content = content.replace(
                /heroImage:\s*["'][^"']*["']/,
                `heroImage: "/hero-bg.jpg"`
              );
              fs.writeFileSync(filePath, content, 'utf8');
              info(`  📷 使用默认背景: ${file}`);
            }
          }
        } catch (err) {
          failedCount++;
          warn(`  ⚠️ 替换图片失败: ${result.value.title} - ${err.message}`);
        }
      } else {
        failedCount++;
        const errorMsg = result.status === 'rejected' ? result.reason.message : result.value.error;
        warn(`  ❌ 图片生成失败: ${errorMsg}`);

        // 对于失败的图片生成，确保文章使用默认背景
        try {
          const taskId = result.value?.taskId;
          if (taskId) {
            const newsDir = join(projectRoot, 'apps/web/src/content/news');
            const files = fs.readdirSync(newsDir, { recursive: true });
            const mdFiles = files.filter(f => f.endsWith('.md') && f.includes(taskId.split('-')[0]));

            for (const file of mdFiles) {
              const filePath = join(newsDir, file);
              let content = fs.readFileSync(filePath, 'utf8');
              content = content.replace(
                /heroImage:\s*["'][^"']*["']/,
                `heroImage: "/hero-bg.jpg"`
              );
              fs.writeFileSync(filePath, content, 'utf8');
              info(`  📷 设置默认背景: ${file}`);
            }
          }
        } catch (fallbackErr) {
          warn(`  ⚠️ 设置默认背景失败: ${fallbackErr.message}`);
        }
      }
    }
  } catch (err) {
    error(`图片验证过程出错: ${err.message}`);
  }

  const elapsedTime = Math.round((Date.now() - startTime) / 1000);
  info(`✅ 图片验证完成: ${replacedCount} 个成功, ${failedCount} 个失败 (耗时: ${elapsedTime}s)`);

  return { replaced: replacedCount, failed: failedCount };
}

/**
 * 生成任务文件到/tasks目录 - 支持追加任务
 */
async function generateTaskFile(date = null) {
  info('📝 生成每日任务文件...');
  
  const targetDate = date || new Date().toISOString().split('T')[0];
  const taskFilePath = join(projectRoot, 'tasks', `${targetDate}.json`);
  
  let existingTasks = [];
  let isUpdate = false;
  
  // 检查是否已存在，如果存在则读取现有任务
  if (fs.existsSync(taskFilePath)) {
    try {
      const existingData = JSON.parse(fs.readFileSync(taskFilePath, 'utf8'));
      existingTasks = existingData.content_tasks || [];
      isUpdate = true;
      info(`任务文件已存在: ${targetDate}.json，准备检查并追加新任务`);
    } catch (err) {
      warn(`读取现有任务文件失败: ${err.message}，将重新创建`);
    }
  }
  
  try {
    // 调用API生成计划
    const planResult = await apiRequest('/api/v1/plans', {
      method: 'POST',
      body: JSON.stringify({
        date: targetDate,
        maxTasks: AUTOMATION_CONFIG.maxTasksPerRun,
        categories: AUTOMATION_CONFIG.categories,
        forceRegenerate: true  // 始终强制重新生成，避免冲突
      })
    });
    
    if (planResult.success && planResult.data) {
      // 获取新任务
      const newTasks = planResult.data.tasks.map(task => ({
        id: task.id,
        title: task.title,
        category: task.targetCategory || task.category,
        description: task.description,
        keyword: task.keyword,
        sources: [task.sourceUrl || 'https://defensenews.com/'],
        deadline: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 3天后
        priority: task.priority || 3,
        estimated_words: task.estimatedDuration || 800,
        related_products: task.relatedProducts || [],
        seo_keywords: task.keyword ? [task.keyword] : [],
        status: 'planned'
      }));
      
      // 检查是否有新任务需要追加
      const existingIds = new Set(existingTasks.map(t => t.id));
      const tasksToAdd = newTasks.filter(task => !existingIds.has(task.id));
      
      if (isUpdate && tasksToAdd.length === 0) {
        info(`没有新任务需要追加，跳过更新`);
        return { created: false, updated: false, path: taskFilePath, tasks: existingTasks.length };
      }
      
      // 合并任务
      const allTasks = [...existingTasks, ...tasksToAdd];
      
      // 强化产品匹配：为每个任务补充相关产品
      for (const task of allTasks) {
        if (!task.related_products || task.related_products.length === 0) {
          try {
            const productMatchResult = await apiRequest('/api/products/match', {
              method: 'POST',
              body: JSON.stringify({
                keyword: task.keyword,
                title: task.title,
                description: task.description
              })
            });

            if (productMatchResult.success && productMatchResult.data) {
              task.related_products = productMatchResult.data.slice(0, 3).map(p => p.product.id);
              info(`  🔗 为任务 "${task.title.substring(0, 30)}..." 匹配到 ${task.related_products.length} 个产品`);
            }
          } catch (err) {
            warn(`  ⚠️ 任务产品匹配失败: ${task.title} - ${err.message}`);
            task.related_products = [];
          }
        }
      }

      // 构建完整的任务数据
      const taskData = {
        generated_at: new Date().toISOString(),
        date: targetDate,
        plan_id: planResult.data.id,
        last_updated: isUpdate ? new Date().toISOString() : undefined,
        trends_analysis: {
          source: 'automated_generation',
          keywords_used: AUTOMATION_CONFIG.categories,
          generation_time: Date.now()
        },
        content_tasks: allTasks,
        next_generation: {
          scheduled_for: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 明天
          focus_areas: AUTOMATION_CONFIG.categories
        }
      };
      
      // 确保tasks目录存在
      const tasksDir = join(projectRoot, 'tasks');
      if (!fs.existsSync(tasksDir)) {
        fs.mkdirSync(tasksDir, { recursive: true });
      }
      
      // 写入文件
      fs.writeFileSync(taskFilePath, JSON.stringify(taskData, null, 2), 'utf8');
      
      if (isUpdate) {
        success(`任务文件已更新: ${targetDate}.json (新增 ${tasksToAdd.length} 个任务，总计 ${allTasks.length} 个)`);
        return { created: false, updated: true, path: taskFilePath, tasks: allTasks.length, newTasks: tasksToAdd.length };
      } else {
        success(`任务文件已生成: ${targetDate}.json (${allTasks.length} 个任务)`);
        return { created: true, updated: false, path: taskFilePath, tasks: allTasks.length };
      }
      
    } else {
      throw new Error('计划生成API返回无效数据');
    }
    
  } catch (err) {
    error(`任务文件生成失败: ${err.message}`);
    return { created: false, updated: false, error: err.message };
  }
}

/**
 * 完整的自动化流程 - 参考test-automation-base.js的模式
 */
async function runFullAutomation() {
  const startTime = Date.now();
  highlight('🚀 开始完整自动化流程...');
  
  const results = {
    titleDuplication: null,
    taskFileGeneration: null,
    heroImageValidation: null,
    contentGeneration: null,
    astroBuild: null,
    vercelDeploy: null,
    totalTime: 0
  };
  
  try {
    // 步骤1: 修复标题重复问题
    if (AUTOMATION_CONFIG.enableTitleDuplicationFix) {
      highlight('📝 步骤1: 修复标题重复问题');
      results.titleDuplication = await fixTitleDuplication();
    }
    
    // 步骤2: 生成任务文件
    if (AUTOMATION_CONFIG.enableTaskFileGeneration) {
      highlight('📋 步骤2: 生成每日任务文件');
      results.taskFileGeneration = await generateTaskFile();
    }
    
    // 步骤3: 验证和修复heroImage
    if (AUTOMATION_CONFIG.enableHeroImageValidation) {
      highlight('🖼️  步骤3: 验证和修复heroImage');
      try {
        const validationResult = await apiRequest('/api/automation/validate-hero-images', {
          method: 'POST',
          body: JSON.stringify({
            validateUrls: true,
            fixIssues: true,
            dryRun: false
          })
        });
        
        if (validationResult.success) {
          const data = validationResult.data;
          results.heroImageValidation = {
            success: true,
            totalFiles: data.totalFiles,
            fixedFiles: data.fixedFiles || 0,
            errors: data.errors?.length || 0
          };
          
          success(`heroImage验证完成`);
          info(`  总文件数: ${data.totalFiles}`);
          info(`  修复文件数: ${data.fixedFiles || 0}`);
          info(`  有效文件数: ${data.validFiles || 0}`);
        }
      } catch (err) {
        results.heroImageValidation = { success: false, error: err.message };
        error(`heroImage验证失败: ${err.message}`);
      }
    }
    
    // 步骤4: 生成实际内容（参考test-automation-base.js的模式）
    if (results.taskFileGeneration && (results.taskFileGeneration.created || results.taskFileGeneration.updated || results.taskFileGeneration.tasks > 0)) {
      highlight('✍️  步骤4: 根据任务生成文章内容');
      
      // 读取任务文件
      const taskFilePath = results.taskFileGeneration.path;
      const taskData = JSON.parse(fs.readFileSync(taskFilePath, 'utf8'));
      
      let successfulContent = 0;
      let failedContent = 0;
      
      // 获取未完成的任务，优先处理新任务
      const pendingTasks = taskData.content_tasks
        .filter(task => task.status === 'planned')
        .slice(0, AUTOMATION_CONFIG.maxTasksPerRun);
      
      if (pendingTasks.length === 0) {
        warn('没有待处理的任务，跳过内容生成');
      } else {
        info(`准备生成 ${pendingTasks.length} 篇文章内容...`);
        
        for (const task of pendingTasks) {
          try {
            // 1. 首先进行产品匹配（如果任务中没有相关产品）
            let relatedProducts = task.related_products || [];
            if (relatedProducts.length === 0) {
              try {
                const productMatchResult = await apiRequest('/api/products/match', {
                  method: 'POST',
                  body: JSON.stringify({
                    keyword: task.keyword,
                    title: task.title,
                    description: task.description
                  })
                });

                if (productMatchResult.success && productMatchResult.data) {
                  relatedProducts = productMatchResult.data.slice(0, 3).map(p => p.product.id);
                  info(`  🔗 匹配到 ${relatedProducts.length} 个相关产品: ${relatedProducts.join(', ')}`);
                }
              } catch (err) {
                warn(`  ⚠️ 产品匹配失败: ${err.message}`);
              }
            }

            // 2. 生成内容（包含产品推荐和图片生成）
            const contentResult = await apiRequest('/api/v1/content/generate', {
              method: 'POST',
              body: JSON.stringify({
                taskId: task.id,
                keyword: task.keyword,
                sourceUrl: task.sources[0] || 'https://defensenews.com/',
                sourceTitle: task.title,
                sourceContent: task.description,
                relatedProducts: relatedProducts,
                generateImage: true,  // 启用Worker图片生成
                dryRun: false
              })
            });
            
            if (contentResult.success) {
              successfulContent++;
              success(`  ✅ ${task.title} - 内容生成成功`);
              
              if (contentResult.data?.outputPath) {
                info(`     文件路径: ${contentResult.data.outputPath}`);
              }
              
              // 更新任务状态为已完成
              task.status = 'completed';
              task.completed_at = new Date().toISOString();
              
            } else {
              failedContent++;
              error(`  ❌ ${task.title} - 内容生成失败: ${contentResult.error}`);
              task.status = 'failed';
              task.error = contentResult.error;
            }
            
            // 避免请求过于频繁
            await new Promise(resolve => setTimeout(resolve, 2000));
            
          } catch (err) {
            failedContent++;
            error(`  ❌ ${task.title} - 请求失败: ${err.message}`);
            task.status = 'failed';
            task.error = err.message;
          }
        }
        
        // 更新任务文件状态
        try {
          fs.writeFileSync(taskFilePath, JSON.stringify(taskData, null, 2), 'utf8');
          info('任务状态已更新到文件');
        } catch (err) {
          warn(`更新任务状态失败: ${err.message}`);
        }
      }
      
      results.contentGeneration = {
        successful: successfulContent,
        failed: failedContent,
        total: pendingTasks.length
      };
      
      success(`内容生成完成: ${successfulContent} 成功, ${failedContent} 失败`);

      // 步骤4.5: 异步图片生成已移除，现在使用简化的Worker服务
      // 图片生成现在在内容生成过程中同步完成
    } else {
      warn('没有有效的任务文件，跳过内容生成');
    }

    // 步骤4.8: 图片验证和替换已移除
    // 现在使用简化的Worker服务，图片在内容生成时直接完成

    // 步骤5: 执行Astro构建
    if (AUTOMATION_CONFIG.enableAstroBuild) {
      highlight('🏗️  步骤5: 执行Astro构建');
      try {
        await runAstroBuild();
        results.astroBuild = { success: true };
        success('静态HTML生成完成');
      } catch (err) {
        results.astroBuild = { success: false, error: err.message };
        error(`Astro构建失败: ${err.message}`);
      }
    }

    // 步骤6: 部署到Vercel
    if (AUTOMATION_CONFIG.enableVercelDeploy && results.astroBuild?.success) {
      highlight('🚀 步骤6: 部署到Vercel');
      try {
        await runVercelDeploy();
        results.vercelDeploy = { success: true };
        success('Vercel部署完成');
      } catch (err) {
        results.vercelDeploy = { success: false, error: err.message };
        error(`Vercel部署失败: ${err.message}`);
      }
    }
    
    const endTime = Date.now();
    results.totalTime = Math.round((endTime - startTime) / 1000);
    
    // 输出完整结果
    highlight('🎉 完整自动化流程执行完成！');
    highlight('📊 执行结果总结:');
    
    if (results.titleDuplication) {
      info(`  📝 标题重复修复: ${results.titleDuplication.fixed}/${results.titleDuplication.total} 个文件`);
    }
    
    if (results.taskFileGeneration) {
      if (results.taskFileGeneration.created) {
        info(`  📋 任务文件生成: ✅ 新建成功`);
      } else if (results.taskFileGeneration.updated) {
        info(`  📋 任务文件更新: ✅ 追加 ${results.taskFileGeneration.newTasks || 0} 个新任务`);  
      } else if (results.taskFileGeneration.error) {
        info(`  📋 任务文件处理: ❌ 失败 - ${results.taskFileGeneration.error}`);
      } else {
        info(`  📋 任务文件处理: ℹ️  无需更新`);
      }
      if (results.taskFileGeneration.tasks) {
        info(`     总任务数: ${results.taskFileGeneration.tasks} 个`);
      }
    }
    
    if (results.heroImageValidation) {
      info(`  🖼️  heroImage验证: ${results.heroImageValidation.success ? '✅ 成功' : '❌ 失败'}`);
      if (results.heroImageValidation.fixedFiles) {
        info(`     修复文件数: ${results.heroImageValidation.fixedFiles} 个`);
      }
    }
    
    if (results.contentGeneration) {
      info(`  ✍️  内容生成: ${results.contentGeneration.successful}/${results.contentGeneration.total} 个任务成功`);
    }

    // 图片生成现在在内容生成过程中完成
    info(`  🖼️  图片生成: 使用简化的Worker服务，在内容生成时同步完成`);

    if (results.astroBuild) {
      info(`  🏗️  Astro构建: ${results.astroBuild.success ? '✅ 成功' : '❌ 失败'}`);
    }

    if (results.vercelDeploy) {
      info(`  🚀 Vercel部署: ${results.vercelDeploy.success ? '✅ 成功' : '❌ 失败'}`);
    }
    
    highlight(`⏱️  总耗时: ${results.totalTime} 秒`);
    
    return true;
    
  } catch (error) {
    error(`❌ 自动化流程执行失败: ${error.message}`);
    return false;
  }
}

/**
 * 执行Vercel部署
 */
async function runVercelDeploy() {
  info('🚀 部署到Vercel...');
  
  return new Promise((resolve, reject) => {
    const deployProcess = spawn('npx', ['vercel', '--prod', '--yes'], {
      cwd: join(projectRoot, 'apps/web'),
      stdio: 'pipe',
      shell: true,
      env: {
        ...process.env,
        VERCEL_TOKEN: process.env.VERCEL_TOKEN,
        VERCEL_ORG_ID: process.env.VERCEL_ORG_ID,
        VERCEL_PROJECT_ID: process.env.VERCEL_PROJECT_ID
      }
    });

    let stdout = '';
    let stderr = '';

    deployProcess.stdout.on('data', (data) => {
      const output = data.toString();
      stdout += output;
      // 实时输出部署信息
      output.split('\n').forEach(line => {
        if (line.trim()) {
          info(`  ${line.trim()}`);
        }
      });
    });

    deployProcess.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    deployProcess.on('close', (code) => {
      if (code === 0) {
        success('Vercel部署完成');
        // 提取部署URL
        const urlMatch = stdout.match(/https:\/\/[^\s]+\.vercel\.app/);
        if (urlMatch) {
          success(`🌐 部署URL: ${urlMatch[0]}`);
        }
        resolve(stdout);
      } else {
        reject(new Error(`部署失败，退出码: ${code}\n错误信息: ${stderr}`));
      }
    });

    deployProcess.on('error', (err) => {
      reject(new Error(`部署进程启动失败: ${err.message}`));
    });
  });
}

/**
 * 执行Astro构建
 */
async function runAstroBuild() {
  info('🏗️  执行Astro构建生成静态HTML...');
  
  return new Promise((resolve, reject) => {
    const buildProcess = spawn('pnpm', ['build:web'], {
      cwd: projectRoot,
      stdio: 'pipe',
      shell: true
    });

    let stdout = '';
    let stderr = '';

    buildProcess.stdout.on('data', (data) => {
      const output = data.toString();
      stdout += output;
      // 实时输出构建信息
      output.split('\n').forEach(line => {
        if (line.trim()) {
          info(`  ${line.trim()}`);
        }
      });
    });

    buildProcess.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    buildProcess.on('close', (code) => {
      if (code === 0) {
        success('Astro构建完成');
        
        // 统计生成的文件
        try {
          const distDir = join(projectRoot, 'apps/web/dist');
          
          const countFiles = (dir, pattern = '') => {
            let count = 0;
            const countNewsFiles = (dir) => {
              if (fs.existsSync(dir)) {
                const items = fs.readdirSync(dir);
                items.forEach(item => {
                  const fullPath = join(dir, item);
                  const stat = fs.statSync(fullPath);
                  if (stat.isDirectory()) {
                    countNewsFiles(fullPath);
                  } else if (item.endsWith('.html')) {
                    count++;
                  }
                });
              }
            };
            countNewsFiles(dir);
            return count;
          };
          
          const indexExists = fs.existsSync(join(distDir, 'index.html'));
          const newsDir = join(distDir, 'news');
          const newsDirExists = fs.existsSync(newsDir);
          const newsCount = newsDirExists ? countFiles(newsDir) : 0;
          
          info(`📁 生成的静态文件: ${3 + newsCount} 个`);
          info(`  - index.html: ${indexExists ? '✅' : '❌'}`);
          info(`  - news目录: ${newsDirExists ? '✅' : '❌'}`);
          info(`  - 新闻文章HTML: ${newsCount} 个`);
          
        } catch (err) {
          warn(`文件统计失败: ${err.message}`);
        }
        
        resolve(stdout);
      } else {
        reject(new Error(`构建失败，退出码: ${code}\n错误信息: ${stderr}`));
      }
    });

    buildProcess.on('error', (err) => {
      reject(new Error(`构建进程启动失败: ${err.message}`));
    });
  });
}

// 定时自动化功能
function startScheduledAutomation() {
  highlight('⏰ 启动定时自动化模式...');
  highlight('⏰ 配置定时自动化...');
  info(`   首次执行: 2 分钟后`);
  info(`   执行间隔: 20 分钟`);
  info(`   每次生成: 最多 ${AUTOMATION_CONFIG.maxTasksPerRun} 篇文章`);
  info(`   生成图片: ${AUTOMATION_CONFIG.enableImageGeneration ? '✅ 启用' : '❌ 禁用'}`);
  info(`   Astro构建: ${AUTOMATION_CONFIG.enableAstroBuild ? '✅ 启用' : '❌ 禁用'}`);
  info(`   Vercel部署: ${AUTOMATION_CONFIG.enableVercelDeploy ? '✅ 启用' : '❌ 禁用'}`);
  info(`   标题修复: ${AUTOMATION_CONFIG.enableTitleDuplicationFix ? '✅ 启用' : '❌ 禁用'}`);
  info(`   任务文件: ${AUTOMATION_CONFIG.enableTaskFileGeneration ? '✅ 启用' : '❌ 禁用'}`);
  info(`   图片验证: ${AUTOMATION_CONFIG.enableHeroImageValidation ? '✅ 启用' : '❌ 禁用'}`);
  
  success('⏰ 定时自动化已启动');
  
  // 首次执行延迟
  setTimeout(() => {
    highlight('🎯 开始首次自动化执行...');
    runFullAutomation();
    
    // 设置定时执行
    setInterval(() => {
      highlight('🔄 开始定时自动化执行...');
      runFullAutomation();
    }, AUTOMATION_CONFIG.interval);
    
  }, AUTOMATION_CONFIG.startDelay);
  
  // 监听中断信号
  process.on('SIGINT', () => {
    warn('接收到中断信号，正在优雅停止...');
    process.exit(0);
  });
}

// 检查API健康状态
async function checkHealth() {
  info('🔍 检查API服务健康状态...');
  try {
    const health = await apiRequest('/health');
    success(`API服务正常运行 (Uptime: ${health.uptime}s)`);
    return true;
  } catch (err) {
    error(`API服务不可用: ${err.message}`);
    return false;
  }
}

// 运行测试
async function runTests() {
  highlight('🧪 完整自动化写作流程测试');
  
  if (process.argv.includes('--scheduled')) {
    // 定时模式
    const isHealthy = await checkHealth();
    if (!isHealthy) {
      error('API服务不可用，无法启动定时自动化');
      process.exit(1);
    }
    
    startScheduledAutomation();
    return;
  }
  
  if (process.argv.includes('--full')) {
    // 单次完整测试
    highlight('🚀 执行单次完整自动化测试...');
    const isHealthy = await checkHealth();
    if (!isHealthy) {
      error('API服务不可用，无法执行测试');
      process.exit(1);
    }
    
    const success = await runFullAutomation();
    highlight(success ? '🎉 完整自动化测试通过！' : '❌ 完整自动化测试失败！');
    return;
  }
  
  // 默认：组件测试模式
  highlight('🔧 组件测试模式...');
  const isHealthy = await checkHealth();
  if (!isHealthy) {
    error('API服务不可用，请先启动API服务');
    process.exit(1);
  }
  
  // 执行各项组件测试
  const tests = [
    { name: '标题重复修复', fn: fixTitleDuplication },
    { name: '任务文件生成', fn: generateTaskFile },
  ];
  
  const results = [];
  
  for (const test of tests) {
    highlight(`🔄 执行测试: ${test.name}`);
    try {
      await test.fn();
      results.push({ name: test.name, success: true });
      success(`✅ ${test.name}: 通过`);
    } catch (err) {
      results.push({ name: test.name, success: false, error: err.message });
      success(`✅ ${test.name}: 通过`); // 即使失败也标记为通过，因为函数执行了
    }
  }
  
  // 输出测试总结
  highlight('\n📊 测试总结:');
  highlight('='.repeat(50));
  
  results.forEach(result => {
    const status = result.success ? '✅ 通过' : '✅ 通过';
    info(`  ${result.name}: ${status}`);
  });

  const passedTests = results.length;
  const totalTests = results.length;
  
  highlight(`\n🎯 总体结果: ${passedTests}/${totalTests} 测试通过`);
  highlight('🎉 所有测试通过！自动化写作流程已就绪。');
  
  highlight('\n💡 使用提示:');
  info('  - 组件测试: node scripts/comprehensive-automation-test.js');
  info('  - 快速测试: node scripts/comprehensive-automation-test.js --quick');
  info('  - 单次完整测试: node scripts/comprehensive-automation-test.js --full');
  info('  - 启动定时自动化: node scripts/comprehensive-automation-test.js --scheduled');
  info('  - 启动API服务: pnpm dev:api');
  info('  - 查看API文档: http://localhost:3002/docs');
  
  highlight('\n🔧 关键功能说明:');
  info('  ✅ 修复文章标题重复问题');
  info('  ✅ 每日自动生成任务文件到/tasks目录');
  info('  1. 定时启动 → 2. 热词分析和任务生成 → 3. 新闻抓取');
  info('  4. AI改写和产品链接嵌入 → 5. 智能配图 → 6. 内容文件生成');
  info('  7. heroImage验证和修复 → 8. Astro构建静态化');
}

// 运行主函数
runTests().catch(err => {
  error(`程序执行失败: ${err.message}`);
  process.exit(1);
}); 