# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variable files
.env
.env.development.local
.env.test.local
.env.production.local
.env.local

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# vuepress build output
.vuepress/dist

# vuepress v2.x temp and cache directory
.temp
.cache

# Docusaurus cache and generated files
.docusaurus

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Astro specific
.astro/
dist/
.vercel/
.netlify/

# Build outputs
build/
out/

# Generated files
src/env.d.ts

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Editor directories and files
.vscode/
!.vscode/extensions.json
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
*~

# Temporary files
*.tmp
*.temp

# Log files
*.log
logs
*.log.*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Database
*.db
*.sqlite
*.sqlite3

# Local development
.env.local
.env.development
.env.test
.env.production

# Package manager lock files (choose one)
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# Content management
# Uncomment if you want to ignore content files
# src/content/**
# content/**

# Analytics and tracking
.analytics/
google*.html
sitemap*.xml

# Backup files
*.bak
*.backup
*.old

# Compressed files
*.zip
*.tar.gz
*.rar

# Generated documentation
docs/build/
docs/dist/

# Test coverage
coverage/
.nyc_output/

# Storybook build outputs
storybook-static/

# Local Netlify folder
.netlify

# Local Vercel folder
.vercel

# Wrangler (Cloudflare Workers)
.wrangler/

# Sentry
.sentryclirc

# Mac system files
.DS_Store
.AppleDouble
.LSOverride

# Windows system files
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Linux system files
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# JetBrains IDEs
.idea/
*.iml
*.iws
*.ipr
out/

# Sublime Text
*.sublime-workspace
*.sublime-project

# Atom
.atom/

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Vim
[._]*.s[a-v][a-z]
[._]*.sw[a-p]
[._]s[a-rt-v][a-z]
[._]ss[a-gi-z]
[._]sw[a-p]
Session.vim
Sessionx.vim
.netrwhist
*~
tags
[._]*.un~ 