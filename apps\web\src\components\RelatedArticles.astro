---
import { type CollectionEntry, getCollection } from 'astro:content';

export interface Props {
  currentPost: CollectionEntry<'news'>;
  limit?: number;
}

const { currentPost, limit = 3 } = Astro.props;

// 获取所有已发布的文章
const allPosts = await getCollection('news', ({ data }) => {
  return !data.draft;
});

// 过滤掉当前文章
const otherPosts = allPosts.filter(post => post.id !== currentPost.id);

// 相关度计算函数
function calculateRelevance(post1: CollectionEntry<'news'>, post2: CollectionEntry<'news'>): number {
  let score = 0;
  
  // 相同分类加分
  if (post1.data.category === post2.data.category) {
    score += 40;
  }
  
  // 标签匹配加分
  const tags1 = post1.data.tags || [];
  const tags2 = post2.data.tags || [];
  const commonTags = tags1.filter(tag => tags2.includes(tag));
  score += commonTags.length * 15;
  
  // 标题关键词匹配
  const title1Words = post1.data.title.toLowerCase().split(' ').filter(word => word.length > 3);
  const title2Words = post2.data.title.toLowerCase().split(' ').filter(word => word.length > 3);
  const commonWords = title1Words.filter(word => title2Words.includes(word));
  score += commonWords.length * 10;
  
  // 发布时间相近加分（7天内）
  const date1 = new Date(post1.data.date);
  const date2 = new Date(post2.data.date);
  const dayDiff = Math.abs(date1.getTime() - date2.getTime()) / (1000 * 60 * 60 * 24);
  if (dayDiff <= 7) {
    score += Math.max(0, 10 - dayDiff);
  }
  
  // 相关产品匹配
  const products1 = post1.data.relatedProducts || [];
  const products2 = post2.data.relatedProducts || [];
  const commonProducts = products1.filter(product => products2.includes(product));
  score += commonProducts.length * 20;
  
  return score;
}

// 计算所有文章的相关度并排序
const relatedPosts = otherPosts
  .map(post => ({
    post,
    relevance: calculateRelevance(currentPost, post)
  }))
  .filter(item => item.relevance > 20) // 只显示相关度大于20的文章
  .sort((a, b) => b.relevance - a.relevance)
  .slice(0, limit)
  .map(item => item.post);

// 如果相关文章不足，补充同分类的最新文章
if (relatedPosts.length < limit) {
  const sameCategoryPosts = otherPosts
    .filter(post => 
      post.data.category === currentPost.data.category && 
      !relatedPosts.some(related => related.id === post.id)
    )
    .sort((a, b) => new Date(b.data.date).getTime() - new Date(a.data.date).getTime())
    .slice(0, limit - relatedPosts.length);
  
  relatedPosts.push(...sameCategoryPosts);
}
---

{relatedPosts.length > 0 && (
  <section class="bg-gray-50 rounded-lg p-6 mt-8">
    <h3 class="text-xl font-bold text-gray-900 mb-4 flex items-center">
      <svg class="w-5 h-5 mr-2 text-primary-600" fill="currentColor" viewBox="0 0 20 20">
        <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z"/>
      </svg>
      Related Articles
    </h3>
    <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {relatedPosts.map((post) => {
        const postDate = new Date(post.data.date);
        return (
          <article class="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow border border-gray-200">
            <a href={`/news/${post.id}`} class="block p-4">
              {post.data.heroImage && (
                <div class="aspect-w-16 aspect-h-9 mb-3 rounded-md overflow-hidden">
                  <img 
                    src={post.data.heroImage} 
                    alt={post.data.title}
                    class="w-full h-32 object-cover"
                    loading="lazy"
                  />
                </div>
              )}
              
              <div class="space-y-2">
                <div class="flex items-center space-x-2 text-xs text-gray-500">
                  <span class="bg-primary-100 text-primary-800 px-2 py-1 rounded-full font-medium">
                    {post.data.category}
                  </span>
                  <time datetime={postDate.toISOString()}>
                    {postDate.toLocaleDateString('en-US', { 
                      month: 'short', 
                      day: 'numeric', 
                      year: 'numeric' 
                    })}
                  </time>
                </div>
                
                <h4 class="font-semibold text-gray-900 line-clamp-2 hover:text-primary-600 transition-colors">
                  {post.data.title}
                </h4>
                
                {post.data.description && (
                  <p class="text-sm text-gray-600 line-clamp-2">
                    {post.data.description}
                  </p>
                )}
                
                {post.data.tags && post.data.tags.length > 0 && (
                  <div class="flex flex-wrap gap-1 mt-2">
                    {post.data.tags.slice(0, 2).map((tag) => (
                      <span class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                        {tag}
                      </span>
                    ))}
                  </div>
                )}
              </div>
            </a>
          </article>
        );
      })}
    </div>
  </section>
)}

<style>
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style> 