---
// Navigation items with proper typing
const navItems = [
  { name: 'Industry', href: '/category/industry', icon: '🏭' },
  { name: 'Research', href: '/category/research', icon: '🔬' },
  { name: 'Events', href: '/category/events', icon: '📅' },
  { name: 'Frontier', href: '/category/frontier', icon: '🚀' },
  { name: 'Insight', href: '/category/insight', icon: '💡' },
  { name: 'More', href: '/category/misc', icon: '📰' }
];

// Get current path for active states
const currentPath = Astro.url.pathname;
---

<header class="sticky top-0 z-50 bg-white/95 backdrop-blur-md border-b border-secondary-200 shadow-soft">
  <nav class="container-page py-4" aria-label="Main navigation">
    <div class="flex items-center justify-between">
      <!-- Logo and brand -->
      <div class="flex items-center space-x-3">
        <a 
          href="/" 
          class="flex items-center space-x-3 group focus-outline rounded-lg p-2 -m-2"
          aria-label="Aerospace & Defense News - Home"
        >
          <!-- Logo Icon -->
          <div class="w-10 h-10 bg-gradient-to-br from-primary-600 to-primary-700 rounded-xl flex items-center justify-center shadow-medium group-hover:shadow-strong transition-all duration-300 group-hover:scale-105">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"/>
            </svg>
          </div>
          
          <!-- Brand Text -->
          <div class="hidden sm:block">
            <h1 class="text-xl font-bold text-secondary-900 group-hover:text-primary-600 transition-colors">
              Aerospace & Defense
            </h1>
            <p class="text-xs text-secondary-500 font-medium tracking-wide uppercase">
              News Hub
            </p>
          </div>
        </a>
      </div>

      <!-- Desktop Navigation -->
      <div class="hidden lg:flex items-center space-x-8">
        {navItems.map((item) => {
          const isActive = currentPath.startsWith(item.href);
          return (
            <a 
              href={item.href}
              class={`nav-link group relative ${isActive ? 'nav-link-active' : ''}`}
              aria-current={isActive ? 'page' : undefined}
            >
              <span class="flex items-center space-x-2">
                <span class="text-lg" aria-hidden="true">{item.icon}</span>
                <span>{item.name}</span>
              </span>
              
              <!-- Active indicator -->
              {isActive && (
                <div class="absolute -bottom-1 left-0 right-0 h-0.5 bg-primary-600 rounded-full"></div>
              )}
              
              <!-- Hover indicator -->
              <div class="absolute -bottom-1 left-0 right-0 h-0.5 bg-primary-600 rounded-full scale-x-0 group-hover:scale-x-100 transition-transform duration-200"></div>
            </a>
          );
        })}
      </div>

      <!-- Right side actions -->
      <div class="flex items-center space-x-4">
        <!-- Search button -->
        <a 
          href="/search"
          class="btn-ghost flex items-center space-x-2 focus-outline"
          aria-label="Search articles"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
          <span class="hidden md:inline">Search</span>
        </a>

        <!-- Newsletter signup CTA -->
        <a 
          href="#newsletter"
          class="hidden xl:inline-flex btn-primary text-sm"
        >
          Subscribe
        </a>

        <!-- Mobile menu button -->
        <button 
          class="lg:hidden btn-ghost p-2 focus-outline"
          aria-label="Open mobile menu"
          x-data
          @click="$dispatch('toggle-mobile-menu')"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        </button>
      </div>
    </div>

    <!-- Mobile Navigation Menu -->
    <div 
      class="lg:hidden"
      x-data="{ open: false }"
      @toggle-mobile-menu.window="open = !open"
      x-show="open"
      x-transition:enter="transition ease-out duration-200"
      x-transition:enter-start="opacity-0 scale-95"
      x-transition:enter-end="opacity-100 scale-100"
      x-transition:leave="transition ease-in duration-150"
      x-transition:leave-start="opacity-100 scale-100"
      x-transition:leave-end="opacity-0 scale-95"
      style="display: none;"
    >
      <!-- Mobile menu overlay -->
      <div 
        class="fixed inset-0 bg-secondary-900/50 backdrop-blur-sm z-40"
        @click="open = false"
        aria-hidden="true"
      ></div>
      
      <!-- Mobile menu panel -->
      <div class="absolute top-full left-0 right-0 bg-white shadow-strong border-t border-secondary-200 z-50">
        <div class="container-page py-6">
          <div class="grid grid-cols-2 gap-4 mb-6">
            {navItems.map((item) => {
              const isActive = currentPath.startsWith(item.href);
              return (
                <a 
                  href={item.href}
                  class={`p-4 rounded-xl border transition-all duration-200 ${
                    isActive 
                      ? 'bg-primary-50 border-primary-200 text-primary-700' 
                      : 'bg-secondary-50 border-secondary-200 text-secondary-700 hover:bg-primary-50 hover:border-primary-200'
                  }`}
                  @click="open = false"
                >
                  <div class="flex items-center space-x-3">
                    <span class="text-2xl" aria-hidden="true">{item.icon}</span>
                    <span class="font-semibold">{item.name}</span>
                  </div>
                </a>
              );
            })}
          </div>
          
          <!-- Mobile menu actions -->
          <div class="flex flex-col space-y-3">
            <a 
              href="/search"
              class="btn-secondary text-center"
              @click="open = false"
            >
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
              Search Articles
            </a>
            <a 
              href="#newsletter"
              class="btn-primary text-center"
              @click="open = false"
            >
              Subscribe to Newsletter
            </a>
          </div>
        </div>
      </div>
    </div>
  </nav>
</header>

<style>
  /* Enhance backdrop blur for better browser support */
  @supports (backdrop-filter: blur(12px)) {
    header {
      backdrop-filter: blur(12px);
    }
  }
  
  /* Fallback for browsers without backdrop-filter */
  @supports not (backdrop-filter: blur(12px)) {
    header {
      background-color: rgba(255, 255, 255, 0.98);
    }
  }
</style> 