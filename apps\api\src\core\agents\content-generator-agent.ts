import { z } from 'zod';
import { BaseAgent, type AgentContext } from './base-agent.js';
import { taskRepository, contentRepository, planRepository } from '../database/repositories.js';
import { createHash } from 'crypto';
import { writeFileSync, mkdirSync, existsSync } from 'fs';
import { dirname, join, resolve } from 'path';
import { fileURLToPath } from 'url';
import { createImageService } from '../tools/image/image-service.js';
// import { backgroundImageService } from '../services/background-image-service.js'; // 暂时注释掉，文件不存在
import { contentService } from '../services/content-service.js';

// 导入产品数据加载器
import { productDataLoader } from '../../utils/productDataLoader.js';

import { generateFrontmatter } from '@news-site/shared';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// 获取项目根目录路径（从 apps/api/dist/core/agents 向上5级到项目根目录）
const projectRoot = resolve(__dirname, '../../../../..');

// 内容生成输入schema
export const ContentGeneratorInputSchema = z.object({
  taskId: z.string(),
  keyword: z.string().min(1),
  sourceUrl: z.string().url().optional(),
  sourceContent: z.string().optional(),
  relatedProducts: z.array(z.string()).default([]),
  generateImage: z.boolean().default(true),
  preGeneratedImageUrl: z.string().optional(),
  outputDir: z.string().optional(),
  dryRun: z.boolean().default(false)
});

export type ContentGeneratorInput = z.infer<typeof ContentGeneratorInputSchema>;

// 生成的文章输出
export interface ArticleOutput {
  title: string;
  slug: string;
  content: string;
  frontMatter: Record<string, any>;
  imageUrl?: string;
  metadata: {
    wordCount: number;
    readTime: number;
    seoScore: number;
    keywordDensity: number;
  };
}

/**
 * 内容生成Agent
 * 负责从源内容生成高质量的技术文章
 */
export class ContentGeneratorAgent extends BaseAgent {
  private openaiApiKey?: string;
  private openaiModel: string = 'gpt-4o-mini';
  private unsplashApiKey?: string;
  private outputDir: string = join(projectRoot, 'apps/web/src/content/news');
  private imageService: any;

  constructor() {
    super({
      name: 'ContentGeneratorAgent',
      description: 'Generates high-quality technical articles from source content',
      version: '2.0.0',
      maxRetries: 3,
      timeout: 120000 // 2 minutes
    });
  }

  protected async onInitialize(): Promise<void> {
    // 获取API密钥
    this.openaiApiKey = process.env.OPENAI_API_KEY;
    this.unsplashApiKey = process.env.UNSPLASH_ACCESS_KEY;
    
    // 初始化图片服务
    this.imageService = createImageService((level, message, data) => {
      this.log(level as any, message, data);
    });
    
    // 只在实际执行任务时检查API密钥，不在初始化时阻塞
    if (!this.openaiApiKey) {
      await this.log('warn', 'OPENAI_API_KEY not configured - service will be limited', {});
    }

    // 设置输出目录
    const customOutputDir = process.env.CONTENT_OUTPUT_DIR;
    if (customOutputDir) {
      this.outputDir = customOutputDir;
    }

    await this.log('info', 'Content generator initialized', {
      model: this.openaiModel,
      outputDir: this.outputDir,
      hasOpenaiKey: !!this.openaiApiKey,
      hasUnsplashKey: !!this.unsplashApiKey,
      imageServiceInitialized: !!this.imageService
    });
  }

  protected async validateInput(input: any): Promise<any> {
    return ContentGeneratorInputSchema.parse(input);
  }

  protected async onExecute(input: any, context: AgentContext): Promise<any> {
    const validatedInput = input as ContentGeneratorInput;
    const startTime = Date.now();
    let task: any = null;

    // 检查API密钥，如果是dryRun模式则允许继续
    if (!this.openaiApiKey && !validatedInput.dryRun) {
      throw new Error('OPENAI_API_KEY is required for content generation. Please configure it in your environment variables.');
    }

    try {
      // 获取任务信息，如果不存在则创建一个mock任务
      task = await taskRepository.getById(validatedInput.taskId);
      if (!task) {
        await this.log('info', `Task ${validatedInput.taskId} not found, creating mock task for content generation`, {
          taskId: validatedInput.taskId,
          keyword: validatedInput.keyword
        });
        
        // 创建一个mock任务对象，不保存到数据库
        task = {
          id: validatedInput.taskId,
          planId: 'mock-plan',
          title: `Auto-generated task for ${validatedInput.keyword}`,
          description: `Automatically created task for content generation`,
          keyword: validatedInput.keyword,
          targetCategory: this.inferCategory(validatedInput.keyword) as 'industry' | 'research' | 'events' | 'frontier' | 'insight' | 'misc',
          priority: 3,
          status: 'pending' as const,
          relatedProducts: validatedInput.relatedProducts || [],
          sourceUrl: validatedInput.sourceUrl,
          estimatedDuration: 60,
          actualDuration: null,
          errorMessage: null,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
      } else {
        // 更新任务状态
        await taskRepository.update(validatedInput.taskId, {
          status: 'in-progress'
        });
      }

      let sourceContent = input.sourceContent || '';
      let sourceTitle = '';
      let sourceMetadata = {};

      // 如果有源URL，抓取内容
      if (input.sourceUrl && !sourceContent) {
        const scrapeResult = await this.scrapeContent(input.sourceUrl);
        if (scrapeResult.success) {
          sourceContent = scrapeResult.content || '';
          sourceTitle = scrapeResult.title || '';
          sourceMetadata = scrapeResult.metadata || {};
        } else {
          await this.log('warn', 'Failed to scrape content, using keyword only', {
            url: input.sourceUrl,
            error: scrapeResult.error
          });
        }
      }

      // 生成文章内容
      const article = await this.generateContent({
        keyword: input.keyword,
        sourceContent,
        sourceTitle,
        sourceMetadata,
        relatedProducts: input.relatedProducts,
        taskId: input.taskId,
        targetCategory: task.targetCategory,
        dryRun: input.dryRun
      });

      // 处理配图：优先使用预生成图片
      if (input.preGeneratedImageUrl) {
        article.imageUrl = input.preGeneratedImageUrl;
        await this.log('info', 'Using pre-generated image', { 
          url: input.preGeneratedImageUrl.substring(0, 80) + '...' 
        });
      } else if (input.generateImage) {
        try {
          const imageResult = await this.generateImage(article.title, input.keyword, task.targetCategory);
          article.imageUrl = imageResult.url;
        } catch (error) {
          await this.log('warn', 'Image generation failed, using fallback', { error });
          article.imageUrl = this.getFallbackImage(input.keyword, task.targetCategory);
        }
      }

      let outputPath: string | undefined;

      // 保存文章文件
      if (!input.dryRun) {
        outputPath = await this.saveArticleFile(article, task, input.outputDir);

        // 只有真实任务才保存到数据库
        if (task.planId !== 'mock-plan') {
          // 保存到数据库
          await contentRepository.create({
            taskId: validatedInput.taskId,
            title: article.title,
            slug: article.slug,
            markdown: article.content,
            frontMatter: article.frontMatter,
            wordCount: article.metadata.wordCount,
            readTime: article.metadata.readTime,
            seoScore: article.metadata.seoScore,
            keywordDensity: article.metadata.keywordDensity,
            generatedImageUrl: article.imageUrl
          });

          // 更新任务状态
          await taskRepository.update(validatedInput.taskId, {
            status: 'completed'
          });
        } else {
          await this.log('info', 'Skipping database save for mock task', {
            taskId: validatedInput.taskId
          });
        }
      }

      const executionTime = Date.now() - startTime;

      await this.log('info', 'Content generation completed', {
        taskId: input.taskId,
        title: article.title,
        wordCount: article.metadata.wordCount,
        executionTime,
        dryRun: input.dryRun
      });

      return {
        success: true,
        data: {
          article,
          outputPath,
          executionTime
        },
        metadata: context.metadata,
        warnings: [],
        executionTime
      };

    } catch (error) {
      // 更新任务状态为失败（只对真实任务）
      if (!input.dryRun && task && task.planId !== 'mock-plan') {
        await taskRepository.update(input.taskId, {
          status: 'failed',
          errorMessage: error instanceof Error ? error.message : 'Unknown error'
        });
      }

      throw error;
    }
  }

  protected async onHealthCheck(): Promise<Record<string, any>> {
    return {
      openaiConfigured: !!this.openaiApiKey,
      unsplashConfigured: !!this.unsplashApiKey,
      outputDirectory: this.outputDir,
      outputDirectoryExists: existsSync(this.outputDir),
      model: this.openaiModel
    };
  }

  /**
   * 抓取网页内容
   */
  private async scrapeContent(url: string): Promise<{
    success: boolean;
    title?: string;
    content?: string;
    metadata?: Record<string, any>;
    error?: string;
  }> {
    try {
      const response = await fetch(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const html = await response.text();
      const title = this.extractTitleFromHtml(html);
      const content = this.extractContentFromHtml(html);

      return {
        success: true,
        title,
        content,
        metadata: {
          url,
          statusCode: response.status,
          contentLength: html.length
        }
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * 生成文章内容
   */
  private async generateContent(data: {
    keyword: string;
    sourceContent: string;
    sourceTitle: string;
    sourceMetadata: Record<string, any>;
    relatedProducts: string[];
    taskId: string;
    targetCategory?: string;
    dryRun?: boolean;
  }): Promise<ArticleOutput> {
    // 如果是dryRun模式或没有API密钥，生成mock内容
    if (data.dryRun || !this.openaiApiKey) {
      return this.generateMockContent(data);
    }

    await this.log('info', 'Using upgraded ContentService for content generation', {
      keyword: data.keyword,
      sourceContentLength: data.sourceContent.length,
      relatedProductsCount: data.relatedProducts.length
    });

    try {
      // 使用升级后的ContentService，它包含了产品匹配功能
      const contentOutput = await contentService.generateContent({
        keyword: data.keyword,
        sourceContent: data.sourceContent,
        sourceTitle: data.sourceTitle,
        relatedProducts: data.relatedProducts
      });

      await this.log('info', 'Content generated successfully with product integration', {
        title: contentOutput.title,
        wordCount: contentOutput.metadata.wordCount,
        productLinksCount: contentOutput.metadata.productLinksCount || 0,
        relatedProductsCount: contentOutput.metadata.relatedProducts?.length || 0
      });

      // 转换为ArticleOutput格式
      const articleOutput: ArticleOutput = {
        title: contentOutput.title,
        slug: contentOutput.slug,
        content: contentOutput.content,
        imageUrl: contentOutput.imageUrl,
        frontMatter: {
          title: contentOutput.title,
          description: contentOutput.excerpt,
          date: new Date().toISOString().split('T')[0],
          category: contentOutput.category,
          tags: contentOutput.tags,
          author: 'Technical Editorial Team',
          featured: false,
          draft: false,
          heroImage: contentOutput.imageUrl,
          relatedProducts: data.relatedProducts,
          lang: 'en',
          readTime: contentOutput.metadata.readTime,
          summary: contentOutput.excerpt,
          seo: {
            metaDescription: contentOutput.excerpt,
            keywords: contentOutput.tags,
            ogTitle: contentOutput.title,
            ogDescription: contentOutput.excerpt
          }
        },
        metadata: {
          wordCount: contentOutput.metadata.wordCount || 0,
          readTime: contentOutput.metadata.readTime || 0,
          seoScore: contentOutput.metadata.seoScore || 0,
          keywordDensity: contentOutput.metadata.keywordDensity || 0
        }
      };

      // 如果有相关产品推荐，添加到frontMatter（只保存产品ID）
      if (contentOutput.metadata.relatedProducts) {
        articleOutput.frontMatter.productRecommendations = contentOutput.metadata.relatedProducts.map(p => p.id);
      }

      return articleOutput;

    } catch (error) {
      await this.log('error', 'Content generation failed', { error });
      throw error;
    }
  }

  /**
   * 构建提示词
   */
  private buildPrompt(data: {
    keyword: string;
    sourceContent: string;
    sourceTitle: string;
    relatedProducts: string[];
  }): string {
    return `Create a comprehensive technical article about "${data.keyword}" based on the following source material:

Title: ${data.sourceTitle || 'N/A'}
Content: ${data.sourceContent || 'No source content provided'}

Requirements:
1. Write a professional, engaging article of 800-1200 words
2. Focus on technical aspects and industry applications
3. Include relevant aerospace and defense perspectives
4. Use proper markdown formatting with headers (##, ###)
5. Include a compelling introduction and conclusion
6. Naturally incorporate the keyword "${data.keyword}" throughout
7. Maintain a professional, authoritative tone

Related products to potentially mention: ${data.relatedProducts.join(', ')}

Please provide the article in markdown format with a clear title.`;
  }

  /**
   * 解析生成的内容
   */
  private parseGeneratedContent(content: string, keyword: string): ArticleOutput {
    const lines = content.split('\n').filter(line => line.trim());
    const title = lines.find(line => line.startsWith('#'))?.replace(/^#+\s*/, '') || 
                  this.generateFallbackTitle(keyword);
    
    const slug = this.generateSlug(title);
    const wordCount = this.countWords(content);
    
    return {
      title,
      slug,
      content,
      frontMatter: {
        title,
        description: `Comprehensive analysis of ${keyword} in aerospace and defense applications`,
        date: new Date().toISOString().split('T')[0],
        category: this.inferCategory(keyword),
        tags: [keyword],
        author: 'Technical Editorial Team',
        featured: false,
        draft: false,
        heroImage: undefined,
        relatedProducts: [],
        lang: 'en',
        readTime: Math.ceil(wordCount / 200),
        summary: `Professional insights into ${keyword} technology and applications`,
        seo: {
          metaDescription: `Explore ${keyword} technology in aerospace and defense. Expert analysis and industry insights.`,
          keywords: [keyword],
          ogTitle: title,
          ogDescription: `Professional insights into ${keyword} technology and applications`
        }
      },
      metadata: {
        wordCount,
        readTime: Math.ceil(wordCount / 200),
        seoScore: 0,
        keywordDensity: 0
      }
    };
  }

  /**
   * 生成配图 - 使用简化的Worker图片生成
   */
  private async generateImage(title: string, keyword: string, category?: string): Promise<{
    url: string;
  }> {
    try {
      await this.log('info', 'Generating image with simplified Worker service', {
        title: title?.substring(0, 50),
        keyword,
        category
      });

      // 使用简化的图片服务
      const imageService = createImageService(this.log.bind(this));
      const result = await imageService.generateImage({
        keyword,
        title,
        category
      });

      if (!result.success || !result.url) {
        throw new Error(result.error || 'Image generation failed');
      }

      await this.log('info', 'Image generation completed', {
        url: result.url,
        provider: result.provider,
        elapsedMs: result.metadata?.elapsedMs
      });

      return {
        url: result.url
      };

    } catch (error) {
      await this.log('warn', 'Image generation failed, using fallback', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return {
        url: this.getFallbackImage(keyword, category)
      };
    }
  }

  /**
   * 构建图片生成的prompt
   */
  private buildImagePrompt(title: string, keyword: string, category?: string): string {
    const parts = [];
    
    // 基础关键词
    if (keyword) {
      parts.push(keyword);
    }

    // 根据类别添加上下文
    if (category) {
      const categoryPrompts = {
        industry: 'industrial technology, manufacturing, modern facility',
        research: 'scientific research, laboratory, innovation, technology',
        events: 'conference, technology event, professional presentation',
        frontier: 'cutting edge, futuristic technology, advanced systems',
        insight: 'data analytics, business intelligence, professional workspace',
        misc: 'technology innovation, professional, modern design'
      };
      
      const categoryPrompt = categoryPrompts[category as keyof typeof categoryPrompts] || categoryPrompts.misc;
      parts.push(categoryPrompt);
    }

    // 添加技术相关的通用描述
    parts.push('high-tech, aerospace, defense, professional photography style, clean composition');

    return parts.join(', ');
  }

  /**
   * 从Unsplash获取图片
   */
  private async generateUnsplashImage(keyword: string): Promise<string> {
    const searchTerms = [keyword, 'aerospace technology', 'defense systems', 'industrial equipment'];

    for (const term of searchTerms) {
      try {
        const response = await fetch(
          `https://api.unsplash.com/search/photos?query=${encodeURIComponent(term)}&per_page=1&orientation=landscape`,
          {
            headers: {
              'Authorization': `Client-ID ${this.unsplashApiKey}`
            }
          }
        );

        if (response.ok) {
          const data = await response.json();
          if (data.results && data.results.length > 0) {
            return `${data.results[0].urls.regular}?w=1200&h=675&fit=crop`;
          }
        }
      } catch (error) {
        await this.log('warn', `Failed to fetch Unsplash image for term "${term}"`, { error });
      }
    }

    throw new Error('No suitable images found on Unsplash');
  }

  /**
   * 获取回退图片 - 基于关键词生成动态URL
   */
  private getFallbackImage(keyword?: string, category?: string): string {
    // 优先使用环境变量配置的fallback图片
    if (process.env.FALLBACK_IMAGE_URL) {
      return process.env.FALLBACK_IMAGE_URL;
    }
    
    // 始终使用默认背景图片，确保稳定性
    return '/hero-bg.jpg';
  }

  /**
   * 保存文章文件
   */
  private async saveArticleFile(article: ArticleOutput, task: any, customOutputDir?: string): Promise<string> {
    const outputDir = customOutputDir || this.outputDir;
    const date = new Date().toISOString().split('T')[0];
    // 优先使用任务的targetCategory，确保路由映射正确
    const category = task.targetCategory || this.inferCategory(task.keyword);
    const categoryDir = join(outputDir, category);
    
    // 确保目录存在
    mkdirSync(categoryDir, { recursive: true });

    // 生成唯一的文件名，避免重复
    let filename = `${date}-${article.slug}.md`;
    let outputPath = join(categoryDir, filename);
    let counter = 1;
    
    // 检查文件是否已存在，如果存在则添加数字后缀
    while (existsSync(outputPath)) {
      const slugWithSuffix = `${article.slug}-${counter}`;
      filename = `${date}-${slugWithSuffix}.md`;
      outputPath = join(categoryDir, filename);
      counter++;
      
      // 防止无限循环，最多尝试100次
      if (counter > 100) {
        const timestamp = Date.now();
        filename = `${date}-${article.slug}-${timestamp}.md`;
        outputPath = join(categoryDir, filename);
        break;
      }
    }

    // 构建前置信息
    const frontMatter = {
      ...article.frontMatter,
      heroImage: article.imageUrl || this.getFallbackImage(task.keyword, task.targetCategory),
      relatedProducts: task.relatedProducts || []
    };

    // 生成Markdown内容
    const markdownContent = this.buildMarkdownContent(frontMatter, article.content);

    // 写入文件
    writeFileSync(outputPath, markdownContent, 'utf8');

    // 如果有后台图片任务，更新任务的文章路径
    const backgroundTaskId = (article as any).backgroundTaskId;
    if (backgroundTaskId) {
      // backgroundImageService.updateTaskArticlePath(backgroundTaskId, outputPath); // 暂时注释掉
      await this.log('info', 'Background task article path update skipped (service not available)', {
        taskId: backgroundTaskId,
        articlePath: outputPath
      });
    }

    await this.log('info', 'Article file saved', {
      path: outputPath,
      category,
      wordCount: article.metadata.wordCount,
      originalSlug: article.slug,
      finalFilename: filename,
      backgroundTaskId
    });

    return outputPath;
  }

  /**
   * 构建Markdown内容
   */
  private buildMarkdownContent(frontMatter: Record<string, any>, content: string): string {
    // 使用统一的frontmatter格式化函数
    const formattedFrontmatter = generateFrontmatter(frontMatter);

    return `${formattedFrontmatter}

${content}`;
  }

  // 工具方法
  private extractTitleFromHtml(html: string): string {
    const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
    if (titleMatch) {
      return titleMatch[1].trim();
    }

    const h1Match = html.match(/<h1[^>]*>([^<]+)<\/h1>/i);
    if (h1Match) {
      return h1Match[1].trim();
    }

    return '';
  }

  private extractContentFromHtml(html: string): string {
    let content = html
      .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '')
      .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '')
      .replace(/<[^>]+>/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();

    return content;
  }

  private generateFallbackTitle(keyword: string): string {
    const templates = [
      `Advanced ${keyword} Technology in Aerospace Applications`,
      `${keyword} Systems: Innovation in Defense Technology`,
      `Next-Generation ${keyword} Solutions for Industrial Applications`
    ];
    
    return templates[Math.floor(Math.random() * templates.length)];
  }

  private generateSlug(title: string): string {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .substring(0, 50)
      .replace(/^-+|-+$/g, '');
  }

  private inferCategory(keyword: string): string {
    const keywordLower = keyword.toLowerCase();
    
    if (keywordLower.includes('summit') || keywordLower.includes('conference') || keywordLower.includes('event')) {
      return 'events';
    }
    if (keywordLower.includes('industry') || keywordLower.includes('market') || keywordLower.includes('business') || keywordLower.includes('commercial')) {
      return 'industry';
    }
    if (keywordLower.includes('research') || keywordLower.includes('study') || keywordLower.includes('analysis')) {
      return 'research';
    }
    if (keywordLower.includes('frontier') || keywordLower.includes('breakthrough') || keywordLower.includes('advanced') || keywordLower.includes('next-generation')) {
      return 'frontier';
    }
    if (keywordLower.includes('insight') || keywordLower.includes('trend') || keywordLower.includes('outlook')) {
      return 'insight';
    }
    
    return 'misc';
  }

  private countWords(text: string): number {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  /**
   * 计算产品链接数量
   */
  private countProductLinks(content: string): number {
    const linkPattern = /\[([^\]]+)\]\([^)]+\)/g;
    const matches = content.match(linkPattern);
    return matches ? matches.length : 0;
  }

  /**
   * 生成智能的mock内容
   */
  private generateIntelligentMockContent(keyword: string, title: string, recommendedProducts: any[], sourceContent: string): string {
    const sections = [
      `# ${title}`,
      '',
      `The latest developments in ${keyword} technology are reshaping the aerospace and defense landscape. Recent advances have demonstrated significant potential for improving operational capabilities and strategic advantages.`,
      '',
      `## Key Developments`,
      '',
      `Industry experts highlight several breakthrough innovations in ${keyword} that are gaining attention from defense contractors and aerospace manufacturers. These technological advances represent a significant step forward in addressing current operational challenges.`,
      '',
      `## Technical Implications`,
      '',
      `The integration of ${keyword} technology offers numerous benefits including enhanced performance, improved reliability, and cost-effective solutions for complex operational requirements. Early adoption by leading organizations demonstrates the practical value of these innovations.`,
      '',
      `## Future Outlook`,
      '',
      `As ${keyword} technology continues to mature, we can expect to see broader adoption across various defense and aerospace applications. The ongoing research and development efforts suggest promising opportunities for further advancement in this field.`
    ];

    return sections.join('\n');
  }

  /**
   * 生成mock内容（用于测试或没有API密钥时）
   */
  private generateMockContent(data: {
    keyword: string;
    sourceContent: string;
    sourceTitle: string;
    sourceMetadata: Record<string, any>;
    relatedProducts: string[];
    taskId: string;
    targetCategory?: string;
  }): ArticleOutput {
    // 1. 智能产品推荐 - 即使在mock模式下也使用真实的产品推荐
    const recommendedProducts = productDataLoader.recommendProductsForContent(
      data.sourceContent,
      data.sourceTitle,
      5 // 推荐最多5个产品
    );

    // 2. 合并推荐产品和手动指定的产品
    const allRelatedProducts = [
      ...data.relatedProducts,
      ...recommendedProducts.map(p => p.id)
    ];

    const title = data.sourceTitle || this.generateFallbackTitle(data.keyword);
    const slug = this.generateSlug(title);
    // 使用任务的targetCategory，确保分类一致性
    const category = data.targetCategory || this.inferCategory(data.keyword);

    // 3. 生成智能的mock内容，自然融入产品推荐
    let mockContent = this.generateIntelligentMockContent(data.keyword, title, recommendedProducts, data.sourceContent);

    // 4. 如果自然融入不够，再进行后处理插入
    const currentLinkCount = this.countProductLinks(mockContent);
    if (currentLinkCount < Math.min(1, recommendedProducts.length)) {
      mockContent = productDataLoader.insertProductLinksInContent(
        mockContent,
        recommendedProducts,
        2 // 最多插入2个软链接
      );
    }

    const wordCount = this.countWords(mockContent);
    
    return {
      title,
      slug,
      content: mockContent,
      frontMatter: {
        title,
        description: `Latest developments and insights in ${data.keyword} for aerospace and defense applications.`,
        category,
        tags: [data.keyword, 'aerospace', 'defense', 'technology'],
        date: new Date().toISOString().split('T')[0],
        author: 'AI Content Generator',
        featured: false,
        relatedProducts: allRelatedProducts.slice(0, 5) // 限制为5个
      },
      metadata: {
        wordCount,
        readTime: Math.ceil(wordCount / 200),
        seoScore: 85,
        keywordDensity: 2.5
      }
    };
  }
}