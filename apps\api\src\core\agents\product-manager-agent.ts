import { z } from 'zod';
import { BaseAgent, type AgentContext } from './base-agent.js';
import { configRepository } from '../database/repositories.js';
import { readFileSync, writeFileSync, existsSync, mkdirSync } from 'fs';
import { join, dirname } from 'path';

// 产品数据模式
export const ProductSchema = z.object({
  id: z.string().min(1),
  name: z.string().min(1),
  description: z.string().min(1),
  image: z.string().url(),
  category: z.string(),
  site: z.string().default('main'),
  url: z.string(),
  priority: z.number().int().min(1).max(10).default(1)
});

export const SiteSchema = z.object({
  name: z.string().min(1),
  baseUrl: z.string().optional(),
  isExternal: z.boolean().default(true)
});

export const CategorySchema = z.object({
  name: z.string().min(1),
  color: z.enum(['blue', 'green', 'purple', 'orange', 'indigo', 'red', 'yellow']).default('blue')
});

// 产品管理输入schema
export const ProductManagerInputSchema = z.object({
  action: z.enum([
    'add_product', 'update_product', 'delete_product',
    'add_site', 'update_site', 'delete_site',
    'add_category', 'update_category', 'delete_category',
    'search_products', 'get_products', 'get_related_products',
    'validate_data', 'import_csv', 'export_csv',
    'backup_data', 'restore_data'
  ]),
  data: z.any().optional(),
  options: z.record(z.any()).optional()
});

export type ProductManagerInput = z.infer<typeof ProductManagerInputSchema>;

export type Product = z.infer<typeof ProductSchema>;
export type Site = z.infer<typeof SiteSchema>;
export type Category = z.infer<typeof CategorySchema>;

// 产品数据结构
export interface ProductData {
  products: Record<string, Product>;
  sites: Record<string, Site>;
  categories: Record<string, Category>;
}

/**
 * 产品管理Agent
 * 负责管理产品、站点和分类数据
 */
export class ProductManagerAgent extends BaseAgent {
  private dataPath: string = './src/data/products.json';
  private data: ProductData = { products: {}, sites: {}, categories: {} };
  
  constructor() {
    super({
      name: 'ProductManagerAgent',
      description: 'Manages products, sites, and categories data',
      version: '2.0.0',
      maxRetries: 2,
      timeout: 30000
    });
  }

  protected async onInitialize(): Promise<void> {
    // 从配置获取数据路径
    const customPath = await configRepository.get('products_data_path');
    if (customPath) {
      this.dataPath = customPath;
    }

    // 加载产品数据
    await this.loadData();
    
    await this.log('info', 'Product manager initialized', {
      dataPath: this.dataPath,
      productsCount: Object.keys(this.data.products).length,
      sitesCount: Object.keys(this.data.sites).length,
      categoriesCount: Object.keys(this.data.categories).length
    });
  }

  protected async validateInput(input: any): Promise<any> {
    return ProductManagerInputSchema.parse(input);
  }

  protected async onExecute(input: any, context: AgentContext): Promise<any> {
    const validatedInput = input as ProductManagerInput;
    switch (input.action) {
      case 'add_product':
        return await this.addProduct(input.data);
      
      case 'update_product':
        return await this.updateProduct(input.data.id, input.data);
      
      case 'delete_product':
        return await this.deleteProduct(input.data.id);
      
      case 'add_site':
        return await this.addSite(input.data.id, input.data);
      
      case 'update_site':
        return await this.updateSite(input.data.id, input.data);
      
      case 'delete_site':
        return await this.deleteSite(input.data.id);
      
      case 'add_category':
        return await this.addCategory(input.data.id, input.data);
      
      case 'update_category':
        return await this.updateCategory(input.data.id, input.data);
      
      case 'delete_category':
        return await this.deleteCategory(input.data.id);
      
      case 'search_products':
        return await this.searchProducts(input.data.query, input.options);
      
      case 'get_products':
        return await this.getProducts(input.options);
      
      case 'get_related_products':
        return await this.getRelatedProducts(input.data.productIds, input.options);
      
      case 'validate_data':
        return await this.validateData();
      
      case 'import_csv':
        return await this.importFromCSV(input.data.csvPath, input.options);
      
      case 'export_csv':
        return await this.exportToCSV(input.data.outputPath, input.options);
      
      case 'backup_data':
        return await this.backupData(input.data.backupPath);
      
      case 'restore_data':
        return await this.restoreData(input.data.backupPath);
      
      default:
        throw new Error(`Unknown action: ${input.action}`);
    }
  }

  protected async onHealthCheck(): Promise<Record<string, any>> {
    const dataFileExists = existsSync(this.dataPath);
    const validationResult = await this.validateData();
    
    return {
      dataFileExists,
      dataPath: this.dataPath,
      productsCount: Object.keys(this.data.products).length,
      sitesCount: Object.keys(this.data.sites).length,
      categoriesCount: Object.keys(this.data.categories).length,
      dataValid: validationResult.isValid,
      lastModified: dataFileExists ? this.getFileLastModified() : null
    };
  }

  /**
   * 加载产品数据
   */
  private async loadData(): Promise<void> {
    try {
      if (existsSync(this.dataPath)) {
        const rawData = readFileSync(this.dataPath, 'utf8');
        this.data = JSON.parse(rawData);
        
        // 确保数据结构完整
        if (!this.data.products) this.data.products = {};
        if (!this.data.sites) this.data.sites = {};
        if (!this.data.categories) this.data.categories = {};
        
      } else {
        // 创建默认数据
        this.data = {
          products: {},
          sites: {
            main: {
              name: 'Main Site',
              baseUrl: '',
              isExternal: false
            }
          },
          categories: {
            technology: {
              name: 'Technology',
              color: 'blue'
            },
            events: {
              name: 'Events',
              color: 'green'
            },
            products: {
              name: 'Products',
              color: 'purple'
            },
            industry: {
              name: 'Industry',
              color: 'orange'
            }
          }
        };
        
        await this.saveData();
      }
      
    } catch (error) {
      await this.log('error', 'Failed to load product data', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw new Error('Failed to load product data');
    }
  }

  /**
   * 保存产品数据
   */
  private async saveData(): Promise<void> {
    try {
      // 确保目录存在
      const dir = dirname(this.dataPath);
      if (!existsSync(dir)) {
        mkdirSync(dir, { recursive: true });
      }

      writeFileSync(this.dataPath, JSON.stringify(this.data, null, 2), 'utf8');
      
      await this.log('info', 'Product data saved successfully', {
        path: this.dataPath
      });
      
    } catch (error) {
      await this.log('error', 'Failed to save product data', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw new Error('Failed to save product data');
    }
  }

  /**
   * 添加产品
   */
  async addProduct(productData: any): Promise<any> {
    const product = ProductSchema.parse(productData);
    
    if (this.data.products[product.id]) {
      throw new Error(`Product with ID '${product.id}' already exists`);
    }

    // 验证引用的站点和分类是否存在
    if (!this.data.sites[product.site]) {
      throw new Error(`Site '${product.site}' does not exist`);
    }
    
    if (!this.data.categories[product.category]) {
      throw new Error(`Category '${product.category}' does not exist`);
    }

    this.data.products[product.id] = product;
    await this.saveData();

    await this.log('info', 'Product added successfully', {
      productId: product.id,
      name: product.name,
      category: product.category
    });

    return {
      success: true,
      product,
      message: `Product '${product.name}' added successfully`
    };
  }

  /**
   * 更新产品
   */
  async updateProduct(productId: string, updates: any): Promise<any> {
    if (!this.data.products[productId]) {
      throw new Error(`Product with ID '${productId}' not found`);
    }

    const currentProduct = this.data.products[productId];
    const updatedProduct = ProductSchema.parse({ ...currentProduct, ...updates });

    // 验证引用的站点和分类是否存在
    if (updatedProduct.site && !this.data.sites[updatedProduct.site]) {
      throw new Error(`Site '${updatedProduct.site}' does not exist`);
    }
    
    if (updatedProduct.category && !this.data.categories[updatedProduct.category]) {
      throw new Error(`Category '${updatedProduct.category}' does not exist`);
    }

    this.data.products[productId] = updatedProduct;
    await this.saveData();

    await this.log('info', 'Product updated successfully', {
      productId,
      name: updatedProduct.name
    });

    return {
      success: true,
      product: updatedProduct,
      message: `Product '${updatedProduct.name}' updated successfully`
    };
  }

  /**
   * 删除产品
   */
  async deleteProduct(productId: string): Promise<any> {
    if (!this.data.products[productId]) {
      throw new Error(`Product with ID '${productId}' not found`);
    }

    const productName = this.data.products[productId].name;
    delete this.data.products[productId];
    await this.saveData();

    await this.log('info', 'Product deleted successfully', {
      productId,
      name: productName
    });

    return {
      success: true,
      message: `Product '${productName}' deleted successfully`
    };
  }

  /**
   * 添加站点
   */
  async addSite(siteId: string, siteData: any): Promise<any> {
    const site = SiteSchema.parse(siteData);
    
    if (this.data.sites[siteId]) {
      throw new Error(`Site with ID '${siteId}' already exists`);
    }

    this.data.sites[siteId] = site;
    await this.saveData();

    await this.log('info', 'Site added successfully', {
      siteId,
      name: site.name
    });

    return {
      success: true,
      site: { id: siteId, ...site },
      message: `Site '${site.name}' added successfully`
    };
  }

  /**
   * 更新站点
   */
  async updateSite(siteId: string, updates: any): Promise<any> {
    if (!this.data.sites[siteId]) {
      throw new Error(`Site with ID '${siteId}' not found`);
    }

    const currentSite = this.data.sites[siteId];
    const updatedSite = SiteSchema.parse({ ...currentSite, ...updates });

    this.data.sites[siteId] = updatedSite;
    await this.saveData();

    await this.log('info', 'Site updated successfully', {
      siteId,
      name: updatedSite.name
    });

    return {
      success: true,
      site: { id: siteId, ...updatedSite },
      message: `Site '${updatedSite.name}' updated successfully`
    };
  }

  /**
   * 删除站点
   */
  async deleteSite(siteId: string): Promise<any> {
    if (!this.data.sites[siteId]) {
      throw new Error(`Site with ID '${siteId}' not found`);
    }

    // 检查是否有产品使用此站点
    const usingProducts = Object.entries(this.data.products)
      .filter(([_, product]) => product.site === siteId);

    if (usingProducts.length > 0) {
      throw new Error(`Cannot delete site '${siteId}'. It is used by ${usingProducts.length} product(s)`);
    }

    const siteName = this.data.sites[siteId].name;
    delete this.data.sites[siteId];
    await this.saveData();

    await this.log('info', 'Site deleted successfully', {
      siteId,
      name: siteName
    });

    return {
      success: true,
      message: `Site '${siteName}' deleted successfully`
    };
  }

  /**
   * 添加分类
   */
  async addCategory(categoryId: string, categoryData: any): Promise<any> {
    const category = CategorySchema.parse(categoryData);
    
    if (this.data.categories[categoryId]) {
      throw new Error(`Category with ID '${categoryId}' already exists`);
    }

    this.data.categories[categoryId] = category;
    await this.saveData();

    await this.log('info', 'Category added successfully', {
      categoryId,
      name: category.name
    });

    return {
      success: true,
      category: { id: categoryId, ...category },
      message: `Category '${category.name}' added successfully`
    };
  }

  /**
   * 更新分类
   */
  async updateCategory(categoryId: string, updates: any): Promise<any> {
    if (!this.data.categories[categoryId]) {
      throw new Error(`Category with ID '${categoryId}' not found`);
    }

    const currentCategory = this.data.categories[categoryId];
    const updatedCategory = CategorySchema.parse({ ...currentCategory, ...updates });

    this.data.categories[categoryId] = updatedCategory;
    await this.saveData();

    await this.log('info', 'Category updated successfully', {
      categoryId,
      name: updatedCategory.name
    });

    return {
      success: true,
      category: { id: categoryId, ...updatedCategory },
      message: `Category '${updatedCategory.name}' updated successfully`
    };
  }

  /**
   * 删除分类
   */
  async deleteCategory(categoryId: string): Promise<any> {
    if (!this.data.categories[categoryId]) {
      throw new Error(`Category with ID '${categoryId}' not found`);
    }

    // 检查是否有产品使用此分类
    const usingProducts = Object.entries(this.data.products)
      .filter(([_, product]) => product.category === categoryId);

    if (usingProducts.length > 0) {
      throw new Error(`Cannot delete category '${categoryId}'. It is used by ${usingProducts.length} product(s)`);
    }

    const categoryName = this.data.categories[categoryId].name;
    delete this.data.categories[categoryId];
    await this.saveData();

    await this.log('info', 'Category deleted successfully', {
      categoryId,
      name: categoryName
    });

    return {
      success: true,
      message: `Category '${categoryName}' deleted successfully`
    };
  }

  /**
   * 搜索产品
   */
  async searchProducts(query: string, options: any = {}): Promise<any> {
    const lowerQuery = query.toLowerCase();
    const limit = options.limit || 50;
    const category = options.category;
    const site = options.site;

    let results = Object.entries(this.data.products).filter(([id, product]) => {
      // 文本搜索
      const textMatch = product.name.toLowerCase().includes(lowerQuery) ||
                       product.description.toLowerCase().includes(lowerQuery) ||
                       id.toLowerCase().includes(lowerQuery);

      // 分类过滤
      const categoryMatch = !category || product.category === category;
      
      // 站点过滤
      const siteMatch = !site || product.site === site;

      return textMatch && categoryMatch && siteMatch;
    });

    // 按优先级排序
    results.sort(([, a], [, b]) => a.priority - b.priority);

    // 限制结果数量
    if (limit > 0) {
      results = results.slice(0, limit);
    }

    await this.log('info', 'Product search completed', {
      query,
      resultsCount: results.length,
      options
    });

    return {
      query,
      total: results.length,
      products: results.map(([id, product]) => ({ ...product, id }))
    };
  }

  /**
   * 获取产品列表
   */
  async getProducts(options: any = {}): Promise<any> {
    const category = options.category;
    const site = options.site;
    const limit = options.limit || 0;
    const sortBy = options.sortBy || 'priority'; // priority, name, id
    const sortOrder = options.sortOrder || 'asc';

    let products = Object.entries(this.data.products);

    // 过滤
    if (category) {
      products = products.filter(([, product]) => product.category === category);
    }
    
    if (site) {
      products = products.filter(([, product]) => product.site === site);
    }

    // 排序
    products.sort(([idA, productA], [idB, productB]) => {
      let aValue: any, bValue: any;
      
      switch (sortBy) {
        case 'name':
          aValue = productA.name;
          bValue = productB.name;
          break;
        case 'id':
          aValue = idA;
          bValue = idB;
          break;
        case 'priority':
        default:
          aValue = productA.priority;
          bValue = productB.priority;
          break;
      }

      if (sortOrder === 'desc') {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      } else {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      }
    });

    // 限制数量
    if (limit > 0) {
      products = products.slice(0, limit);
    }

    return {
      total: products.length,
      products: products.map(([id, product]) => ({ ...product, id })),
      options
    };
  }

  /**
   * 获取相关产品
   */
  async getRelatedProducts(productIds: string[], options: any = {}): Promise<any> {
    const maxProducts = options.maxProducts || 5;
    const excludeIds = new Set(productIds);

    const relatedProducts = [];

    // 获取指定产品的分类
    const categories = new Set();
    for (const productId of productIds) {
      const product = this.data.products[productId];
      if (product) {
        categories.add(product.category);
      }
    }

    // 根据相同分类查找相关产品
    for (const [id, product] of Object.entries(this.data.products)) {
      if (excludeIds.has(id)) continue;
      
      if (categories.has(product.category)) {
        relatedProducts.push({ ...product, id });
      }
    }

    // 按优先级排序并限制数量
    relatedProducts.sort((a, b) => a.priority - b.priority);
    
    return {
      inputProductIds: productIds,
      total: relatedProducts.length,
      products: relatedProducts.slice(0, maxProducts)
    };
  }

  /**
   * 验证数据完整性
   */
  async validateData(): Promise<any> {
    const issues: string[] = [];

    // 验证产品
    for (const [id, product] of Object.entries(this.data.products)) {
      try {
        ProductSchema.parse(product);
        
        // 检查引用的站点和分类是否存在
        if (!this.data.sites[product.site]) {
          issues.push(`Product '${id}' references unknown site: ${product.site}`);
        }
        
        if (!this.data.categories[product.category]) {
          issues.push(`Product '${id}' references unknown category: ${product.category}`);
        }
        
      } catch (error) {
        issues.push(`Product '${id}' has invalid data: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    // 验证站点
    for (const [id, site] of Object.entries(this.data.sites)) {
      try {
        SiteSchema.parse(site);
      } catch (error) {
        issues.push(`Site '${id}' has invalid data: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    // 验证分类
    for (const [id, category] of Object.entries(this.data.categories)) {
      try {
        CategorySchema.parse(category);
      } catch (error) {
        issues.push(`Category '${id}' has invalid data: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    const isValid = issues.length === 0;

    await this.log(isValid ? 'info' : 'warn', 'Data validation completed', {
      isValid,
      issuesCount: issues.length
    });

    return {
      isValid,
      issuesCount: issues.length,
      issues
    };
  }

  /**
   * 从CSV导入产品
   */
  async importFromCSV(csvPath: string, options: any = {}): Promise<any> {
    if (!existsSync(csvPath)) {
      throw new Error(`CSV file not found: ${csvPath}`);
    }

    try {
      const csvContent = readFileSync(csvPath, 'utf8');
      const lines = csvContent.split('\n').filter(line => line.trim());
      
      if (lines.length < 2) {
        throw new Error('CSV file must have at least a header and one data row');
      }

      const headers = lines[0].split(',').map(h => h.trim());
      const imported = [];
      const errors = [];

      for (let i = 1; i < lines.length; i++) {
        const values = lines[i].split(',').map(v => v.trim());
        const productData: any = {};
        
        headers.forEach((header, index) => {
          productData[header] = values[index] || '';
        });

        try {
          // 转换数据类型
          if (productData.priority) {
            productData.priority = parseInt(productData.priority) || 1;
          }

          const result = await this.addProduct(productData);
          imported.push(result.product);

        } catch (error) {
          errors.push({
            line: i + 1,
            data: productData,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

      await this.log('info', 'CSV import completed', {
        csvPath,
        imported: imported.length,
        errors: errors.length
      });

      return {
        imported: imported.length,
        errors: errors.length,
        importedProducts: imported,
        errorDetails: errors
      };

    } catch (error) {
      throw new Error(`Failed to import CSV: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 导出产品到CSV
   */
  async exportToCSV(outputPath: string, options: any = {}): Promise<any> {
    const includeCategories = options.includeCategories || false;
    const includeSites = options.includeSites || false;

    try {
      const products = Object.entries(this.data.products).map(([id, product]) => ({ id, ...product }));
      
      if (products.length === 0) {
        throw new Error('No products to export');
      }

      // 生成CSV头部
      const headers = ['id', 'name', 'description', 'image', 'category', 'site', 'url', 'priority'];
      let csvContent = headers.join(',') + '\n';

      // 添加产品数据
      for (const product of products) {
        const row = headers.map(header => {
          const value = product[header as keyof typeof product];
          return typeof value === 'string' && value.includes(',') ? `"${value}"` : value;
        });
        csvContent += row.join(',') + '\n';
      }

      // 确保输出目录存在
      const dir = dirname(outputPath);
      if (!existsSync(dir)) {
        mkdirSync(dir, { recursive: true });
      }

      writeFileSync(outputPath, csvContent, 'utf8');

      await this.log('info', 'CSV export completed', {
        outputPath,
        productsExported: products.length
      });

      return {
        success: true,
        outputPath,
        productsExported: products.length,
        message: `Exported ${products.length} products to ${outputPath}`
      };

    } catch (error) {
      throw new Error(`Failed to export CSV: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 备份数据
   */
  async backupData(backupPath?: string): Promise<any> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const defaultBackupPath = `./backups/products-backup-${timestamp}.json`;
    const finalBackupPath = backupPath || defaultBackupPath;

    try {
      // 确保备份目录存在
      const dir = dirname(finalBackupPath);
      if (!existsSync(dir)) {
        mkdirSync(dir, { recursive: true });
      }

      // 创建备份数据
      const backupData = {
        timestamp: new Date().toISOString(),
        version: '2.0.0',
        data: this.data
      };

      writeFileSync(finalBackupPath, JSON.stringify(backupData, null, 2), 'utf8');

      await this.log('info', 'Data backup completed', {
        backupPath: finalBackupPath
      });

      return {
        success: true,
        backupPath: finalBackupPath,
        timestamp,
        message: `Data backed up to ${finalBackupPath}`
      };

    } catch (error) {
      throw new Error(`Failed to backup data: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 恢复数据
   */
  async restoreData(backupPath: string): Promise<any> {
    if (!existsSync(backupPath)) {
      throw new Error(`Backup file not found: ${backupPath}`);
    }

    try {
      const backupContent = readFileSync(backupPath, 'utf8');
      const backupData = JSON.parse(backupContent);

      if (!backupData.data) {
        throw new Error('Invalid backup file format');
      }

      // 验证备份数据
      const validationResult = await this.validateDataStructure(backupData.data);
      if (!validationResult.isValid) {
        throw new Error(`Invalid backup data: ${validationResult.issues.join(', ')}`);
      }

      // 创建当前数据的备份
      await this.backupData();

      // 恢复数据
      this.data = backupData.data;
      await this.saveData();

      await this.log('info', 'Data restored successfully', {
        backupPath,
        restoredFrom: backupData.timestamp
      });

      return {
        success: true,
        restoredFrom: backupData.timestamp,
        message: `Data restored from ${backupPath}`
      };

    } catch (error) {
      throw new Error(`Failed to restore data: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 验证数据结构
   */
  private async validateDataStructure(data: any): Promise<{ isValid: boolean; issues: string[] }> {
    const issues: string[] = [];

    if (!data.products || typeof data.products !== 'object') {
      issues.push('Missing or invalid products object');
    }

    if (!data.sites || typeof data.sites !== 'object') {
      issues.push('Missing or invalid sites object');
    }

    if (!data.categories || typeof data.categories !== 'object') {
      issues.push('Missing or invalid categories object');
    }

    return {
      isValid: issues.length === 0,
      issues
    };
  }

  /**
   * 获取文件最后修改时间
   */
  private getFileLastModified(): string | null {
    try {
      const stats = require('fs').statSync(this.dataPath);
      return stats.mtime.toISOString();
    } catch {
      return null;
    }
  }

  // 公共接口方法
  
  /**
   * 获取产品详情
   */
  async getProduct(productId: string): Promise<Product | null> {
    return this.data.products[productId] || null;
  }

  /**
   * 获取所有站点
   */
  async getSites(): Promise<Record<string, Site>> {
    return this.data.sites;
  }

  /**
   * 获取所有分类
   */
  async getCategories(): Promise<Record<string, Category>> {
    return this.data.categories;
  }

  /**
   * 获取产品的完整URL
   */
  async getProductUrl(product: Product): Promise<string> {
    const site = this.data.sites[product.site];
    
    if (!site) {
      return product.url;
    }

    if (site.isExternal && site.baseUrl) {
      return `${site.baseUrl}${product.url}`;
    }

    return product.url;
  }

  /**
   * 检查产品是否为外部链接
   */
  async isExternalProduct(product: Product): Promise<boolean> {
    const site = this.data.sites[product.site];
    return site ? site.isExternal : false;
  }
}