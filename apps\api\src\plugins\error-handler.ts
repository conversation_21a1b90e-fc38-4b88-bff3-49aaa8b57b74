import type { FastifyInstance } from 'fastify';
import fp from 'fastify-plugin';

// 临时使用基础类型，直到共享包正确导入
const ERROR_CODES = {
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  INTERNAL_ERROR: 'INTERNAL_ERROR'
};

const HTTP_STATUS = {
  BAD_REQUEST: 400,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500
};

function createErrorResponse(code: string, message: string, details?: any) {
  return {
    success: false,
    error: { code, message, details },
    meta: { timestamp: new Date().toISOString() }
  };
}

async function errorHandlerPlugin(fastify: FastifyInstance) {
  // 设置错误处理器
  fastify.setErrorHandler(async (error, request, reply) => {
    const { log } = request;

    // 记录错误
    log.error(error, 'Unhandled error occurred');

    // Validation错误 (来自schema validation)
    if (error.validation) {
      const response = createErrorResponse(
        ERROR_CODES.VALIDATION_ERROR,
        'Validation failed',
        error.validation
      );
      return reply.code(HTTP_STATUS.BAD_REQUEST).send(response);
    }

    // Rate limit错误
    if (error.statusCode === 429) {
      const response = createErrorResponse(
        ERROR_CODES.RATE_LIMIT_EXCEEDED,
        'Too many requests'
      );
      return reply.code(429).send(response);
    }

    // 已知的HTTP错误
    if (error.statusCode && error.statusCode < 500) {
      const response = createErrorResponse(
        error.code || 'CLIENT_ERROR',
        error.message
      );
      return reply.code(error.statusCode).send(response);
    }

    // 服务器内部错误
    const response = createErrorResponse(
      ERROR_CODES.INTERNAL_ERROR,
      'Internal server error'
    );
    
    return reply.code(HTTP_STATUS.INTERNAL_SERVER_ERROR).send(response);
  });

  // 404处理
  fastify.setNotFoundHandler(async (request, reply) => {
    const response = createErrorResponse(
      'NOT_FOUND',
      `Route ${request.method} ${request.url} not found`
    );
    
    return reply.code(HTTP_STATUS.NOT_FOUND).send(response);
  });
}

export default fp(errorHandlerPlugin, {
  name: 'error-handler'
}); 