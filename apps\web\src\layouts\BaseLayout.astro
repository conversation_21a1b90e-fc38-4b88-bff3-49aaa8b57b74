---
import '../styles/global.css';

export interface Props {
  title: string;
  description?: string;
  image?: string;
  canonicalURL?: string;
}

const { title, description, image, canonicalURL } = Astro.props;
const defaultDescription = "Leading source for aerospace and defense industry news, analysis, and insights. Stay informed with the latest developments in aviation, space exploration, and defense technologies.";

// Generate canonical URL
const canonical = canonicalURL || new URL(Astro.url.pathname, Astro.site).toString();

// Generate Open Graph image
const ogImage = image ? new URL(image, Astro.site).toString() : new URL('/og-default.jpg', Astro.site).toString();
---

<!doctype html>
<html lang="en" class="scroll-smooth">
  <head>
    <meta charset="UTF-8" />
    <meta name="description" content={description || defaultDescription} />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    
    <!-- Favicon - 使用版本号强制刷新缓存 -->
    <link rel="icon" type="image/svg+xml" href="/favicon.svg?v=5" />
    <link rel="icon" type="image/x-icon" href="/favicon.svg?v=5" />
    <link rel="shortcut icon" href="/favicon.svg?v=5" />
    <link rel="apple-touch-icon" href="/favicon.svg?v=5" />
    <meta name="msapplication-TileColor" content="#2563eb" />
    
    <meta name="generator" content={Astro.generator} />
    
    <!-- Canonical URL -->
    <link rel="canonical" href={canonical} />
    
    <!-- SEO Meta Tags -->
    <title>{title}</title>
    <meta name="robots" content="index, follow" />
    <meta name="author" content="Aerospace & Defense News" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content={canonical} />
    <meta property="og:title" content={title} />
    <meta property="og:description" content={description || defaultDescription} />
    <meta property="og:image" content={ogImage} />
    <meta property="og:site_name" content="Aerospace & Defense News" />
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content={canonical} />
    <meta property="twitter:title" content={title} />
    <meta property="twitter:description" content={description || defaultDescription} />
    <meta property="twitter:image" content={ogImage} />
    
    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link rel="preconnect" href="https://plausible.io" />
    
    <!-- Analytics - Plausible -->
    <script defer data-domain="aerospace-defense-news.vercel.app" src="https://plausible.io/js/script.js" is:inline></script>
    
    <!-- JSON-LD Structured Data -->
    <script type="application/ld+json" is:inline>
      {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "Aerospace & Defense News",
        "description": "Leading source for aerospace and defense industry news and analysis",
        "url": "https://aerospace-defense-news.vercel.app",
        "publisher": {
          "@type": "Organization",
          "name": "Aerospace & Defense News",
          "logo": {
            "@type": "ImageObject",
            "url": "https://aerospace-defense-news.vercel.app/favicon.svg"
          }
        },
        "potentialAction": {
          "@type": "SearchAction",
          "target": "https://aerospace-defense-news.vercel.app/search?q={search_term_string}",
          "query-input": "required name=search_term_string"
        }
      }
    </script>
    
    <!-- Theme color -->
    <meta name="theme-color" content="#2563eb" />
    
    <!-- PWA Manifest -->
    <link rel="manifest" href="/site.webmanifest" />
  </head>
  
  <body class="bg-secondary-50 text-secondary-900 antialiased min-h-screen flex flex-col">
    <slot />
    
    <!-- Alpine.js for interactive components -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" is:inline></script>
    
    <!-- Global error handling -->
    <script is:inline>
      window.addEventListener('error', function(e) {
        console.error('Global error:', e.error);
        // You can send errors to your analytics service here
      });
    </script>
  </body>
</html> 