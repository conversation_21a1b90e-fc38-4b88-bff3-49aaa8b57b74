# n8n工作流配置指南

本文档介绍如何在n8n中集成Plan Generator服务，实现自动化内容计划生成。

## 🎯 集成方案

### 方案1：HTTP API调用（推荐）

使用n8n的HTTP Request节点调用Plan Generator API。

#### 工作流配置

1. **Trigger节点** - 定时触发
   ```json
   {
     "trigger": "schedule",
     "cron": "0 9 * * *",
     "timezone": "Asia/Shanghai"
   }
   ```

2. **HTTP Request节点** - 调用生成计划API
   ```json
   {
     "method": "POST",
     "url": "http://content-generator:3002/api/plan/generate",
     "headers": {
       "Content-Type": "application/json"
     },
     "body": {
       "maxKeywords": 10,
       "categories": ["industry", "research", "events", "frontier"]
     }
   }
   ```

   或者调用任务追加API：
   ```json
   {
     "method": "POST",
     "url": "http://content-generator:3002/api/tasks/append",
     "headers": {
       "Content-Type": "application/json"
     },
     "body": [
       {
         "id": "custom-task-001",
         "title": "Custom Task Title",
         "description": "Task description",
         "category": "industry",
         "priority": 4
       }
     ]
   }
   ```

3. **Code节点** - 处理响应数据
   ```javascript
   // 处理计划生成结果
   const result = $input.first().json;
   
   if (result.success) {
     console.log(`✅ Generated ${result.data.tasksCount} tasks`);
     console.log(`🎯 High priority: ${result.data.highPriorityCount}`);
     
     // 过滤高优先级任务
     const highPriorityTasks = result.data.tasks.filter(task => task.priority >= 4);
     
     return {
       json: {
         success: true,
         totalTasks: result.data.tasksCount,
         highPriorityTasks: highPriorityTasks,
         nextGeneration: result.data.nextGeneration,
         timestamp: new Date().toISOString()
       }
     };
   } else {
     throw new Error(`Plan generation failed: ${result.error.message}`);
   }
   ```

4. **条件节点** - 检查是否有高优先级任务
   ```json
   {
     "conditions": {
       "boolean": {
         "value1": "={{$json.highPriorityTasks.length}}",
         "operation": "larger",
         "value2": 0
       }
     }
   }
   ```

5. **通知节点** - 发送结果通知（可选）
   ```javascript
   // 发送Slack/邮件通知
   const tasks = $json.highPriorityTasks;
   const message = `🎯 新的高优先级任务生成:
   
   ${tasks.map((task, index) => 
     `${index + 1}. [${task.category.toUpperCase()}] ${task.title}
        截止: ${task.deadline}
        优先级: ${task.priority}`
   ).join('\n\n')}
   
   📅 下次生成: ${$json.nextGeneration}`;
   
   return { json: { message } };
   ```

### 方案2：命令行执行

使用n8n的Execute Command节点直接执行脚本。

#### 节点配置

```json
{
  "command": "cd /app && npm run plan:generate",
  "workingDirectory": "/app"
}
```

## 🔄 完整工作流示例

### 每日自动生成计划

```json
{
  "name": "Daily Content Plan Generation",
  "nodes": [
    {
      "parameters": {
        "rule": {
          "interval": [
            {
              "field": "cronExpression",
              "expression": "0 9 * * *"
            }
          ]
        }
      },
      "name": "Schedule Trigger",
      "type": "n8n-nodes-base.scheduleTrigger",
      "typeVersion": 1,
      "position": [240, 300]
    },
    {
      "parameters": {
        "url": "http://content-generator:3002/api/plan/generate",
        "options": {
          "response": {
            "response": {
              "responseFormat": "json"
            }
          }
        },
        "requestMethod": "POST",
        "jsonParameters": true,
        "bodyParametersJson": "{\n  \"maxKeywords\": 10,\n  \"categories\": [\"industry\", \"research\", \"events\", \"frontier\"]\n}"
      },
      "name": "Generate Plan",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 3,
      "position": [460, 300]
    },
    {
      "parameters": {
        "jsCode": "// 处理计划生成结果\nconst result = $input.first().json;\n\nif (result.success) {\n  const highPriorityTasks = result.data.tasks.filter(task => task.priority >= 4);\n  \n  return {\n    json: {\n      success: true,\n      totalTasks: result.data.tasksCount,\n      highPriorityTasks: highPriorityTasks,\n      allTasks: result.data.tasks,\n      nextGeneration: result.data.nextGeneration\n    }\n  };\n} else {\n  throw new Error(`Plan generation failed: ${result.error.message}`);\n}"
      },
      "name": "Process Results",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [680, 300]
    },
    {
      "parameters": {
        "conditions": {
          "boolean": [
            {
              "value1": "={{$json.highPriorityTasks.length}}",
              "operation": "larger",
              "value2": 0
            }
          ]
        }
      },
      "name": "Has High Priority Tasks?",
      "type": "n8n-nodes-base.if",
      "typeVersion": 1,
      "position": [900, 300]
    },
    {
      "parameters": {
        "message": "=🎯 新的高优先级内容任务已生成!\n\n共生成 {{$json.totalTasks}} 个任务，其中 {{$json.highPriorityTasks.length}} 个高优先级。\n\n📅 下次生成时间: {{$json.nextGeneration}}",
        "options": {}
      },
      "name": "Send Notification",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 3,
      "position": [1120, 200]
    }
  ],
  "connections": {
    "Schedule Trigger": {
      "main": [
        [
          {
            "node": "Generate Plan",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Generate Plan": {
      "main": [
        [
          {
            "node": "Process Results",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Process Results": {
      "main": [
        [
          {
            "node": "Has High Priority Tasks?",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Has High Priority Tasks?": {
      "main": [
        [
          {
            "node": "Send Notification",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  }
}
```

## 🛠 API端点说明

### 1. 生成计划
- **URL**: `POST /api/plan/generate`
- **说明**: 生成新的内容计划
- **响应**: 包含任务列表、优先级统计等

### 2. 获取最新计划
- **URL**: `GET /api/plan/latest`
- **说明**: 获取最新生成的计划
- **响应**: 最新的计划文件内容

### 3. 计划列表
- **URL**: `GET /api/plans`
- **说明**: 获取所有历史计划
- **响应**: 计划文件列表

### 4. 追加任务
- **URL**: `POST /api/tasks/append`
- **说明**: 向今日计划追加新任务，支持去重
- **请求体**: 单个任务对象或任务数组
- **响应**: 写入和跳过的任务统计

### 5. 健康检查
- **URL**: `GET /health`
- **说明**: 服务健康状态检查
- **响应**: 服务状态信息

## 🚀 使用步骤

### 1. 启动API服务

```bash
# 在容器中或本地启动
npm run plan:api
```

服务将在端口3002上运行。

### 2. 在n8n中创建工作流

1. 登录n8n界面：http://localhost:5678
2. 创建新工作流
3. 添加上述节点配置
4. 配置API请求URL为容器内地址：`http://content-generator:3002`

### 3. 测试工作流

1. 手动触发工作流
2. 检查是否成功生成计划
3. 验证通知是否发送

## 📋 最佳实践

1. **错误处理**: 在工作流中添加错误处理节点
2. **重试机制**: 配置HTTP请求的重试策略
3. **日志记录**: 使用Code节点记录执行日志
4. **监控告警**: 设置失败时的通知机制
5. **数据验证**: 验证API响应的数据完整性

## 🔧 故障排除

### 常见问题

1. **连接失败**: 检查容器网络配置
2. **API响应错误**: 查看content-generator服务日志
3. **定时任务不执行**: 检查cron表达式和时区设置

### 调试命令

```bash
# 检查API服务状态
curl http://localhost:3002/health

# 手动测试API
curl -X POST http://localhost:3002/api/plan/generate \
  -H "Content-Type: application/json" \
  -d '{}'

# 测试任务追加
curl -X POST http://localhost:3002/api/tasks/append \
  -H "Content-Type: application/json" \
  -d '[{"id":"test-001","title":"Test Task","category":"test"}]'

# 查看服务日志
docker-compose logs content-generator
``` 