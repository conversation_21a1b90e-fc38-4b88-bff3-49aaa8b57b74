#!/usr/bin/env node

import { execSync, spawn } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { checkImageUrl, extractImageUrls, scanMarkdownFiles } from './check-image-links.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const CONFIG = {
  projectRoot: path.resolve(__dirname, '..'),
  contentDir: path.resolve(__dirname, '../apps/web/src/content/news'),
  reportsDir: path.resolve(__dirname, '../reports'),
  buildDir: path.resolve(__dirname, '../apps/web/dist'),
  enableImageCheck: true,
  enableContentValidation: true,
  enableIncrementalDeploy: true,
  autoFix: true
};

/**
 * 记录日志
 */
function log(level, message, data = {}) {
  const timestamp = new Date().toISOString();
  const prefix = {
    info: '📝',
    warn: '⚠️',
    error: '❌',
    success: '✅'
  }[level] || 'ℹ️';
  
  console.log(`${prefix} [${timestamp}] ${message}`);
  if (Object.keys(data).length > 0) {
    console.log('   ', JSON.stringify(data, null, 2));
  }
}

/**
 * 执行命令并返回结果
 */
function executeCommand(command, options = {}) {
  try {
    log('info', `Executing: ${command}`);
    const result = execSync(command, { 
      encoding: 'utf8', 
      cwd: CONFIG.projectRoot,
      ...options 
    });
    return { success: true, output: result.trim() };
  } catch (error) {
    return { 
      success: false, 
      error: error.message, 
      output: error.stdout?.trim() || '',
      stderr: error.stderr?.trim() || ''
    };
  }
}

/**
 * 检查项目健康状态
 */
async function checkProjectHealth() {
  log('info', '🔍 Checking project health...');
  
  const checks = {
    nodeModules: fs.existsSync(path.join(CONFIG.projectRoot, 'node_modules')),
    packageJson: fs.existsSync(path.join(CONFIG.projectRoot, 'package.json')),
    webApp: fs.existsSync(path.join(CONFIG.projectRoot, 'apps/web')),
    apiApp: fs.existsSync(path.join(CONFIG.projectRoot, 'apps/api')),
    contentDir: fs.existsSync(CONFIG.contentDir)
  };
  
  const failedChecks = Object.entries(checks)
    .filter(([_, passed]) => !passed)
    .map(([check]) => check);
  
  if (failedChecks.length > 0) {
    log('error', 'Project health check failed', { failedChecks });
    return false;
  }
  
  log('success', 'Project health check passed');
  return true;
}

/**
 * 运行图片链接检查
 */
async function runImageLinkCheck() {
  if (!CONFIG.enableImageCheck) {
    log('info', 'Image link check disabled, skipping...');
    return { success: true, brokenLinks: 0 };
  }
  
  log('info', '🖼️ Running image link check...');
  
  try {
    // 扫描Markdown文件
    const markdownFiles = scanMarkdownFiles(CONFIG.contentDir);
    if (markdownFiles.length === 0) {
      log('warn', 'No Markdown files found');
      return { success: true, brokenLinks: 0 };
    }
    
    // 提取图片链接
    const allImages = [];
    for (const filePath of markdownFiles) {
      const content = fs.readFileSync(filePath, 'utf8');
      const images = extractImageUrls(content, filePath);
      allImages.push(...images);
    }
    
    if (allImages.length === 0) {
      log('info', 'No images found to check');
      return { success: true, brokenLinks: 0 };
    }
    
    log('info', `Found ${allImages.length} image references in ${markdownFiles.length} files`);
    
    // 检查链接有效性
    const results = [];
    for (const image of allImages) {
      const checkResult = await checkImageUrl(image.url);
      results.push({ ...image, ...checkResult });
    }
    
    const brokenLinks = results.filter(r => !r.valid);
    
    if (brokenLinks.length > 0) {
      log('warn', `Found ${brokenLinks.length} broken image links`);
      
      if (CONFIG.autoFix) {
        log('info', 'Auto-fixing broken links...');
        // 这里可以调用 check-image-links.js 的修复功能
        const fixCommand = `node scripts/check-image-links.js --fix`;
        const fixResult = executeCommand(fixCommand);
        
        if (fixResult.success) {
          log('success', 'Broken links fixed automatically');
          return { success: true, brokenLinks: 0, fixed: brokenLinks.length };
        } else {
          log('error', 'Failed to fix broken links', { error: fixResult.error });
          return { success: false, brokenLinks: brokenLinks.length };
        }
      } else {
        log('error', 'Broken links found but auto-fix disabled');
        return { success: false, brokenLinks: brokenLinks.length };
      }
    } else {
      log('success', 'All image links are valid');
      return { success: true, brokenLinks: 0 };
    }
    
  } catch (error) {
    log('error', 'Image link check failed', { error: error.message });
    return { success: false, error: error.message };
  }
}

/**
 * 验证内容质量
 */
async function validateContent() {
  if (!CONFIG.enableContentValidation) {
    log('info', 'Content validation disabled, skipping...');
    return { success: true };
  }
  
  log('info', '📄 Validating content quality...');
  
  try {
    // 检查 Astro 项目
    const astroCheckResult = executeCommand('cd apps/web && pnpm astro check');
    
    if (!astroCheckResult.success) {
      log('error', 'Astro content validation failed', {
        error: astroCheckResult.error,
        stderr: astroCheckResult.stderr
      });
      return { success: false, error: 'Astro validation failed' };
    }
    
    log('success', 'Content validation passed');
    return { success: true };
    
  } catch (error) {
    log('error', 'Content validation error', { error: error.message });
    return { success: false, error: error.message };
  }
}

/**
 * 执行增量构建
 */
async function incrementalBuild() {
  log('info', '🔨 Running incremental build...');
  
  try {
    // 检查是否需要构建
    const lastBuildTime = getBuildTimestamp();
    const lastContentChange = getLastContentChangeTime();
    
    if (lastBuildTime && lastContentChange <= lastBuildTime) {
      log('info', 'No content changes detected, skipping build');
      return { success: true, skipped: true };
    }
    
    // 执行构建
    const buildResult = executeCommand('cd apps/web && pnpm build');
    
    if (!buildResult.success) {
      log('error', 'Build failed', {
        error: buildResult.error,
        stderr: buildResult.stderr
      });
      return { success: false, error: 'Build failed' };
    }
    
    // 更新构建时间戳
    setBuildTimestamp();
    
    log('success', 'Incremental build completed');
    return { success: true };
    
  } catch (error) {
    log('error', 'Build error', { error: error.message });
    return { success: false, error: error.message };
  }
}

/**
 * 获取构建时间戳
 */
function getBuildTimestamp() {
  const timestampFile = path.join(CONFIG.reportsDir, 'last-build.timestamp');
  if (fs.existsSync(timestampFile)) {
    return parseInt(fs.readFileSync(timestampFile, 'utf8'));
  }
  return null;
}

/**
 * 设置构建时间戳
 */
function setBuildTimestamp() {
  if (!fs.existsSync(CONFIG.reportsDir)) {
    fs.mkdirSync(CONFIG.reportsDir, { recursive: true });
  }
  const timestampFile = path.join(CONFIG.reportsDir, 'last-build.timestamp');
  fs.writeFileSync(timestampFile, Date.now().toString());
}

/**
 * 获取最后内容更改时间
 */
function getLastContentChangeTime() {
  try {
    const markdownFiles = scanMarkdownFiles(CONFIG.contentDir);
    let latestTime = 0;
    
    for (const file of markdownFiles) {
      const stat = fs.statSync(file);
      latestTime = Math.max(latestTime, stat.mtime.getTime());
    }
    
    return latestTime;
  } catch (error) {
    log('warn', 'Failed to get content change time', { error: error.message });
    return Date.now(); // 假设有变化
  }
}

/**
 * 生成部署报告
 */
function generateDeployReport(results) {
  const report = {
    timestamp: new Date().toISOString(),
    results,
    summary: {
      success: results.every(r => r.success),
      totalSteps: results.length,
      successfulSteps: results.filter(r => r.success).length,
      failedSteps: results.filter(r => !r.success).length
    }
  };
  
  const reportFile = path.join(CONFIG.reportsDir, 'deploy-report.json');
  if (!fs.existsSync(CONFIG.reportsDir)) {
    fs.mkdirSync(CONFIG.reportsDir, { recursive: true });
  }
  
  fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));
  log('info', `Deploy report saved to: ${reportFile}`);
  
  return report;
}

/**
 * 主要部署流程
 */
async function main() {
  console.log('🚀 Starting robust deployment process...\n');
  
  const startTime = Date.now();
  const results = [];
  
  try {
    // 1. 项目健康检查
    const healthCheck = await checkProjectHealth();
    results.push({ step: 'health-check', success: healthCheck });
    
    if (!healthCheck) {
      throw new Error('Project health check failed');
    }
    
    // 2. 图片链接检查
    const imageLinkCheck = await runImageLinkCheck();
    results.push({ step: 'image-link-check', ...imageLinkCheck });
    
    if (!imageLinkCheck.success) {
      throw new Error('Image link check failed');
    }
    
    // 3. 内容验证
    const contentValidation = await validateContent();
    results.push({ step: 'content-validation', ...contentValidation });
    
    if (!contentValidation.success) {
      throw new Error('Content validation failed');
    }
    
    // 4. 增量构建
    if (CONFIG.enableIncrementalDeploy) {
      const buildResult = await incrementalBuild();
      results.push({ step: 'incremental-build', ...buildResult });
      
      if (!buildResult.success) {
        throw new Error('Incremental build failed');
      }
    }
    
    // 生成报告
    const report = generateDeployReport(results);
    
    const duration = ((Date.now() - startTime) / 1000).toFixed(2);
    log('success', `Robust deployment completed successfully in ${duration}s`);
    
    console.log('\n📊 Deployment Summary:');
    results.forEach(result => {
      const status = result.success ? '✅' : '❌';
      console.log(`  ${status} ${result.step}`);
      if (result.brokenLinks !== undefined) {
        console.log(`     - Broken links: ${result.brokenLinks}`);
      }
      if (result.fixed !== undefined) {
        console.log(`     - Fixed links: ${result.fixed}`);
      }
      if (result.skipped) {
        console.log('     - Skipped (no changes)');
      }
    });
    
    process.exit(0);
    
  } catch (error) {
    log('error', 'Deployment failed', { error: error.message });
    
    // 生成失败报告
    const report = generateDeployReport(results);
    
    console.log('\n❌ Deployment failed. Check the report for details.');
    process.exit(1);
  }
}

// 处理命令行参数
if (process.argv.includes('--no-image-check')) {
  CONFIG.enableImageCheck = false;
}
if (process.argv.includes('--no-content-validation')) {
  CONFIG.enableContentValidation = false;
}
if (process.argv.includes('--no-incremental')) {
  CONFIG.enableIncrementalDeploy = false;
}
if (process.argv.includes('--no-auto-fix')) {
  CONFIG.autoFix = false;
}

// 运行主程序
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { checkProjectHealth, runImageLinkCheck, validateContent, incrementalBuild }; 