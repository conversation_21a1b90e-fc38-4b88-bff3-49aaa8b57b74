# API 文档

新闻平台API服务提供完整的内容生成、管理和自动化功能。

## 📖 概述

API服务运行在 `http://localhost:3002`，基于Express.js构建，提供RESTful接口。

### 基础信息
- **基础URL**: `http://localhost:3002`
- **API版本**: v1
- **内容类型**: `application/json`
- **认证方式**: API Key (可选)

## 🔗 核心端点

### 健康检查

#### GET /health
获取API服务健康状态

```bash
curl http://localhost:3002/health
```

**响应示例:**
```json
{
  "status": "ok",
  "uptime": 123456,
  "timestamp": "2024-01-20T10:30:00Z",
  "services": {
    "database": "connected",
    "storage": "available",
    "ai_providers": "active"
  }
}
```

## 📝 内容管理API (v1)

### 生成内容计划

#### POST /api/v1/plans
创建新的内容计划

**请求体:**
```json
{
  "date": "2024-01-20",
  "maxTasks": 5,
  "categories": ["industry", "research", "frontier"],
  "customPrompt": "Focus on aerospace innovations",
  "forceRegenerate": false
}
```

**参数说明:**
- `date`: 计划日期 (YYYY-MM-DD)
- `maxTasks`: 最大任务数量 (1-20)
- `categories`: 内容类别数组
- `customPrompt`: 自定义提示词 (可选)
- `forceRegenerate`: 强制重新生成 (可选)

**响应示例:**
```json
{
  "success": true,
  "data": {
    "plan": {
      "id": "plan_20240120",
      "date": "2024-01-20",
      "status": "generated"
    },
    "tasks": [
      {
        "id": "task_001",
        "keyword": "quantum radar technology",
        "category": "research",
        "priority": "high",
        "relatedProducts": ["radar-systems"]
      }
    ]
  }
}
```

### 生成单篇文章

#### POST /api/v1/content/generate
生成单篇新闻文章

**请求体:**
```json
{
  "taskId": "task_001",
  "keyword": "hypersonic aircraft development",
  "category": "industry",
  "relatedProducts": ["aircraft-systems", "propulsion"],
  "sourceUrl": "https://example.com/news-source",
  "generateImage": true,
  "dryRun": false
}
```

**参数说明:**
- `taskId`: 任务ID (必需)
- `keyword`: 关键词 (必需)
- `category`: 文章类别 (必需)
- `relatedProducts`: 相关产品数组 (可选)
- `sourceUrl`: 源文章URL (可选)
- `generateImage`: 是否生成图片 (默认: true)
- `dryRun`: 测试模式 (默认: false)

**响应示例:**
```json
{
  "success": true,
  "data": {
    "article": {
      "title": "Breakthrough in Hypersonic Aircraft Development",
      "content": "Full article content...",
      "heroImage": "/images/generated/article_123.webp",
      "metadata": {
        "wordCount": 1250,
        "readTime": 5,
        "seoScore": 92
      },
      "tags": ["hypersonic", "aircraft", "aerospace"]
    },
    "outputPath": "/content/news/industry/2024-01-20-hypersonic-aircraft.md"
  }
}
```

### 批量生成文章

#### POST /api/v1/content/batch
批量生成多篇文章

**请求体:**
```json
{
  "taskIds": ["task_001", "task_002", "task_003"],
  "generateImages": true,
  "dryRun": false,
  "maxConcurrent": 3
}
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "processed": 3,
    "successful": 2,
    "failed": 1,
    "results": [
      {
        "taskId": "task_001",
        "status": "success",
        "article": { /* 文章数据 */ }
      },
      {
        "taskId": "task_002", 
        "status": "error",
        "error": "Content generation failed"
      }
    ]
  }
}
```

### 获取内容列表

#### GET /api/v1/content
获取内容列表

**查询参数:**
- `status`: 状态过滤 (pending, completed, failed)
- `category`: 类别过滤
- `limit`: 限制数量 (默认: 20)
- `offset`: 偏移量 (默认: 0)

```bash
curl "http://localhost:3002/api/v1/content?status=completed&category=industry&limit=10"
```

**响应示例:**
```json
{
  "success": true,
  "data": [
    {
      "id": "task_001",
      "title": "Advanced Propulsion Systems",
      "status": "completed",
      "category": "industry",
      "createdAt": "2024-01-20T08:00:00Z",
      "updatedAt": "2024-01-20T08:30:00Z"
    }
  ],
  "pagination": {
    "total": 45,
    "limit": 10,
    "offset": 0,
    "hasMore": true
  }
}
```

## 🖼️ 图片处理API (v1)

### 生成图片

#### POST /api/v1/image/generate
生成文章配图

**请求体:**
```json
{
  "prompt": "Modern aerospace facility with advanced aircraft",
  "style": "realistic",
  "dimensions": {
    "width": 1200,
    "height": 675
  },
  "provider": "fal" // 或 "dalle"
}
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "imageUrl": "https://r2.cloudflare.com/.../generated_image.webp",
    "localPath": "/images/generated/img_123.webp",
    "metadata": {
      "provider": "fal",
      "model": "flux-pro",
      "generatedAt": "2024-01-20T10:30:00Z"
    }
  }
}
```

### 分析图片内容

#### POST /api/v1/image/analyze
分析图片内容和质量

**请求体:**
```json
{
  "imageUrl": "https://example.com/image.jpg",
  "includeAltText": true,
  "checkQuality": true
}
```

### 验证图片URL

#### GET /api/v1/image/validate
验证图片URL可用性

**查询参数:**
- `url`: 图片URL
- `timeout`: 超时时间 (毫秒)

```bash
curl "http://localhost:3002/api/v1/image/validate?url=https://example.com/image.jpg&timeout=5000"
```

## 🤖 自动化API

### 验证和修复heroImage

#### POST /api/automation/validate-hero-images
验证并修复文章中的heroImage链接

**请求体:**
```json
{
  "validateUrls": true,
  "fixIssues": true,
  "dryRun": false,
  "timeout": 5000,
  "fallbackImage": "/hero-bg.jpg"
}
```

**参数说明:**
- `validateUrls`: 是否验证URL有效性
- `fixIssues`: 是否自动修复问题
- `dryRun`: 测试模式，不实际修改文件
- `timeout`: 验证超时时间
- `fallbackImage`: 替换图片路径

**响应示例:**
```json
{
  "success": true,
  "data": {
    "totalFiles": 33,
    "fixedFiles": 5,
    "validFiles": 28,
    "fixedDetails": [
      {
        "file": "industry/2024-01-15-aerospace-news.md",
        "reason": "Dead link detected",
        "oldImage": "https://dead-link.com/image.jpg",
        "newImage": "/hero-bg.jpg"
      }
    ],
    "errors": []
  }
}
```

### 部署前检查

#### POST /api/automation/pre-deployment-check
执行部署前的全面检查

**请求体:**
```json
{
  "validateHeroImages": true,
  "validateContent": true,
  "generateSearchIndex": true,
  "checkSEO": true,
  "dryRun": false
}
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "heroImageValidation": true,
    "contentValidation": true,
    "searchIndex": true,
    "seoCheck": true,
    "readyForDeployment": true,
    "warnings": [],
    "summary": {
      "totalChecks": 4,
      "passedChecks": 4,
      "failedChecks": 0
    }
  }
}
```

### 运行完整自动化流程

#### POST /api/automation/run-full-cycle
执行完整的内容生成到验证流程

**请求体:**
```json
{
  "maxTasks": 5,
  "categories": ["industry", "research"],
  "generateImages": true,
  "executeImmediately": true,
  "validateBeforePublish": true
}
```

### 获取系统状态

#### GET /api/automation/status
获取自动化系统状态

**响应示例:**
```json
{
  "success": true,
  "data": {
    "providers": {
      "openai": "active",
      "serp": "active", 
      "tavily": "active",
      "fal": "active",
      "unsplash": "active"
    },
    "services": {
      "contentGeneration": "healthy",
      "imageGeneration": "healthy",
      "heroImageValidation": "healthy",
      "deployment": "ready"
    },
    "statistics": {
      "articlesGenerated": 127,
      "imagesGenerated": 89,
      "heroImagesFixed": 15,
      "deploymentsCompleted": 8
    }
  }
}
```

## 🧪 测试端点

### 测试链接发现

#### POST /api/automation/test-discovery
测试链接发现功能

**请求体:**
```json
{
  "keyword": "quantum computing breakthroughs",
  "maxUrls": 5,
  "sources": ["google", "bing", "news"]
}
```

### 测试内容生成

#### POST /api/automation/test-content
测试内容生成功能

**请求体:**
```json
{
  "keyword": "space exploration technology",
  "relatedProducts": ["satellite-systems", "propulsion"],
  "dryRun": true
}
```

## 📊 日志和监控API

### 获取heroImage验证日志

#### GET /api/automation/hero-image-logs
获取heroImage验证操作日志

**查询参数:**
- `limit`: 日志数量限制
- `startDate`: 开始日期
- `endDate`: 结束日期

### 记录自定义日志

#### POST /api/automation/log
记录自定义日志信息

**请求体:**
```json
{
  "level": "info",
  "message": "Custom operation completed",
  "metadata": {
    "operation": "content-generation",
    "taskId": "task_001"
  }
}
```

## 🔧 配置API

### 获取配置信息

#### GET /api/config
获取系统配置信息

### 更新配置

#### PUT /api/config
更新系统配置

**请求体:**
```json
{
  "heroImageValidation": {
    "enabled": true,
    "timeout": 5000,
    "fallbackImage": "/hero-bg.jpg"
  },
  "contentGeneration": {
    "defaultCategory": "industry",
    "maxRetries": 3
  }
}
```

## 🚨 错误处理

### 错误响应格式

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid request parameters",
    "details": {
      "field": "keyword",
      "issue": "Keyword is required"
    }
  },
  "timestamp": "2024-01-20T10:30:00Z"
}
```

### 常见错误代码

- `VALIDATION_ERROR`: 请求参数验证失败
- `RATE_LIMIT_EXCEEDED`: 请求频率超限
- `SERVICE_UNAVAILABLE`: 外部服务不可用
- `INTERNAL_ERROR`: 服务器内部错误
- `AUTHENTICATION_FAILED`: 认证失败
- `RESOURCE_NOT_FOUND`: 资源未找到

## 📈 速率限制

- **内容生成**: 10 请求/分钟
- **图片生成**: 5 请求/分钟
- **批量操作**: 2 请求/分钟
- **查询操作**: 100 请求/分钟

## 🔐 认证配置

### API Key认证 (可选)

```bash
curl -H "Authorization: Bearer YOUR_API_KEY" \
     http://localhost:3002/api/v1/content
```

### 环境变量配置

```env
# API认证 (可选)
API_AUTH_ENABLED=false
API_SECRET_KEY=your-secret-key

# 速率限制
RATE_LIMIT_ENABLED=true
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100
```

## 📚 SDK和客户端

### JavaScript/Node.js 客户端

```javascript
const NewsAPI = require('./news-api-client');

const client = new NewsAPI({
  baseUrl: 'http://localhost:3002',
  apiKey: 'your-api-key' // 可选
});

// 生成内容
const result = await client.content.generate({
  keyword: 'aerospace innovation',
  category: 'industry',
  generateImage: true
});

// 验证heroImage
const validation = await client.automation.validateHeroImages({
  validateUrls: true,
  fixIssues: true
});
```

### Python 客户端

```python
from news_api_client import NewsAPIClient

client = NewsAPIClient(
    base_url='http://localhost:3002',
    api_key='your-api-key'  # 可选
)

# 生成内容
result = client.content.generate(
    keyword='quantum radar technology',
    category='research',
    generate_image=True
)

# 批量生成
batch_result = client.content.batch_generate(
    task_ids=['task_001', 'task_002'],
    generate_images=True
)
```

---

这份API文档提供了新闻平台所有可用端点的详细说明。如需更多信息或遇到问题，请参考项目README或提交Issue。 