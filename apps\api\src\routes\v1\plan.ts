import type { FastifyInstance } from 'fastify';
import { planService } from '../../core/services/plan-service.js';

// 临时的本地实现
function createApiResponse(data: any, meta?: any) {
  return {
    success: true,
    data,
    meta: {
      timestamp: new Date().toISOString(),
      ...meta
    }
  };
}

// 临时的schema定义
const CreatePlanDtoSchema = {
  type: 'object',
  properties: {
    date: { type: 'string' },
    maxTasks: { type: 'number', minimum: 1, maximum: 50, default: 10 },
    categories: { 
      type: 'array', 
      items: { type: 'string' },
      default: ['industry', 'research', 'frontier']
    },
    customPrompt: { type: 'string' }
  }
};

export default async function planRoutes(fastify: FastifyInstance) {
  // GET /api/v1/plans - 获取计划列表
  fastify.get('/', async (request, reply) => {
    try {
      const plans = await planService.getPlans();
      return createApiResponse(plans, { total: plans.length });
    } catch (error) {
      fastify.log.error(`Failed to fetch plans: ${error instanceof Error ? error.message : String(error)}`);
      reply.code(500).send({
        success: false,
        error: 'Failed to fetch plans'
      });
    }
  });

  // POST /api/v1/plans - 创建新计划
  fastify.post('/', {
    schema: {
      body: CreatePlanDtoSchema,
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: { type: 'object' }
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const planData = request.body as any;
      const plan = await planService.createPlan(planData);
      
      reply.code(201);
      return createApiResponse(plan);
    } catch (error: any) {
      fastify.log.error(`Failed to create plan: ${error instanceof Error ? error.message : String(error)}`);
      if (error.message.includes('already exists')) {
        reply.code(409).send({
          success: false,
          error: error.message
        });
      } else {
        reply.code(500).send({
          success: false,
          error: 'Failed to create plan'
        });
      }
    }
  });

  // GET /api/v1/plans/:date - 获取特定日期的计划
  fastify.get('/:date', async (request, reply) => {
    try {
      const { date } = request.params as { date: string };
      const plan = await planService.getPlanByDate(date);
      
      if (!plan) {
        reply.code(404).send({
          success: false,
          error: `Plan not found for date: ${date}`
        });
        return;
      }
      
      return createApiResponse(plan);
    } catch (error: any) {
      fastify.log.error(`Failed to fetch plan: ${error instanceof Error ? error.message : String(error)}`);
      reply.code(500).send({
        success: false,
        error: 'Failed to fetch plan'
      });
    }
  });

  // POST /api/v1/plans/:date/generate - 生成特定日期的计划
  fastify.post('/:date/generate', async (request, reply) => {
    try {
      const { date } = request.params as { date: string };
      const { forceRegenerate = false, ...options } = request.body as any;
      
      const plan = await planService.generatePlan();
      
      reply.code(201);
      return createApiResponse(plan);
    } catch (error: any) {
      fastify.log.error(`Failed to generate plan: ${error instanceof Error ? error.message : String(error)}`);
      reply.code(500).send({
        success: false,
        error: error.message || 'Failed to generate plan'
      });
    }
  });
}