export interface ImageProviderConfig {
  priority: number;
  enabled: boolean;
  timeout?: number;
  retries?: number;
  [key: string]: any;
}

export interface ImageGenerationRequest {
  prompt?: string;
  keyword?: string;
  title?: string;
  category?: string;
  dimensions?: {
    width: number;
    height: number;
  };
  orientation?: 'landscape' | 'portrait' | 'square';
  style?: string;
}

export interface ImageGenerationResult {
  success: boolean;
  url?: string;
  provider: string;
  metadata?: {
    prompt?: string;
    dimensions?: { width: number; height: number };
    style?: string;
    credits?: number;
    [key: string]: any;
  };
  error?: string;
}

export abstract class BaseImageProvider {
  protected config: ImageProviderConfig;
  protected name: string;

  constructor(name: string, config: ImageProviderConfig) {
    this.name = name;
    this.config = config;
  }

  abstract generate(request: ImageGenerationRequest): Promise<ImageGenerationResult>;

  abstract healthCheck(): Promise<boolean>;

  getName(): string {
    return this.name;
  }

  getConfig(): ImageProviderConfig {
    return this.config;
  }

  isEnabled(): boolean {
    return this.config.enabled;
  }

  getPriority(): number {
    return this.config.priority;
  }

  protected async withTimeout<T>(
    promise: Promise<T>,
    timeoutMs: number = 30000
  ): Promise<T> {
    const timeout = new Promise<never>((_, reject) =>
      setTimeout(() => reject(new Error(`Timeout after ${timeoutMs}ms`)), timeoutMs)
    );

    return Promise.race([promise, timeout]);
  }

  protected async withRetries<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        if (attempt === maxRetries) {
          throw lastError;
        }
        
        // 指数退避
        const delay = Math.pow(2, attempt - 1) * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw lastError!;
  }
} 