import type { FastifyInstance } from 'fastify';
import { ContentGeneratorAgent } from '../../core/agents/content-generator-agent.js';
import { taskRepository } from '../../core/database/repositories.js';

let contentAgent: ContentGeneratorAgent;

export default async function contentRoutes(fastify: FastifyInstance) {
  // 初始化ContentGeneratorAgent
  if (!contentAgent) {
    contentAgent = new ContentGeneratorAgent();
    await contentAgent.initialize();
  }

  // POST /api/v1/content/generate - 生成内容
  fastify.post('/generate', {
    schema: {
      body: {
        type: 'object',
        properties: {
          taskId: { type: 'string' },
          keyword: { type: 'string' },
          sourceUrl: { type: 'string' },
          sourceContent: { type: 'string' },
          relatedProducts: { type: 'array', items: { type: 'string' }, default: [] },
          generateImage: { type: 'boolean', default: true },
          dryRun: { type: 'boolean', default: false }
        },
        required: ['taskId', 'keyword']
      }
    }
  }, async (request, reply) => {
    try {
      const input = request.body as any;
      const context = {
        requestId: `req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        metadata: {},
        dryRun: input.dryRun || false
      };
      
      const result = await contentAgent.execute(input, context);
      
      if (result.success) {
        return {
          success: true,
          data: result.data,
          message: 'Content generated successfully'
        };
      } else {
        reply.code(500).send({
          success: false,
          error: result.error || 'Content generation failed'
        });
      }
    } catch (error: any) {
      fastify.log.error(`Content generation failed: ${error instanceof Error ? error.message : String(error)}`);
      reply.code(500).send({
        success: false,
        error: error.message || 'Content generation failed'
      });
    }
  });

  // POST /api/v1/content/batch - 批量生成
  fastify.post('/batch', async (request, reply) => {
    try {
      const { taskIds, generateImages = true, dryRun = false } = request.body as any;
      const results: Array<{ taskId: string; status: string; data?: any; error?: string }> = [];
      let successCount = 0;
      let failureCount = 0;

      // 获取待处理的任务
      let tasks: any[] = [];
      if (taskIds && taskIds.length > 0) {
        const taskPromises = taskIds.map((id: string) => taskRepository.getById(id));
        const resolvedTasks = await Promise.all(taskPromises);
        tasks = resolvedTasks.filter(Boolean);
      } else {
        tasks = await taskRepository.getByStatus('pending');
      }

      for (const task of tasks) {
        try {
          const context = {
            requestId: `batch-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            metadata: {},
            dryRun
          };
          
          const result = await contentAgent.execute({
            taskId: task.id,
            keyword: task.keyword,
            sourceUrl: task.sourceUrl,
            relatedProducts: task.relatedProducts || [],
            generateImage: generateImages,
            dryRun
          }, context);
          
          if (result.success) {
            results.push({ taskId: task.id, status: 'success', data: result.data });
            successCount++;
          } else {
            results.push({ taskId: task.id, status: 'failed', error: result.error });
            failureCount++;
          }
        } catch (error: any) {
          results.push({ taskId: task.id, status: 'failed', error: error.message });
          failureCount++;
        }
      }

      return {
        success: true,
        data: { processed: tasks.length, successful: successCount, failed: failureCount, results },
        message: `Batch processing completed: ${successCount} successful, ${failureCount} failed`
      };
    } catch (error: any) {
      reply.code(500).send({ success: false, error: error.message || 'Batch content generation failed' });
    }
  });

  // GET /api/v1/content - 获取内容列表
  fastify.get('/', async (request, reply) => {
    try {
      const { status, limit = 20, offset = 0 } = request.query as any;
      let tasks;
      
      if (status) {
        tasks = await taskRepository.getByStatus(status);
      } else {
        tasks = await taskRepository.getAll();
      }

      // 简单分页
      const total = tasks.length;
      const paginatedTasks = tasks.slice(offset, offset + limit);

      return {
        success: true,
        data: paginatedTasks,
        meta: {
          total,
          limit,
          offset,
          hasMore: offset + limit < total
        }
      };
    } catch (error: any) {
      fastify.log.error(`Failed to fetch content: ${error instanceof Error ? error.message : String(error)}`);
      reply.code(500).send({
        success: false,
        error: 'Failed to fetch content'
      });
    }
  });
}