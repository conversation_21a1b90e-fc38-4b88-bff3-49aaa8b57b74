import type { FastifyInstance } from 'fastify';
import fp from 'fastify-plugin';

async function validationPlugin(fastify: FastifyInstance) {
  // 添加通用验证装饰器
  fastify.decorate('validateBody', function (schema: any) {
    return {
      preHandler: async (request: any, reply: any) => {
        try {
          request.body = schema.parse(request.body);
        } catch (error) {
          reply.code(400).send({
            success: false,
            error: {
              code: 'VALIDATION_ERROR',
              message: 'Request body validation failed',
              details: error
            }
          });
        }
      }
    };
  });

  fastify.decorate('validateQuery', function (schema: any) {
    return {
      preHandler: async (request: any, reply: any) => {
        try {
          request.query = schema.parse(request.query);
        } catch (error) {
          reply.code(400).send({
            success: false,
            error: {
              code: 'VALIDATION_ERROR', 
              message: 'Query parameters validation failed',
              details: error
            }
          });
        }
      }
    };
  });
}

export default fp(validationPlugin, {
  name: 'validation'
}); 