import Database, { type Database as SqliteDatabase } from 'better-sqlite3';
import { drizzle } from 'drizzle-orm/better-sqlite3';
import { migrate } from 'drizzle-orm/better-sqlite3/migrator';
import * as schema from './schema.js';
import { join, dirname, resolve } from 'path';
import { existsSync, mkdirSync } from 'fs';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// 获取项目根目录路径（从 apps/api/src/core/database 向上五级到项目根目录）
const projectRoot = resolve(__dirname, '../../../../../');

// 数据库配置 - 使用项目根目录作为基准
const DATABASE_PATH = process.env.DATABASE_URL || join(projectRoot, 'apps/api/data/database.sqlite');

// 确保数据库目录存在
const dbDir = dirname(DATABASE_PATH);
if (!existsSync(dbDir)) {
  console.log(`🗂️ 创建数据库目录: ${dbDir}`);
  mkdirSync(dbDir, { recursive: true });
}

// 创建SQLite连接
const sqlite = new Database(DATABASE_PATH);

// 启用外键约束
sqlite.pragma('foreign_keys = ON');

// 创建Drizzle数据库实例
export const db = drizzle(sqlite, { schema });



// 初始化数据库
export async function initDatabase() {
  try {
    console.log('🗄️ 初始化数据库...');
    
    // 运行迁移 (如果有的话)
    // migrate(db, { migrationsFolder: './migrations' });
    
    // 创建表 (开发环境快速创建)
    const createTables = `
      CREATE TABLE IF NOT EXISTS plans (
        id TEXT PRIMARY KEY,
        date TEXT NOT NULL UNIQUE,
        status TEXT NOT NULL DEFAULT 'draft',
        max_tasks INTEGER NOT NULL DEFAULT 10,
        categories TEXT DEFAULT '["industry","research","frontier"]',
        custom_prompt TEXT,
        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
      );

      CREATE TABLE IF NOT EXISTS tasks (
        id TEXT PRIMARY KEY,
        plan_id TEXT NOT NULL REFERENCES plans(id) ON DELETE CASCADE,
        title TEXT NOT NULL,
        description TEXT,
        keyword TEXT NOT NULL,
        source_url TEXT,
        target_category TEXT NOT NULL,
        priority INTEGER NOT NULL DEFAULT 3,
        status TEXT NOT NULL DEFAULT 'pending',
        related_products TEXT DEFAULT '[]',
        estimated_duration INTEGER NOT NULL DEFAULT 60,
        actual_duration INTEGER,
        error_message TEXT,
        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
      );

      CREATE TABLE IF NOT EXISTS contents (
        id TEXT PRIMARY KEY,
        task_id TEXT NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
        title TEXT NOT NULL,
        slug TEXT NOT NULL UNIQUE,
        markdown TEXT NOT NULL,
        front_matter TEXT NOT NULL,
        file_path TEXT,
        github_url TEXT,
        word_count INTEGER NOT NULL DEFAULT 0,
        read_time INTEGER NOT NULL DEFAULT 1,
        seo_score REAL DEFAULT 0,
        keyword_density REAL DEFAULT 0,
        generated_image_url TEXT,
        processing_time_ms INTEGER,
        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
      );

      CREATE TABLE IF NOT EXISTS config (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL,
        description TEXT,
        updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
      );

      CREATE TABLE IF NOT EXISTS logs (
        id TEXT PRIMARY KEY,
        level TEXT NOT NULL,
        message TEXT NOT NULL,
        data TEXT,
        source TEXT,
        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
      );

      -- 创建索引
      CREATE INDEX IF NOT EXISTS idx_tasks_plan_id ON tasks(plan_id);
      CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks(status);
      CREATE INDEX IF NOT EXISTS idx_contents_task_id ON contents(task_id);
      CREATE INDEX IF NOT EXISTS idx_logs_created_at ON logs(created_at);
      CREATE INDEX IF NOT EXISTS idx_plans_date ON plans(date);
    `;
    
    sqlite.exec(createTables);
    
    console.log('✅ 数据库初始化完成');
    return true;
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error);
    throw error;
  }
}

// 关闭数据库连接
export function closeDatabase() {
  sqlite.close();
}

// 注意：sqlite实例导出有TypeScript类型命名冲突，在代码内部使用即可 