import { OpenAI } from 'openai';
import type { Content, ContentGenerationRequest, ContentOutput } from '@news-site/shared';
import { logger } from '../../utils/logger.js';
import { ProductMatchingService, ProductMatchResult } from './product-matching-service.js';

export interface ContentGenerationConfig {
  openaiApiKey: string;
  openaiModel?: string;
  outputDir?: string;
  imageGeneration?: {
    enabled: boolean;
    provider: 'openai' | 'unsplash';
    unsplashApiKey?: string;
  };
  productIntegration?: {
    enabled: boolean;
    maxProductsPerArticle?: number;
    minRelevanceScore?: number;
  };
}

export interface ArticleData {
  keyword: string;
  url: string;
  sourceContent: string;
  relatedProducts: string[];
  context?: any;
}

export class ContentService {
  private openai: OpenAI | null = null;
  private config: ContentGenerationConfig;
  private seoPromptTemplate: string;
  private productMatchingService: ProductMatchingService;

  constructor() {
    this.config = {
      openaiApiKey: process.env.OPENAI_API_KEY || '',
      openaiModel: 'gpt-4o-mini',
      outputDir: './src/content/news',
      imageGeneration: {
        enabled: true,
        provider: 'unsplash',
        unsplashApiKey: process.env.UNSPLASH_API_KEY
      },
      productIntegration: {
        enabled: true,
        maxProductsPerArticle: 5,
        minRelevanceScore: 0.3
      }
    };

    this.seoPromptTemplate = this.buildSEOPromptTemplate();
    this.productMatchingService = new ProductMatchingService({
      maxProducts: this.config.productIntegration?.maxProductsPerArticle || 5,
      minScore: this.config.productIntegration?.minRelevanceScore || 0.3
    });
  }

  private getOpenAIClient(): OpenAI {
    if (!this.openai) {
      // 在第一次使用时才初始化，确保环境变量已加载
      const apiKey = process.env.OPENAI_API_KEY || this.config.openaiApiKey;
      
      logger.info('延迟初始化 OpenAI 客户端...');
      logger.info(`OPENAI_API_KEY 长度: ${apiKey?.length || 0}`);
      logger.info(`OPENAI_API_KEY 前缀: ${apiKey?.substring(0, 10) || '未设置'}`);
      
      if (!apiKey) {
        throw new Error('OpenAI API key not configured. Please set OPENAI_API_KEY environment variable.');
      }

      this.openai = new OpenAI({
        apiKey: apiKey
      });

      logger.info('OpenAI 客户端初始化成功');
    }
    
    return this.openai;
  }

  /**
   * 生成文章内容
   */
  async generateContent(request: ContentGenerationRequest): Promise<ContentOutput> {
    logger.info(`Starting content generation for keyword: ${request.keyword}`);
    
    // 1. 产品匹配分析（如果启用）
    let matchedProducts: ProductMatchResult[] = [];
    if (this.config.productIntegration?.enabled) {
      try {
        matchedProducts = this.productMatchingService.matchProducts({
          title: request.sourceTitle,
          keyword: request.keyword,
          description: request.sourceContent,
          extractedContent: request.sourceContent
        });
        logger.info(`Found ${matchedProducts.length} matching products for keyword: ${request.keyword}`);
        
        // 详细日志
        if (matchedProducts.length > 0) {
          logger.info('Top matched products:', {
            products: matchedProducts.slice(0, 3).map(m => ({
              title: m.product.title,
              score: Math.round(m.score * 100),
              keywords: m.matchedKeywords.slice(0, 3)
            }))
          });
        } else {
          logger.warn('No products matched for content:', {
            keyword: request.keyword,
            title: request.sourceTitle,
            hasSourceContent: !!request.sourceContent
          });
        }
      } catch (error) {
        logger.warn('Product matching failed:', error);
      }
    }

    // 2. 构建增强的提示词
    const prompt = this.buildEnhancedPrompt({
      keyword: request.keyword,
      sourceContent: request.sourceContent || '',
      sourceTitle: request.sourceTitle || '',
      relatedProducts: request.relatedProducts || [],
      matchedProducts
    });

    // 3. 获取OpenAI客户端并生成内容
    const openai = this.getOpenAIClient();
    const response = await openai.chat.completions.create({
      model: this.config.openaiModel || 'gpt-4o-mini',
      messages: [
        {
          role: "system",
          content: this.seoPromptTemplate
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 4000
    });

    const generatedContent = response.choices[0].message.content || '';
    
    // 4. 解析生成的内容
    const articleOutput = this.parseGeneratedContent(generatedContent, request.keyword);

    // 5. 后处理：确保产品链接正确插入
    logger.info(`Post-processing check: ${matchedProducts.length} matched products available`);
    if (matchedProducts.length > 0) {
      logger.info(`Post-processing content with ${matchedProducts.length} matched products:`, {
        products: matchedProducts.map(m => ({
          title: m.product.title,
          score: Math.round(m.score * 100),
          url: m.product.url,
          keywords: m.matchedKeywords.slice(0, 3)
        }))
      });

      const originalContent = articleOutput.content;
      const contentPreview = originalContent.substring(0, 200);
      logger.info(`Original content preview: ${contentPreview}...`);

      articleOutput.content = this.postProcessContent(articleOutput.content, matchedProducts);

      // 检查是否有链接被插入
      const originalLinkCount = this.countProductLinks(originalContent);
      const finalLinkCount = this.countProductLinks(articleOutput.content);
      logger.info(`Product links: ${originalLinkCount} -> ${finalLinkCount} (added ${finalLinkCount - originalLinkCount})`);

      if (finalLinkCount === 0) {
        logger.warn('No product links were added during post-processing!');
      }
    } else {
      logger.warn('No matched products for post-processing - product matching may have failed');
    }

    // 6. 生成配图 (如果启用)
    if (this.config.imageGeneration?.enabled) {
      try {
        articleOutput.imageUrl = await this.generateImage(articleOutput.title, request.keyword);
      } catch (error) {
        logger.warn('Image generation failed:', error);
      }
    }

    // 7. 计算SEO指标
    articleOutput.metadata = this.calculateSEOMetrics(articleOutput.content, request.keyword);

    // 8. 添加产品相关数据到metadata
    if (matchedProducts.length > 0) {
      articleOutput.metadata = {
        ...articleOutput.metadata,
        relatedProducts: this.productMatchingService.generateProductRecommendations(matchedProducts),
        productLinksCount: this.countProductLinks(articleOutput.content)
      };
    }

    // 9. 验证产品链接质量
    const linkValidation = this.validateProductLinks(articleOutput.content);
    if (!linkValidation.isValid) {
      logger.warn('产品链接质量问题:', {
        issues: linkValidation.issues,
        stats: linkValidation.stats
      });
    } else {
      logger.info('产品链接质量验证通过:', linkValidation.stats);
    }

    logger.info(`Content generated successfully: ${articleOutput.slug}`);
    return articleOutput;
  }

  /**
   * 从URL抓取内容并生成文章
   */
  async generateFromUrl(data: ArticleData): Promise<ContentOutput> {
    // 首先尝试抓取内容
    const scrapeResult = await this.scrapeWithFallback(data.url);
    if (!scrapeResult.success) {
      throw new Error(`Failed to scrape content from ${data.url}: ${scrapeResult.error}`);
    }

    // 生成文章内容
    return this.generateContent({
      keyword: data.keyword,
      sourceContent: scrapeResult.markdown || '',
      sourceTitle: scrapeResult.title || '',
      relatedProducts: data.relatedProducts,
      context: data.context
    });
  }

  /**
   * 基于关键词自动发现链接并生成文章
   */
  async generateFromKeyword(keyword: string, relatedProducts: string[] = []): Promise<ContentOutput> {
    const { linkDiscoveryTool } = await import('../tools/link-discovery-tool');
    
    // 使用链接发现工具获取相关内容
    const discoveredLinks = await linkDiscoveryTool.discoverLinks(keyword);
    
    if (discoveredLinks.length === 0) {
      throw new Error(`No content found for keyword: ${keyword}`);
    }

    // 选择最佳链接（基于置信度和内容长度）
    const bestLink = discoveredLinks.sort((a, b) => {
      const scoreA = (a.confidence || 0.5) * (a.snippet?.length || 0);
      const scoreB = (b.confidence || 0.5) * (b.snippet?.length || 0);
      return scoreB - scoreA;
    })[0];

    // 合并所有snippet作为源内容
    const combinedContent = discoveredLinks
      .map(link => `${link.title || ''}\n${link.snippet}`)
      .join('\n\n---\n\n');

    // 生成文章内容
    return this.generateContent({
      keyword,
      sourceContent: combinedContent,
      sourceTitle: bestLink.title || keyword,
      relatedProducts,
      context: {
        discoveredLinks: discoveredLinks.map(link => ({
          url: link.url,
          title: link.title,
          source: link.source
        }))
      }
    });
  }

  private buildSEOPromptTemplate(): string {
    return `You are an expert aerospace and defense industry journalist and SEO specialist. Your task is to create high-quality, SEO-optimized news articles that are both informative and engaging.

Key Requirements:
1. Write in professional journalistic style
2. Include relevant technical details and industry insights
3. Optimize for search engines while maintaining readability
4. Include quotes and expert perspectives when appropriate
5. Structure content with clear headings and subheadings
6. Ensure factual accuracy and cite sources
7. Include related products and technologies naturally in the content

Output Format:
Please format your response as JSON with the following structure:
{
  "title": "SEO-optimized headline",
  "excerpt": "Brief description for meta description",
  "content": "Full article content in markdown format",
  "tags": ["tag1", "tag2", "tag3"],
  "category": "industry|research|events|frontier|insight|misc"
}`;
  }

  private buildPrompt(data: {
    keyword: string;
    sourceContent: string;
    sourceTitle: string;
    relatedProducts: any[];
  }): string {
    return `Write a comprehensive news article about: ${data.keyword}

Source Content:
Title: ${data.sourceTitle}
Content: ${data.sourceContent.substring(0, 2000)}${data.sourceContent.length > 2000 ? '...' : ''}

Related Products/Technologies to mention naturally in the article:
${data.relatedProducts.map(p => `- ${p}`).join('\n')}

Article Requirements:
- Length: 800-1200 words
- Include the main keyword "${data.keyword}" naturally throughout
- Create engaging subheadings
- Include technical specifications where relevant
- Add industry context and analysis
- Mention potential impacts and future developments
- Include quotes or expert perspectives (you can create realistic ones)
- Naturally incorporate the related products/technologies
- Ensure factual accuracy based on the source content

Please generate the article following the JSON format specified in the system prompt.`;
  }

  private buildEnhancedPrompt(data: {
    keyword: string;
    sourceContent: string;
    sourceTitle: string;
    relatedProducts: any[];
    matchedProducts: ProductMatchResult[];
  }): string {
    const basePrompt = this.buildPrompt(data);
    
    if (data.matchedProducts.length === 0) {
      return basePrompt;
    }

    const productContext = data.matchedProducts.map(match => ({
      name: match.product.title,
      model: match.product.model || '', // 添加型号信息
      description: match.product.description,
      url: match.product.url,
      keywords: match.matchedKeywords.join(', '),
      relevance: Math.round(match.score * 100)
    }));

    const keyTermsToHighlight = this.extractKeyTermsFromProducts(productContext);

    return `${basePrompt}

IMPORTANT: In your article, naturally incorporate relevant product mentions. Here are related products to consider:

${productContext.map((product, index) => `
${index + 1}. **${product.name}**${product.model ? ` (Model: ${product.model})` : ''} (Relevance: ${product.relevance}%)
   - Description: ${product.description}
   - Key terms: ${product.keywords}
`).join('')}

CRITICAL INSTRUCTION for product integration:
When writing your article, follow these STRICT RULES for product links:

PRODUCT LINKING RULES:
1. MAXIMUM LINKS: Include only 2 product-related terms total in your entire article
2. DISTRIBUTION: If using 2 links, place one in the first half and one in the second half of the article
3. NO REPETITION: Never link the same product multiple times - use each product link only ONCE
4. SPACING: Ensure at least 200 words between different product links
5. NATURAL PLACEMENT: Only link when the term naturally fits the sentence context

EXACT TERMS TO HIGHLIGHT (choose maximum 2):
${keyTermsToHighlight.slice(0, 5).map(term => `- **${term}**`).join('\n')}

QUALITY GUIDELINES:
- Use meaningful product names (e.g., "navigation system") rather than model numbers
- Focus on technical concepts and system types rather than specific model numbers  
- Write naturally - don't force these terms if they don't fit the context
- Prioritize content quality over product placement
- Only highlight terms that add genuine value to the reader

Remember: Quality content with fewer, well-placed product mentions is better than content with excessive links that disrupt readability.`;
  }

  private parseGeneratedContent(content: string, keyword: string): ContentOutput {
    try {
      // Try to parse as JSON first
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return {
          title: parsed.title || this.generateFallbackTitle(keyword),
          slug: this.generateSlug(parsed.title || keyword),
          content: parsed.content || content,
          excerpt: parsed.excerpt || '',
          tags: parsed.tags || [],
          category: parsed.category || this.inferCategory(keyword),
          imageUrl: '',
          metadata: {
            wordCount: 0,
            readTime: 0,
            seoScore: 0,
            keywordDensity: 0
          }
        };
      }
    } catch (error) {
      logger.warn('Failed to parse JSON content, using plain text parsing');
    }

    // Fallback to plain text parsing
    return this.parsePlainTextContent(content, keyword);
  }

  private parsePlainTextContent(content: string, keyword: string): ContentOutput {
    const lines = content.split('\n').filter(line => line.trim());
    const title = lines[0]?.replace(/^#+\s*/, '') || this.generateFallbackTitle(keyword);
    
    return {
      title,
      slug: this.generateSlug(title),
      content,
      excerpt: lines[1]?.substring(0, 160) || '',
      tags: this.extractTags(content),
      category: this.inferCategory(keyword),
      imageUrl: '',
      metadata: {
        wordCount: 0,
        readTime: 0,
        seoScore: 0,
        keywordDensity: 0
      }
    };
  }

  private async generateImage(title: string, keyword: string): Promise<string> {
    if (this.config.imageGeneration?.provider === 'unsplash') {
      return this.generateUnsplashImage(keyword);
    } else {
      return this.generateOpenAIImage(title, keyword);
    }
  }

  private async generateUnsplashImage(keyword: string): Promise<string> {
    const searchTerms = [
      keyword,
      'aerospace',
      'defense',
      'military aircraft',
      'satellite',
      'space technology'
    ];

    for (const term of searchTerms) {
      try {
        const response = await fetch(
          `https://api.unsplash.com/search/photos?query=${encodeURIComponent(term)}&per_page=1&orientation=landscape`,
          {
            headers: {
              'Authorization': `Client-ID ${this.config.imageGeneration?.unsplashApiKey}`
            }
          }
        );

        if (response.ok) {
          const data = await response.json();
          if (data.results && data.results.length > 0) {
            return data.results[0].urls.regular;
          }
        }
      } catch (error) {
        logger.warn(`Failed to fetch image for term: ${term}`, error);
      }
    }

    // Fallback to the default background image
    return '/hero-bg.jpg';
  }

  private async generateOpenAIImage(title: string, keyword: string): Promise<string> {
    try {
      const openai = this.getOpenAIClient();
      const response = await openai.images.generate({
        model: "dall-e-3",
        prompt: `Professional news article header image for: ${title}. Style: Modern, clean, aerospace/defense theme, high-tech, professional lighting`,
        size: "1792x1024",
        quality: "standard",
        n: 1,
      });

      return response.data[0].url || '';
    } catch (error) {
      logger.error('OpenAI image generation failed:', error);
      throw error;
    }
  }

  private async scrapeWithFallback(url: string): Promise<{
    success: boolean;
    url: string;
    title?: string;
    content?: string;
    markdown?: string;
    statusCode?: number;
    error?: string;
  }> {
    try {
      const response = await fetch(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (compatible; NewsBot/1.0)'
        },
        signal: AbortSignal.timeout(15000)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const html = await response.text();
      const title = this.extractTitleFromHtml(html);
      const content = this.extractContentFromHtml(html);

      return {
        success: true,
        url,
        title,
        content,
        markdown: content,
        statusCode: response.status
      };
    } catch (error) {
      return {
        success: false,
        url,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private calculateSEOMetrics(content: string, keyword: string): ContentOutput['metadata'] {
    const wordCount = this.countWords(content);
    const keywordDensity = this.calculateKeywordDensity(content, keyword);
    const seoScore = this.calculateSEOScore(content, keyword, wordCount);
    const readTime = Math.ceil(wordCount / 200); // 200 words per minute

    return {
      wordCount,
      readTime,
      seoScore,
      keywordDensity
    };
  }

  private countWords(text: string): number {
    return text.trim().split(/\s+/).length;
  }

  private calculateKeywordDensity(content: string, keyword: string): number {
    const words = content.toLowerCase().split(/\s+/);
    const keywordCount = words.filter(word => 
      word.includes(keyword.toLowerCase())
    ).length;
    return (keywordCount / words.length) * 100;
  }

  private calculateSEOScore(content: string, keyword: string, wordCount: number): number {
    let score = 0;
    
    // Word count (ideal: 800-1200)
    if (wordCount >= 800 && wordCount <= 1200) score += 20;
    else if (wordCount >= 600) score += 15;
    else if (wordCount >= 400) score += 10;
    
    // Keyword density (ideal: 1-3%)
    const density = this.calculateKeywordDensity(content, keyword);
    if (density >= 1 && density <= 3) score += 20;
    else if (density >= 0.5 && density <= 5) score += 10;
    
    // Heading structure
    const headings = content.match(/^#+\s/gm);
    if (headings && headings.length >= 3) score += 15;
    else if (headings && headings.length >= 1) score += 10;
    
    // Content structure
    if (content.includes('\n\n')) score += 10; // Paragraphs
    if (content.match(/\*\*.*\*\*/)) score += 5; // Bold text
    if (content.match(/-\s/gm)) score += 5; // Lists
    
    return Math.min(score, 100);
  }

  private generateFallbackTitle(keyword: string): string {
    const templates = [
      `Breaking: Latest Developments in ${keyword}`,
      `Industry Analysis: ${keyword} Market Trends`,
      `Technology Focus: Advances in ${keyword}`,
      `Defense Update: ${keyword} Systems Overview`
    ];
    
    return templates[Math.floor(Math.random() * templates.length)];
  }

  private generateSlug(title: string): string {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  }

  private inferCategory(keyword: string): string {
    const categoryKeywords = {
      'industry': ['market', 'company', 'business', 'commercial', 'sales'],
      'research': ['study', 'research', 'development', 'test', 'experiment'],
      'frontier': ['breakthrough', 'innovation', 'future', 'next-gen', 'advanced'],
      'events': ['conference', 'summit', 'expo', 'meeting', 'announcement'],
      'insight': ['analysis', 'opinion', 'perspective', 'review', 'commentary']
    };

    const searchText = keyword.toLowerCase();
    
    for (const [category, keywords] of Object.entries(categoryKeywords)) {
      if (keywords.some(kw => searchText.includes(kw))) {
        return category;
      }
    }
    
    return 'misc';
  }

  private extractTags(content: string): string[] {
    const technicalTerms = [
      'aerospace', 'defense', 'military', 'satellite', 'aircraft',
      'missile', 'radar', 'stealth', 'drone', 'cybersecurity',
      'AI', 'space', 'hypersonic', 'propulsion'
    ];
    
    const foundTags = technicalTerms.filter(term => 
      content.toLowerCase().includes(term.toLowerCase())
    );
    
    return foundTags.slice(0, 5);
  }

  private extractTitleFromHtml(html: string): string {
    const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
    return titleMatch ? titleMatch[1].trim() : '';
  }

  private extractContentFromHtml(html: string): string {
    // Simple HTML content extraction
    return html
      .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '')
      .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '')
      .replace(/<[^>]+>/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();
  }

  /**
   * 后处理内容，确保产品链接正确插入
   */
  private postProcessContent(content: string, matchedProducts: ProductMatchResult[]): string {
    let processedContent = content;
    let linksAdded = 0;
    const MAX_LINKS = 2; // 严格限制最多2个链接
    const MIN_DISTANCE = 200; // 链接间最小字符距离

    logger.info('Starting post-processing with products:', {
      productCount: matchedProducts.length,
      products: matchedProducts.slice(0, 3).map(m => ({
        title: m.product.title,
        keywords: m.matchedKeywords.slice(0, 3)
      }))
    });

    // 记录已添加的链接位置，用于间距检查
    const linkPositions: Array<{position: number, term: string, productUrl: string}> = [];

    // 第一步：处理AI标注的术语（**term**格式）
    const highlightedTerms = content.match(/\*\*([^*]+)\*\*/g);
    if (highlightedTerms) {
      logger.info(`Found ${highlightedTerms.length} highlighted terms:`, highlightedTerms);

      for (const highlightedTerm of highlightedTerms) {
        if (linksAdded >= MAX_LINKS) break; // 严格控制链接数量

        const term = highlightedTerm.replace(/\*\*/g, ''); // 移除**标记

        // 查找匹配的产品
        const matchingProduct = this.findBestMatchingProduct(term, matchedProducts);
        if (matchingProduct) {
          // 检查是否已经链接过这个产品
          const productAlreadyLinked = linkPositions.some(link => link.productUrl === matchingProduct.product.url);
          if (productAlreadyLinked) {
            logger.info(`Product ${matchingProduct.product.title} already linked, skipping "${term}"`);
            processedContent = processedContent.replace(highlightedTerm, `**${term}**`);
            continue;
          }

          // 检查与其他链接的距离
          const termPosition = processedContent.indexOf(highlightedTerm);
          const tooClose = linkPositions.some(link => 
            Math.abs(link.position - termPosition) < MIN_DISTANCE
          );

          if (tooClose) {
            logger.info(`Term "${term}" too close to existing link, keeping as bold text`);
            processedContent = processedContent.replace(highlightedTerm, `**${term}**`);
            continue;
          }

          const replacement = `[${term}](${matchingProduct.product.url})`;
          processedContent = processedContent.replace(highlightedTerm, replacement);
          linkPositions.push({
            position: termPosition,
            term: term,
            productUrl: matchingProduct.product.url
          });
          linksAdded++;
          logger.info(`Converted highlighted term "${term}" to link -> ${matchingProduct.product.title} (${linksAdded}/${MAX_LINKS})`);
        } else {
          // 如果没有匹配的产品，保留原始格式（加粗）
          processedContent = processedContent.replace(highlightedTerm, `**${term}**`);
          logger.info(`No matching product for highlighted term "${term}", keeping as bold`);
        }
      }
    }

    // 第二步：如果仍有空间，智能添加重要产品链接
    if (linksAdded < MAX_LINKS && matchedProducts.length > 0) {
      logger.info(`Only ${linksAdded} links from highlighted terms, attempting to add ${MAX_LINKS - linksAdded} more high-value links`);

      // 只考虑相关性最高的产品
      const topProducts = matchedProducts
        .filter(match => !linkPositions.some(link => link.productUrl === match.product.url))
        .sort((a, b) => b.score - a.score)
        .slice(0, MAX_LINKS - linksAdded);

      for (const match of topProducts) {
        if (linksAdded >= MAX_LINKS) break;

        const product = match.product;

        // 查找最佳关键词 - 优先选择长词组且在文章中出现的
        const viableKeywords = match.matchedKeywords
          .filter(keyword => {
            // 检查关键词是否在文章中出现且未被链接
            return processedContent.includes(keyword) && !this.isAlreadyLinked(processedContent, keyword);
          })
          .sort((a, b) => b.length - a.length) // 优先长词组
          .slice(0, 2); // 只考虑前2个最佳候选

        let linkAdded = false;
        for (const keyword of viableKeywords) {
          // 使用更简单可靠的正则表达式查找关键词
          const escapedKeyword = this.escapeRegExp(keyword);
          const regex = new RegExp(`\\b(${escapedKeyword})\\b`, 'i');
          const matchResult = processedContent.match(regex);

          if (matchResult && !this.isAlreadyLinked(processedContent, keyword)) {
            const termPosition = processedContent.indexOf(matchResult[0]);
            
            // 检查与现有链接的距离
            const tooClose = linkPositions.some(link => 
              Math.abs(link.position - termPosition) < MIN_DISTANCE
            );

            if (!tooClose) {
              const originalText = matchResult[1];
              const replacement = `[${originalText}](${product.url})`;
              processedContent = processedContent.replace(matchResult[0], replacement);
              linkPositions.push({
                position: termPosition,
                term: keyword,
                productUrl: product.url
              });
              linksAdded++;
              logger.info(`Auto-added strategic link for "${keyword}" -> ${product.title} (${linksAdded}/${MAX_LINKS})`);
              linkAdded = true;
              break;
            } else {
              logger.info(`Keyword "${keyword}" found but too close to existing link, skipping`);
            }
          }
        }

        if (!linkAdded) {
          logger.info(`No suitable placement found for product: ${product.title}`);
        }
      }
    }

    logger.info(`Post-processing completed. Final links added: ${linksAdded}/${MAX_LINKS}`);
    
    // 验证最终结果
    const finalLinkCount = this.countProductLinks(processedContent);
    if (finalLinkCount !== linksAdded) {
      logger.warn(`Link count mismatch: processed ${linksAdded} but found ${finalLinkCount} in final content`);
    }

    return processedContent;
  }

  /**
   * 为给定术语找到最佳匹配的产品
   */
  private findBestMatchingProduct(term: string, matchedProducts: ProductMatchResult[]): ProductMatchResult | null {
    const termLower = term.toLowerCase();
    let bestMatch: ProductMatchResult | null = null;
    let bestScore = 0;

    for (const product of matchedProducts) {
      let score = 0;

      // 检查产品标题匹配
      if (product.product.title.toLowerCase().includes(termLower)) {
        score += 10;
      }

      // 检查关键词匹配
      for (const keyword of product.matchedKeywords) {
        if (keyword.toLowerCase().includes(termLower) || termLower.includes(keyword.toLowerCase())) {
          score += 5;
        }
      }

      // 检查描述匹配
      if (product.product.description.toLowerCase().includes(termLower)) {
        score += 3;
      }

      if (score > bestScore) {
        bestScore = score;
        bestMatch = product;
      }
    }

    return bestScore > 0 ? bestMatch : null;
  }

  /**
   * 从产品列表中提取关键术语供AI标注
   */
  private extractKeyTermsFromProducts(productContext: any[]): string[] {
    const keyTerms = new Set<string>();

    for (const product of productContext) {
      // 添加产品名称中的关键词
      const productNameWords = product.name.toLowerCase().split(/\s+/);

      // 添加关键词
      const keywords = product.keywords.split(',').map((k: string) => k.trim().toLowerCase());

      // 收集有意义的术语
      [...productNameWords, ...keywords].forEach(term => {
        // 过滤掉太短或太通用的词
        if (term.length > 3 && !['the', 'and', 'for', 'with', 'from', 'that', 'this', 'high', 'low'].includes(term)) {
          keyTerms.add(term);
        }
      });

      // 添加一些组合术语
      if (keywords.some(k => k.includes('thermal')) && keywords.some(k => k.includes('battery'))) {
        keyTerms.add('thermal battery');
      }
      if (keywords.some(k => k.includes('linear')) && keywords.some(k => k.includes('actuator'))) {
        keyTerms.add('linear actuator');
      }
      if (keywords.some(k => k.includes('precision')) && keywords.some(k => k.includes('control'))) {
        keyTerms.add('precision control');
      }
    }

    // 返回最相关的术语，按长度排序（优先长术语）
    return Array.from(keyTerms)
      .sort((a, b) => b.length - a.length)
      .slice(0, 6); // 最多6个术语
  }

  /**
   * 提取产品术语用于直接链接
   */
  private extractProductTermsForLinking(matchedProducts: ProductMatchResult[]): Array<{term: string, product: any}> {
    const termsToLink: Array<{term: string, product: any}> = [];

    for (const match of matchedProducts.slice(0, 3)) { // 最多处理3个产品
      const product = match.product;

      // 收集可能的链接术语
      const possibleTerms = [
        'thermal battery',
        'thermal batteries',
        'linear actuator',
        'linear actuators',
        'precision control',
        'control system',
        'control systems',
        'battery system',
        'battery systems',
        'power system',
        'power systems',
        'energy storage',
        'actuator system',
        'actuator systems'
      ];

      // 检查哪些术语与产品相关
      for (const term of possibleTerms) {
        const termWords = term.toLowerCase().split(/\s+/);
        const productKeywords = match.matchedKeywords.map(k => k.toLowerCase());

        // 如果术语的所有单词都在产品关键词中，则认为相关
        const isRelevant = termWords.every(word =>
          productKeywords.some(keyword => keyword.includes(word))
        );

        if (isRelevant) {
          termsToLink.push({ term, product });
          break; // 每个产品只添加一个术语
        }
      }
    }

    // 按术语长度排序，优先长术语
    return termsToLink.sort((a, b) => b.term.length - a.term.length);
  }

  /**
   * 检查内容中是否已经存在链接
   */
  private isAlreadyLinked(content: string, term: string): boolean {
    const escapedTerm = this.escapeRegExp(term);
    const linkPattern = new RegExp(`\\[([^\\]]*${escapedTerm}[^\\]]*)\\]\\([^)]+\\)`, 'gi');
    return linkPattern.test(content);
  }

  /**
   * 转义正则表达式特殊字符
   */
  private escapeRegExp(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  /**
   * 统计产品链接数量
   */
  private countProductLinks(content: string): number {
    const linkPattern = /\[([^\]]+)\]\((https:\/\/www\.gnc-tech\.com\/products\/[^)]+)\)/g;
    const matches = content.match(linkPattern);
    return matches ? matches.length : 0;
  }

  /**
   * 验证产品链接质量
   */
  private validateProductLinks(content: string): {
    isValid: boolean;
    issues: string[];
    stats: {
      totalLinks: number;
      uniqueProducts: number;
      linkDensity: number;
      averageDistance: number;
    };
  } {
    const issues: string[] = [];
    const linkRegex = /\[([^\]]+)\]\((https:\/\/www\.gnc-tech\.com\/products\/[^)]+)\)/g;
    const links: Array<{text: string, url: string, position: number}> = [];

    let match;
    while ((match = linkRegex.exec(content)) !== null) {
      links.push({
        text: match[1],
        url: match[2],
        position: match.index
      });
    }

    // 统计分析
    const totalLinks = links.length;
    const uniqueProducts = new Set(links.map(link => link.url)).size;
    const contentLength = content.length;
    const linkDensity = totalLinks / Math.max(contentLength / 1000, 1); // 每1000字符的链接数

    // 计算链接间平均距离
    let totalDistance = 0;
    if (links.length > 1) {
      for (let i = 1; i < links.length; i++) {
        totalDistance += links[i].position - links[i-1].position;
      }
    }
    const averageDistance = links.length > 1 ? totalDistance / (links.length - 1) : 0;

    // 验证规则
    if (totalLinks > 2) {
      issues.push(`过多产品链接: ${totalLinks} 个 (推荐最多2个)`);
    }

    if (totalLinks !== uniqueProducts) {
      issues.push(`重复产品链接: ${totalLinks - uniqueProducts} 个重复`);
    }

    if (linkDensity > 3) {
      issues.push(`链接密度过高: ${linkDensity.toFixed(1)} 个/1000字符 (推荐<3)`);
    }

    if (averageDistance > 0 && averageDistance < 200) {
      issues.push(`链接过于密集: 平均间距 ${Math.round(averageDistance)} 字符 (推荐>200)`);
    }

    // 检查同一行重复链接
    const lines = content.split('\n');
    lines.forEach((line, lineIndex) => {
      const lineLinks = Array.from(line.matchAll(linkRegex));
      if (lineLinks.length > 1) {
        const urls = lineLinks.map(match => match[2]);
        const duplicateUrls = urls.filter((url, index) => urls.indexOf(url) !== index);
        if (duplicateUrls.length > 0) {
          issues.push(`第${lineIndex + 1}行存在重复链接: ${duplicateUrls[0]}`);
        }
      }
    });

    return {
      isValid: issues.length === 0,
      issues,
      stats: {
        totalLinks,
        uniqueProducts,
        linkDensity: Math.round(linkDensity * 10) / 10,
        averageDistance: Math.round(averageDistance)
      }
    };
  }
}

export const contentService = new ContentService(); 