---
import { getCollection } from 'astro:content';
import BaseLayout from '../layouts/BaseLayout.astro';
import Header from '../components/Header.astro';
import Footer from '../components/Footer.astro';

// Get all posts for search index
const allPosts = await getCollection('news', ({ data }) => {
  return data.draft !== true;
});

// Create search index data
const searchIndex = allPosts.map(post => ({
  id: post.id,
  title: post.data.title,
  description: post.data.description || '',
  category: post.data.category,
  tags: post.data.tags || [],
  author: post.data.author || '',
  image: post.data.heroImage || '',
  url: `/news/${post.id}`,
  date: post.data.date.toISOString(),
  featured: post.data.featured || false
}));

// Popular search terms
const popularSearches = [
  'hypersonic', 'satellite', 'defense', 'aerospace', 'AI', 'quantum', 'space', 'radar'
];

// Recent searches (could be enhanced with actual analytics)
const trendingTopics = [
  'Quantum radar breakthrough',
  'Space elevator technology',
  'AI in aviation',
  'Hypersonic systems'
];
---

<BaseLayout 
  title="Search Articles | Aerospace & Defense News"
  description="Search through our comprehensive database of aerospace and defense industry news and insights."
>
  <Header />
  
  <!-- Hero Section -->
  <section class="bg-gradient-to-br from-primary-50 to-secondary-50 relative overflow-hidden">
    <div class="absolute inset-0 bg-grid-white/[0.05] bg-[size:20px_20px]"></div>
    <div class="container-page section-spacing relative">
      <div class="text-center max-w-4xl mx-auto animate-fade-in">
        <h1 class="heading-xl text-secondary-900 mb-6">
          Search <span class="text-gradient">Knowledge Base</span>
        </h1>
        <p class="body-lg text-secondary-600 mb-8 max-w-3xl mx-auto">
          Discover insights from our comprehensive database of aerospace and defense industry news, 
          research, and expert analysis.
        </p>
      </div>
    </div>
  </section>

  <main class="bg-white">
    <!-- Search Interface -->
    <section class="container-page py-12">
      <div 
        class="max-w-4xl mx-auto"
        x-data={`{
          query: '${Astro.url.searchParams.get('q') || ''}',
          results: [],
          isLoading: false,
          selectedCategory: '',
          searchIndex: ${JSON.stringify(searchIndex)},
          fuse: null,
          fuseLoaded: false,
          
          init() {
            console.log('Search initialized with', this.searchIndex.length, 'articles');
            // Initialize Fuse.js when script loads
            this.initializeFuse();
            
            // Search immediately if query exists
            if (this.query) {
              this.search();
            }
          },
          
          async initializeFuse() {
            console.log('Initializing Fuse.js...');
            try {
              // Check if Fuse is already available
              if (typeof window.Fuse !== 'undefined') {
                this.setupFuse();
                return;
              }
              
              // Load Fuse.js dynamically
              const script = document.createElement('script');
              script.src = 'https://cdn.jsdelivr.net/npm/fuse.js@6.6.2/dist/fuse.min.js';
              script.onload = () => {
                console.log('Fuse.js loaded successfully');
                this.setupFuse();
              };
              script.onerror = () => {
                console.error('Failed to load Fuse.js');
                // Fallback to simple search
                this.fuseLoaded = false;
              };
              document.head.appendChild(script);
            } catch (error) {
              console.error('Error initializing Fuse:', error);
              this.fuseLoaded = false;
            }
          },
          
          setupFuse() {
            try {
              const options = {
                keys: [
                  { name: 'title', weight: 0.4 },
                  { name: 'description', weight: 0.3 },
                  { name: 'tags', weight: 0.2 },
                  { name: 'author', weight: 0.1 }
                ],
                threshold: 0.3,
                includeScore: true,
                includeMatches: true,
                minMatchCharLength: 2
              };
              
              this.fuse = new Fuse(this.searchIndex, options);
              this.fuseLoaded = true;
              console.log('Fuse.js configured successfully');
              
              // If there's a pending search, execute it now
              if (this.query) {
                this.search();
              }
            } catch (error) {
              console.error('Error setting up Fuse:', error);
              this.fuseLoaded = false;
            }
          },
          
          search() {
            if (!this.query.trim()) {
              this.results = [];
              return;
            }
            
            console.log('Searching for:', this.query);
            this.isLoading = true;
            
            // Simulate loading for better UX
            setTimeout(() => {
              let searchResults = [];
              
              if (this.fuseLoaded && this.fuse) {
                // Use Fuse.js for fuzzy search
                console.log('Using Fuse.js search');
                searchResults = this.fuse.search(this.query);
              } else {
                // Fallback to simple string search
                console.log('Using fallback search');
                searchResults = this.simpleSearch();
              }
              
              // Filter by category if selected
              if (this.selectedCategory) {
                searchResults = searchResults.filter(result => {
                  const item = result.item || result;
                  return item.category === this.selectedCategory;
                });
              }
              
              this.results = searchResults.slice(0, 20);
              this.isLoading = false;
              
              console.log('Search results:', this.results.length);
              
              // Update URL
              const url = new URL(window.location);
              if (this.query) {
                url.searchParams.set('q', this.query);
              } else {
                url.searchParams.delete('q');
              }
              window.history.replaceState({}, '', url);
            }, 200);
          },
          
          simpleSearch() {
            const query = this.query.toLowerCase();
            return this.searchIndex
              .filter(item => {
                return item.title.toLowerCase().includes(query) ||
                       (item.description && item.description.toLowerCase().includes(query)) ||
                       (item.tags && item.tags.some(tag => tag.toLowerCase().includes(query))) ||
                       (item.author && item.author.toLowerCase().includes(query));
              })
              .map(item => ({ item, score: 0 }));
          },
          
          setQuery(newQuery) {
            this.query = newQuery;
            this.search();
          },
          
          highlightText(text, matches) {
            if (!text) return '';
            if (!matches || matches.length === 0 || !this.fuseLoaded) {
              // Simple highlighting for fallback search
              let result = text;
              const terms = this.query.split(' ');
              terms.forEach(term => {
                if (term.length > 1) {
                  const regex = new RegExp(\`(\${term})\`, 'gi');
                  result = result.replace(regex, '<mark class="bg-accent-100 text-accent-800 px-1 rounded">$1</mark>');
                }
              });
              return result;
            }
            
            // Fuse.js highlighting
            let result = text;
            const terms = this.query.split(' ');
            terms.forEach(term => {
              if (term.length > 1) {
                const regex = new RegExp(\`(\${term})\`, 'gi');
                result = result.replace(regex, '<mark class="bg-accent-100 text-accent-800 px-1 rounded">$1</mark>');
              }
            });
            return result;
          }
        }`}
      >
        <!-- Search Input -->
        <div class="relative mb-8 animate-fade-in">
          <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
            <svg class="h-6 w-6 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          <input 
            type="text"
            x-model="query"
            @input.debounce.300ms="search()"
            placeholder="Search articles, topics, or keywords..."
            class="block w-full pl-12 pr-4 py-4 border border-secondary-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-lg shadow-soft transition-all duration-200"
          />
          <div 
            x-show="isLoading" 
            class="absolute inset-y-0 right-0 pr-4 flex items-center"
          >
            <div class="loading-spinner w-5 h-5"></div>
          </div>
        </div>

        <!-- Search Categories -->
        <div class="flex flex-wrap gap-3 mb-8 animate-fade-in" style="animation-delay: 200ms">
          <span class="text-sm font-medium text-secondary-700">Filter by category:</span>
          <button 
            @click="selectedCategory = ''; search()"
            :class="selectedCategory === '' ? 'badge-primary' : 'badge-secondary'"
            class="badge transition-all duration-200"
          >
            All
          </button>
          <button 
            @click="selectedCategory = 'industry'; search()"
            :class="selectedCategory === 'industry' ? 'badge-primary' : 'badge-secondary'"
            class="badge transition-all duration-200"
          >
            Industry
          </button>
          <button 
            @click="selectedCategory = 'research'; search()"
            :class="selectedCategory === 'research' ? 'badge-primary' : 'badge-secondary'"
            class="badge transition-all duration-200"
          >
            Research
          </button>
          <button 
            @click="selectedCategory = 'events'; search()"
            :class="selectedCategory === 'events' ? 'badge-primary' : 'badge-secondary'"
            class="badge transition-all duration-200"
          >
            Events
          </button>
          <button 
            @click="selectedCategory = 'frontier'; search()"
            :class="selectedCategory === 'frontier' ? 'badge-primary' : 'badge-secondary'"
            class="badge transition-all duration-200"
          >
            Frontier
          </button>
          <button 
            @click="selectedCategory = 'insight'; search()"
            :class="selectedCategory === 'insight' ? 'badge-primary' : 'badge-secondary'"
            class="badge transition-all duration-200"
          >
            Insights
          </button>
          <button 
            @click="selectedCategory = 'misc'; search()"
            :class="selectedCategory === 'misc' ? 'badge-primary' : 'badge-secondary'"
            class="badge transition-all duration-200"
          >
            More
          </button>
        </div>

        <!-- Quick Search Suggestions -->
        <div x-show="!query && !results.length" class="animate-fade-in" style="animation-delay: 400ms">
          <div class="grid md:grid-cols-2 gap-8 mb-12">
            <!-- Popular Searches -->
            <div class="card p-6">
              <h3 class="text-lg font-bold text-secondary-900 mb-4 flex items-center">
                <svg class="w-5 h-5 mr-2 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
                Popular Searches
              </h3>
              <div class="flex flex-wrap gap-2">
                {popularSearches.map(term => (
                  <button 
                    @click="setQuery('{term}')"
                    class="bg-secondary-100 hover:bg-primary-100 text-secondary-700 hover:text-primary-700 text-sm px-3 py-2 rounded-lg transition-all duration-200 hover:scale-105"
                  >
                    {term}
                  </button>
                ))}
              </div>
            </div>

            <!-- Trending Topics -->
            <div class="card p-6">
              <h3 class="text-lg font-bold text-secondary-900 mb-4 flex items-center">
                <svg class="w-5 h-5 mr-2 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 18.657A8 8 0 016.343 7.343S7 9 9 10c0-2 .5-5 2.986-7C14 5 16.09 5.777 17.656 7.343A7.975 7.975 0 0120 13a7.975 7.975 0 01-2.343 5.657z" />
                </svg>
                Trending Topics
              </h3>
              <div class="space-y-2">
                {trendingTopics.map(topic => (
                  <button 
                    @click="setQuery('{topic}')"
                    class="block w-full text-left text-sm text-secondary-600 hover:text-primary-600 hover:bg-primary-50 p-2 rounded-lg transition-all duration-200"
                  >
                    {topic}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        <!-- Results Count -->
        <div x-show="!isLoading && query && results.length > 0" class="text-sm text-secondary-600 mb-6">
          Found <span x-text="results.length" class="font-semibold"></span> result<span x-show="results.length !== 1">s</span> 
          for "<span x-text="query" class="font-medium text-secondary-900"></span>"
        </div>

        <!-- No Results -->
        <div x-show="!isLoading && query && results.length === 0" class="text-center py-20 animate-fade-in">
          <div class="w-24 h-24 mx-auto mb-6 bg-secondary-100 rounded-3xl flex items-center justify-center text-4xl">
            🔍
          </div>
          <h3 class="heading-md text-secondary-900 mb-4">No results found</h3>
          <p class="body-lg text-secondary-600 mb-8 max-w-2xl mx-auto">
            We couldn't find any articles matching "<span x-text="query" class="font-medium"></span>". 
            Try different keywords or browse our categories.
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <button @click="query = ''; selectedCategory = ''" class="btn-secondary">
              Clear search
            </button>
            <a href="/" class="btn-primary">
              Browse latest articles
            </a>
          </div>
        </div>

        <!-- Search Results -->
        <div x-show="!isLoading && results.length > 0" class="space-y-6 animate-fade-in">
          <template x-for="(result, index) in results" :key="result.item.id">
            <article class="card-interactive p-6 animate-fade-in" :style="`animation-delay: ${index * 50}ms`">
              <div class="flex flex-col lg:flex-row lg:items-start gap-6">
                <!-- Article Image -->
                <div x-show="result.item.image" class="flex-shrink-0">
                  <img 
                    :src="result.item.image"
                    :alt="result.item.title"
                    class="w-full lg:w-32 h-32 object-cover rounded-lg"
                    loading="lazy"
                  />
                </div>
                
                <!-- Article Content -->
                <div class="flex-1 min-w-0">
                  <!-- Meta Info -->
                  <div class="flex items-center flex-wrap gap-3 mb-3">
                    <span class="badge badge-primary" x-text="result.item.category"></span>
                    <time class="text-sm text-secondary-500" x-text="new Date(result.item.date).toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })"></time>
                    <span x-show="result.item.featured" class="badge badge-accent">Featured</span>
                  </div>
                  
                  <!-- Title -->
                  <h3 class="text-xl font-bold text-secondary-900 mb-3 hover:text-primary-600 transition-colors">
                    <a 
                      :href="result.item.url"
                      class="focus-outline"
                      x-html="highlightText(result.item.title, result.matches)"
                    ></a>
                  </h3>
                  
                  <!-- Description -->
                  <p 
                    class="text-secondary-600 mb-4 line-clamp-2"
                    x-html="highlightText(result.item.description, result.matches)"
                  ></p>
                  
                  <!-- Footer -->
                  <div class="flex items-center justify-between">
                    <div class="flex items-center gap-3">
                      <span x-show="result.item.author" class="text-sm text-secondary-500">
                        By <span x-text="result.item.author" class="font-medium"></span>
                      </span>
                      <div x-show="result.item.tags && result.item.tags.length" class="flex gap-1">
                        <template x-for="tag in result.item.tags.slice(0, 2)" :key="tag">
                          <span class="bg-secondary-100 text-secondary-600 text-xs px-2 py-1 rounded-full" x-text="'#' + tag"></span>
                        </template>
                      </div>
                    </div>
                    
                    <a 
                      :href="result.item.url"
                      class="inline-flex items-center text-primary-600 hover:text-primary-700 font-semibold text-sm transition-colors group"
                    >
                      Read article
                      <svg class="w-3 h-3 ml-1 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                      </svg>
                    </a>
                  </div>
                </div>
              </div>
            </article>
          </template>
        </div>
      </div>
    </section>
  </main>

  <Footer />
</BaseLayout>

<style>
  /* Line clamp utility */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Animation for staggered entrance */
  .animate-fade-in {
    animation: fadeIn 0.6s ease-out both;
  }
  
  @media (prefers-reduced-motion: reduce) {
    .animate-fade-in {
      animation: none;
    }
  }

  /* Search result highlighting */
  mark {
    background-color: rgb(254 243 199); /* accent-100 */
    color: rgb(146 64 14); /* accent-800 */
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
  }
</style> 