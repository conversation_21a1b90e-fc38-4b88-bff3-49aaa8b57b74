import { planRepository, taskRepository, configRepository } from '../database/repositories.js';
import { OpenAI } from 'openai';
import type { PlanData, PlanTopic, TrendAnalysis } from '@news-site/shared';
import { logger } from '../../utils/logger.js';
import { ContentGeneratorAgent } from '../agents/content-generator-agent.js';

export interface CreatePlanRequest {
  date: string;
  maxTasks?: number;
  categories?: string[];
  customPrompt?: string;
  forceRegenerate?: boolean;
}

export interface PlanResponse {
  id: string;
  date: string;
  status: string;
  tasks: any[];
  stats: {
    totalTasks: number;
    highPriorityTasks: number;
    categoriesDistribution: Record<string, number>;
  };
}

/**
 * 计划服务
 * 提供计划相关的业务逻辑
 */
export class PlanService {
  private openai: OpenAI | null;
  private readonly categories = ['industry', 'research', 'events', 'frontier', 'insight', 'misc'];
  private readonly keywords = [
    'hypersonic', 'satellite', 'aerospace', 'defense', 'military',
    'space exploration', 'commercial aviation', 'drone technology',
    'artificial intelligence', 'cybersecurity', 'radar systems',
    'stealth technology', 'missile defense', 'propulsion',
    'spacecraft', 'space station', 'mars mission', 'lunar base'
  ];

  constructor() {
    this.openai = process.env.OPENAI_API_KEY ? new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    }) : null;
  }

  /**
   * 创建新计划
   */
  async createPlan(request: CreatePlanRequest): Promise<PlanResponse> {
    const {
      date,
      maxTasks = 10,
      categories = ['industry', 'research', 'frontier'],
      customPrompt,
      forceRegenerate = false
    } = request;

    // 检查是否已存在该日期的计划
    const existingPlan = await planRepository.getByDate(date);
    if (existingPlan && !forceRegenerate) {
      throw new Error(`Plan for date ${date} already exists`);
    }

    // 如果强制重新生成，删除现有计划和任务
    if (existingPlan && forceRegenerate) {
      // 删除现有任务
      const existingTasks = await taskRepository.getByPlanId(existingPlan.id);
      for (const task of existingTasks) {
        await taskRepository.delete(task.id);
      }
      // 删除现有计划
      await planRepository.delete(existingPlan.id);
    }

    // 创建计划
    const plan = await planRepository.create({
      date,
      status: 'draft',
      maxTasks,
      categories,
      customPrompt
    });

    // 生成任务（简化版本）
    const tasks = await this.generateTasks(plan.id, maxTasks, categories);

    // 更新计划状态
    await planRepository.update(plan.id, { status: 'active' });

    // 计算统计信息
    const stats = this.calculateStats(tasks);

    return {
      id: plan.id,
      date: plan.date,
      status: 'active',
      tasks,
      stats
    };
  }

  /**
   * 获取计划列表
   */
  async getPlans(): Promise<any[]> {
    return await planRepository.getAll();
  }

  /**
   * 根据ID获取计划
   */
  async getPlanById(id: string): Promise<any> {
    const plan = await planRepository.getById(id);
    if (!plan) {
      throw new Error('Plan not found');
    }

    const tasks = await taskRepository.getByPlanId(id);
    const stats = this.calculateStats(tasks);

    return {
      ...plan,
      tasks,
      stats
    };
  }

  /**
   * 根据日期获取计划
   */
  async getPlanByDate(date: string): Promise<any> {
    const plan = await planRepository.getByDate(date);
    if (!plan) {
      throw new Error('Plan not found for date');
    }

    const tasks = await taskRepository.getByPlanId(plan.id);
    const stats = this.calculateStats(tasks);

    return {
      ...plan,
      tasks,
      stats
    };
  }

  /**
   * 生成任务（AI增强版本）
   */
  private async generateTasks(planId: string, maxTasks: number, categories: string[]): Promise<any[]> {
    // 先尝试AI生成
    if (this.openai) {
      try {
        const aiTasks = await this.generateAITasks(planId, maxTasks, categories);
        if (aiTasks.length > 0) {
          return aiTasks;
        }
      } catch (error) {
        console.warn('AI任务生成失败，使用模板方案:', error);
      }
    }

    // 扩展的任务模板池（避免重复）
    const allTaskTemplates: Array<{
      title: string;
      category: "industry" | "research" | "frontier" | "events" | "insight" | "misc";
      description: string;
      keyword: string;
    }> = [
      // Industry 分类
      {
        title: `Advanced Hypersonic Technology Integration in Defense Systems - ${new Date().getFullYear()}`,
        category: "industry" as const,
        description: "Exploring the latest developments in hypersonic technology and their integration into modern defense systems.",
        keyword: "hypersonic technology"
      },
      {
        title: "Next-Generation Satellite Constellation Networks Deployment",
        category: "industry" as const,
        description: "Major satellite constellation networks expanding global communications and internet coverage.",
        keyword: "satellite constellation"
      },
      {
        title: "Electric Aircraft Propulsion Systems Market Analysis",
        category: "industry" as const,
        description: "Analysis of the growing market for electric propulsion in commercial and military aircraft.",
        keyword: "electric aircraft"
      },
      {
        title: "Advanced Materials in Aerospace Manufacturing Processes",
        category: "industry" as const,
        description: "Revolutionary materials and manufacturing techniques transforming aerospace production.",
        keyword: "aerospace materials"
      },
      // Research 分类
      {
        title: "Commercial Space Station Development Programs Update",
        category: "research" as const,
        description: "Recent progress in private sector space station development as ISS approaches end of life.",
        keyword: "commercial space station"
      },
      {
        title: "Quantum Radar Technology Breakthrough in Military Applications",
        category: "research" as const,
        description: "Recent breakthroughs in quantum radar technology and their potential military applications.",
        keyword: "quantum radar"
      },
      {
        title: "Space Debris Mitigation Research and Active Removal Technologies",
        category: "research" as const,
        description: "Latest research into technologies for removing and preventing space debris accumulation.",
        keyword: "space debris removal"
      },
      {
        title: "Lunar Mining Operations Feasibility Studies and Technical Challenges",
        category: "research" as const,
        description: "Current research into the technical and economic feasibility of lunar resource extraction.",
        keyword: "lunar mining"
      },
      // Frontier 分类
      {
        title: "AI-Powered Autonomous Defense Systems Field Testing Results",
        category: "frontier" as const,
        description: "Latest field testing results from AI-powered autonomous defense and surveillance systems.",
        keyword: "autonomous defense systems"
      },
      {
        title: "Space-Based Solar Power Generation Systems Development Status",
        category: "frontier" as const,
        description: "Progress in developing orbital solar power collection and transmission systems.",
        keyword: "space solar power"
      },
      {
        title: "Neural Interface Technology for Advanced Pilot Training Systems",
        category: "frontier" as const,
        description: "Cutting-edge brain-computer interfaces enhancing pilot training and aircraft control.",
        keyword: "neural interface aviation"
      },
      {
        title: "Metamaterial Stealth Technology Applications in Next-Gen Aircraft",
        category: "frontier" as const,
        description: "Revolutionary metamaterial applications creating unprecedented stealth capabilities.",
        keyword: "metamaterial stealth"
      }
    ];

    // 按分类过滤并随机打乱
    const filteredTemplates = allTaskTemplates
      .filter(t => categories.includes(t.category))
      .sort(() => Math.random() - 0.5); // 随机打乱

    const tasks: any[] = [];
    const timestamp = Date.now();

    for (let i = 0; i < Math.min(maxTasks, filteredTemplates.length); i++) {
      const template = filteredTemplates[i];
      
      // 添加时间戳和随机性使标题独特
      const uniqueTitle = template.title.includes(new Date().getFullYear().toString()) 
        ? template.title 
        : `${template.title} - ${new Date().toLocaleDateString()}`;
      
      const task = await taskRepository.create({
        planId,
        title: uniqueTitle,
        description: template.description,
        keyword: template.keyword,
        targetCategory: template.category,
        priority: Math.floor(Math.random() * 5) + 1,
        relatedProducts: this.suggestRelatedProducts(template),
        estimatedDuration: this.estimateWordCount(template.category)
      });

      tasks.push(task);
    }

    return tasks;
  }

  /**
   * AI生成动态任务
   */
  private async generateAITasks(planId: string, maxTasks: number, categories: string[]): Promise<any[]> {
    const prompt = `Generate ${maxTasks} unique and current aerospace/defense news article tasks.

Current date: ${new Date().toLocaleDateString()}
Categories: ${categories.join(', ')}

Requirements:
- Each task must be UNIQUE and TIMELY
- Focus on recent developments and emerging trends
- Include specific keywords for SEO
- Vary the topics to avoid repetition

Format as JSON array with objects containing:
- title: Specific, newsworthy headline
- category: One of [${categories.join(', ')}]
- description: Brief 1-2 sentence description
- keyword: Main SEO keyword/phrase

Example output:
[
  {
    "title": "Boeing's New Hypersonic Test Vehicle Achieves Mach 7 in Latest Trial",
    "category": "industry",
    "description": "Boeing successfully tests next-generation hypersonic vehicle reaching unprecedented speeds.",
    "keyword": "hypersonic vehicle testing"
  }
]`;

    const response = await this.openai!.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [
        {
          role: "system",
          content: "You are an aerospace industry news expert. Generate diverse, current, and unique article topics."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.8, // 增加随机性
      max_tokens: 2000
    });

    const content = response.choices[0].message.content;
    const jsonMatch = content?.match(/\[[\s\S]*\]/);
    
    if (jsonMatch) {
      const aiTemplates = JSON.parse(jsonMatch[0]);
      const tasks: any[] = [];

      for (const template of aiTemplates.slice(0, maxTasks)) {
        const task = await taskRepository.create({
          planId,
          title: template.title,
          description: template.description,
          keyword: template.keyword,
          targetCategory: template.category,
          priority: Math.floor(Math.random() * 5) + 1,
          relatedProducts: this.suggestRelatedProducts(template),
          estimatedDuration: this.estimateWordCount(template.category)
        });

        tasks.push(task);
      }

      return tasks;
    }

    throw new Error('Failed to parse AI response');
  }

  /**
   * 计算统计信息
   */
  private calculateStats(tasks: any[]): any {
    const stats = {
      totalTasks: tasks.length,
      highPriorityTasks: tasks.filter(t => t.priority >= 4).length,
      categoriesDistribution: {} as Record<string, number>
    };

    // 计算分类分布
    for (const task of tasks) {
      const category = task.targetCategory;
      stats.categoriesDistribution[category] = (stats.categoriesDistribution[category] || 0) + 1;
    }

    return stats;
  }

  /**
   * 建议相关产品
   */
  private suggestRelatedProducts(template: any): string[] {
    const productMappings: Record<string, string[]> = {
      'hypersonic': ['hypersonic-systems'],
      'satellite': ['satellite-communication'],
      'radar': ['radar-systems'],
      'quantum': ['radar-systems'],
      'defense': ['hypersonic-systems', 'radar-systems'],
      'space': ['satellite-communication']
    };

    const products = new Set<string>();
    const text = (template.title + ' ' + template.description).toLowerCase();
    
    Object.entries(productMappings).forEach(([keyword, prods]) => {
      if (text.includes(keyword)) {
        prods.forEach(prod => products.add(prod));
      }
    });

    return Array.from(products);
  }

  /**
   * 估算字数
   */
  private estimateWordCount(category: string): number {
    const wordCounts: Record<string, number> = {
      'industry': 800,
      'research': 1000,
      'events': 600,
      'frontier': 900,
      'insight': 700,
      'misc': 500
    };

    return wordCounts[category] || 700;
  }

  async generateTopics(): Promise<PlanTopic[]> {
    const prompt = `Generate 5 newsworthy article topics for an aerospace and defense news website. 

    Categories to choose from: ${this.categories.join(', ')}
    
    Current trends to consider:
    - Hypersonic flight technology developments
    - Commercial space industry growth
    - AI integration in defense systems
    - Satellite constellation expansion
    - Next-generation fighter aircraft
    - Space debris management
    - Lunar exploration programs
    - Cybersecurity in aerospace
    
    For each topic, provide:
    1. A compelling headline
    2. Category (from the list above)
    3. Brief description (1-2 sentences)
    4. Potential sources to research
    5. Target deadline (within next 7 days)
    
    Format as JSON array with objects containing: title, category, description, sources, deadline`;

    try {
      if (!this.openai) {
        logger.warn('OpenAI client not available, using fallback topics');
        return this.generateFallbackTopics();
      }
      
      const response = await this.openai.chat.completions.create({
        model: "gpt-4o",
        messages: [
          {
            role: "system",
            content: "You are an expert aerospace and defense industry journalist. Generate timely, relevant, and technically accurate article topics."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 2000
      });

      const content = response.choices[0].message.content;
      
      // Extract JSON from response
      const jsonMatch = content?.match(/\[[\s\S]*\]/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
      
      throw new Error('No valid JSON found in response');
    } catch (error) {
      logger.error('Error generating topics:', error);
      return this.generateFallbackTopics();
    }
  }

  private generateFallbackTopics(): PlanTopic[] {
    const today = new Date();
    const getDeadline = (days: number): string => {
      const deadline = new Date(today);
      deadline.setDate(today.getDate() + days);
      return deadline.toISOString().split('T')[0];
    };

    return [
      {
        title: "Next-Generation Stealth Technology Developments in Fighter Aircraft",
        category: "industry",
        description: "Exploring the latest advancements in stealth technology and their impact on modern fighter aircraft design.",
        sources: ["defense contractors", "military publications", "aerospace conferences"],
        deadline: getDeadline(3)
      },
      {
        title: "Commercial Space Station Projects Accelerate Development Timeline", 
        category: "research",
        description: "Private companies race to develop commercial space stations as the ISS approaches retirement.",
        sources: ["space agencies", "commercial space companies", "industry reports"],
        deadline: getDeadline(5)
      },
      {
        title: "AI-Powered Autonomous Defense Systems Undergo Field Testing",
        category: "frontier",
        description: "Military organizations test new AI systems for autonomous threat detection and response.",
        sources: ["defense research labs", "military contractors", "AI companies"],
        deadline: getDeadline(4)
      },
      {
        title: "Satellite Constellation Networks Transform Global Communications",
        category: "industry", 
        description: "Low Earth Orbit satellite networks revolutionize internet connectivity and emergency communications.",
        sources: ["satellite operators", "telecommunications companies", "space agencies"],
        deadline: getDeadline(6)
      },
      {
        title: "Hypersonic Missile Defense Systems Enter Development Phase",
        category: "research",
        description: "New defense technologies aim to counter the growing hypersonic weapon threat.",
        sources: ["defense contractors", "government agencies", "research institutions"],
        deadline: getDeadline(7)
      }
    ];
  }

  async analyzeTrends(): Promise<TrendAnalysis> {
    // In production, this would integrate with:
    // - Google Trends API
    // - News API
    // - Social media APIs
    // - Industry report APIs
    
    return {
      trending_keywords: [
        { keyword: "hypersonic", growth: "+45%", relevance: "high" },
        { keyword: "space economy", growth: "+32%", relevance: "high" },
        { keyword: "drone warfare", growth: "+28%", relevance: "medium" },
        { keyword: "quantum radar", growth: "+67%", relevance: "medium" },
        { keyword: "space debris", growth: "+23%", relevance: "high" }
      ],
      seasonal_trends: {
        current_season: "Q4 2024",
        focus_areas: ["year-end reviews", "budget announcements", "next year predictions"]
      },
      competitive_analysis: {
        top_topics: ["space exploration", "defense technology", "aviation industry"],
        content_gaps: ["small satellite technology", "green aviation", "cyber warfare"]
      }
    };
  }

  async generatePlan(): Promise<PlanData> {
    logger.info('Generating content plan...');
    
    const [topics, trends] = await Promise.all([
      this.generateTopics(),
      this.analyzeTrends()
    ]);

    const plan: PlanData = {
      id: `plan_${Date.now()}`,
      generated_at: new Date().toISOString(),
      trends_analysis: trends,
      content_tasks: topics.map((topic, index) => ({
        id: `task_${Date.now()}_${index}`,
        title: topic.title,
        category: topic.category,
        description: topic.description,
        sources: topic.sources,
        deadline: topic.deadline,
        priority: this.calculatePriority(topic, trends),
        estimated_words: this.estimateWordCount(topic.category),
        related_products: this.suggestRelatedProducts(topic),
        seo_keywords: this.extractSEOKeywords(topic.title + ' ' + topic.description),
        status: 'planned'
      })),
      next_generation: {
        scheduled_for: this.getNextGenerationDate(),
        focus_areas: trends.competitive_analysis.content_gaps
      }
    };

    return plan;
  }

  private calculatePriority(topic: PlanTopic, trends: TrendAnalysis): 'high' | 'medium' | 'low' {
    const categoryWeights: Record<string, number> = {
      'industry': 3,
      'frontier': 4,
      'research': 3,
      'events': 2,
      'insight': 2,
      'misc': 1
    };

    let score = categoryWeights[topic.category] || 1;
    
    // Boost score if topic contains trending keywords
    trends.trending_keywords.forEach(trend => {
      if (topic.title.toLowerCase().includes(trend.keyword.toLowerCase()) ||
          topic.description.toLowerCase().includes(trend.keyword.toLowerCase())) {
        score += trend.relevance === 'high' ? 2 : 1;
      }
    });

    if (score >= 6) return 'high';
    if (score >= 4) return 'medium';
    return 'low';
  }

  private extractSEOKeywords(text: string): string[] {
    const words = text.toLowerCase().match(/\b\w{4,}\b/g) || [];
    const filtered = words.filter(word => 
      !['this', 'that', 'with', 'from', 'they', 'have', 'will', 'been', 'were'].includes(word)
    );
    
    return [...new Set(filtered)].slice(0, 10);
  }

  private getNextGenerationDate(): string {
    const nextWeek = new Date();
    nextWeek.setDate(nextWeek.getDate() + 7);
    return nextWeek.toISOString().split('T')[0];
  }

  /**
   * 执行任务 - 根据关键词生成文章内容
   */
  async executeTask(taskId: string): Promise<any> {
    const task = await taskRepository.getById(taskId);
    if (!task) {
      throw new Error('Task not found');
    }

    if (task.status === 'completed') {
      throw new Error('Task already completed');
    }

    try {
      logger.info(`Executing task: ${task.title} with keyword: ${task.keyword}`);
      
      // 更新任务状态为执行中
      await taskRepository.update(taskId, { status: 'in-progress' });

      // 使用ContentGeneratorAgent生成文章
      const contentGenerator = new ContentGeneratorAgent();
      await contentGenerator.initialize();
      
      const contentOutput = await contentGenerator.execute({
        taskId: task.id,
        keyword: task.keyword,
        relatedProducts: task.relatedProducts || [],
        generateImage: true,
        dryRun: false
      }, {
        requestId: `task-${task.id}`,
        userId: 'system',
        metadata: {}
      });

      // 更新任务状态
      await taskRepository.update(taskId, {
        status: 'completed'
      });

      logger.info(`Task ${taskId} completed successfully`);
      return {
        taskId,
        status: 'completed',
        content: contentOutput
      };

    } catch (error) {
      logger.error(`Task ${taskId} execution failed:`, error);
      
      // 更新任务状态为失败
      await taskRepository.update(taskId, {
        status: 'failed',
        errorMessage: error instanceof Error ? error.message : 'Unknown error'
      });

      throw error;
    }
  }

  /**
   * 批量执行计划中的所有待执行任务
   */
  async executePlan(planId: string, maxConcurrent: number = 3): Promise<any> {
    const plan = await planRepository.getById(planId);
    if (!plan) {
      throw new Error('Plan not found');
    }

    const allTasks = await taskRepository.getByPlanId(planId);
    const pendingTasks = allTasks.filter(task => task.status === 'pending');
    if (pendingTasks.length === 0) {
      return { message: 'No pending tasks to execute' };
    }

    logger.info(`Executing plan ${planId} with ${pendingTasks.length} tasks`);

    const results: any[] = [];
    const errors: any[] = [];

    // 分批执行任务以控制并发
    for (let i = 0; i < pendingTasks.length; i += maxConcurrent) {
      const batch = pendingTasks.slice(i, i + maxConcurrent);
      const batchPromises = batch.map(async (task) => {
        try {
          const result = await this.executeTask(task.id);
          results.push(result);
          return result;
        } catch (error) {
          const errorInfo = {
            taskId: task.id,
            error: error instanceof Error ? error.message : 'Unknown error'
          };
          errors.push(errorInfo);
          return errorInfo;
        }
      });

      await Promise.allSettled(batchPromises);
      
      // 在批次之间添加短暂延迟，避免API速率限制
      if (i + maxConcurrent < pendingTasks.length) {
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }

    // 更新计划状态
    const remainingTasks = await taskRepository.getByPlanId(planId);
    const allCompleted = remainingTasks.filter(task => task.status === 'pending');
    if (allCompleted.length === 0) {
      await planRepository.update(planId, { status: 'completed' });
    }

    return {
      planId,
      totalTasks: pendingTasks.length,
      successful: results.length,
      failed: errors.length,
      results,
      errors
    };
  }
}

// 单例导出
export const planService = new PlanService(); 