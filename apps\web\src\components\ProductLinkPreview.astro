---
/**
 * 产品链接悬停预览组件
 * 为文章中的产品链接添加悬停预览功能，提供更好的用户体验
 * 使用静态数据，无需API调用，性能更佳
 */
import serverProductService from '../utils/serverProductService';

// 在构建时加载所有产品数据
await serverProductService.initialize();
const allProducts = await serverProductService.getAllProducts();

// 创建产品映射表，支持多种ID格式
const productMap = new Map();
const productByUrl = new Map();

allProducts.forEach(product => {
  // 按ID索引（优先使用id，然后使用任何可能的模型字段）
  const productId = product.id || product['model'];
  if (productId) {
    productMap.set(productId.toLowerCase(), product);
  }
  
  // 按URL路径索引
  if (product.url) {
    const urlParts = product.url.split('/');
    const urlProductId = urlParts[urlParts.length - 1];
    if (urlProductId) {
      productMap.set(urlProductId.toLowerCase(), product);
      productByUrl.set(product.url, product);
    }
  }
  
  // 按关键词索引
  if (product.keywords) {
    product.keywords.forEach(keyword => {
      if (keyword.length > 3) { // 避免太短的关键词
        productMap.set(keyword.toLowerCase(), product);
      }
    });
  }
});

// 将产品数据内联到页面中
const productData = Object.fromEntries(productMap);
---

<script is:inline define:vars={{ productData, allProducts }}>
  // 产品预览数据缓存
  const productCache = new Map();
  
  // 将长路径URL转换为短链接格式
  function convertToShortUrl(url) {
    if (!url || !url.includes('gnc-tech.com')) {
      return url;
    }

    // 如果已经是短链接格式，直接返回
    if (url.match(/\/products\/[^\/]+$/)) {
      return url;
    }

    // 从长路径中提取产品标识符
    const pathMatch = url.match(/\/products\/.*\/([^\/]+)$/);
    if (pathMatch) {
      const productIdentifier = pathMatch[1];
      const baseUrl = url.includes('www.gnc-tech.com') ? 'https://www.gnc-tech.com' : 'https://gnc-tech.com';

      // 根据产品标识符生成合适的短链接
      let shortPath = productIdentifier.toLowerCase();

      // 处理特殊的产品标识符格式
      if (shortPath.startsWith('d-q-jdw-')) {
        shortPath = shortPath.replace('d-q-jdw-', '');
      }

      // 根据产品类型添加前缀
      if (shortPath.includes('cgsm') || shortPath.includes('cgys')) {
        shortPath = `quartz-mems-imu-${shortPath}`;
      } else if (shortPath.includes('ltys') || shortPath.includes('lttb')) {
        shortPath = `quartz-mems-gyroscope-${shortPath}`;
      } else if (shortPath.includes('cgm')) {
        shortPath = `mems-imu-${shortPath}`;
      } else if (shortPath.includes('dg')) {
        shortPath = `mems-gyroscope-${shortPath}`;
      } else if (shortPath.includes('xz') || shortPath.includes('zx')) {
        shortPath = `linear-actuator-${shortPath}`;
      }

      return `${baseUrl}/products/${shortPath}`;
    }

    return url;
  }

  // 从产品URL提取产品信息（现在使用静态数据）
  function fetchProductInfo(url) {
    if (productCache.has(url)) {
      return productCache.get(url);
    }

    try {
      // 从URL中提取产品ID或路径
      const urlParts = url.split('/');
      const productId = urlParts[urlParts.length - 1];
      
      // 尝试多种方式匹配产品
      let product = null;
      
      // 1. 直接通过产品ID匹配
      product = productData[productId.toLowerCase()];
      
      // 2. 如果没找到，尝试通过完整URL匹配
      if (!product) {
        product = allProducts.find(p => p.url === url);
      }
      
      // 3. 如果还没找到，尝试模糊匹配
      if (!product) {
        const searchTerm = productId.toLowerCase().replace(/-/g, '');
        product = allProducts.find(p => 
          (p['model'] && p['model'].toLowerCase().replace(/-/g, '').includes(searchTerm)) ||
          (p.url && p.url.toLowerCase().includes(productId.toLowerCase())) ||
          (p.keywords && p.keywords.some(k => k.toLowerCase().includes(searchTerm)))
        );
      }

      if (product) {
        const productInfo = {
          id: product['model'] || product.id || productId,
          title: product['title'] || product.name || productId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
          description: product.description || 'Advanced aerospace and defense technology solution',
          image: product.image || '/images/placeholder-product.jpg',
          category: product.category || 'Technology',
          url: convertToShortUrl(product.url || url),
          site: 'gnc-tech',
          keywords: product.keywords || []
        };
        
        // 确保图片URL正确
        if (productInfo.image && !productInfo.image.startsWith('http') && !productInfo.image.startsWith('/')) {
          productInfo.image = '/images/placeholder-product.jpg';
        }
        
        productCache.set(url, productInfo);
        return productInfo;
      }
    } catch (error) {
      console.warn('Failed to fetch product info:', error);
    }

    // 返回基础信息作为备用
    const fallbackData = {
      title: url.split('/').pop()?.replace(/-/g, ' ') || 'Product',
      description: 'Click to view product details',
      image: '/images/placeholder-product.jpg',
      category: 'Technology'
    };

    // 缓存备用数据，避免重复处理
    productCache.set(url, fallbackData);
    return fallbackData;
  }

  // 创建预览卡片
  function createPreviewCard(product) {
    const card = document.createElement('div');
    card.className = 'product-preview-card';

    // 检查是否有有效的图片URL
    const hasValidImage = product.image &&
                         product.image !== '/images/placeholder-product.jpg' &&
                         !product.image.includes('placeholder');

    if (hasValidImage) {
      // 有图片时的布局
      card.innerHTML = `
        <div class="preview-image">
          <!-- 图片将通过JavaScript插入 -->
        </div>
        <div class="preview-content">
          <h4 class="preview-title">${product.title}</h4>
          <p class="preview-description">${product.description}</p>
          <div class="preview-footer">
            <span class="preview-category">${product.category}</span>
            <span class="preview-external">External Product</span>
          </div>
        </div>
      `;

      // 创建图片元素并预加载
      const img = document.createElement('img');
      img.alt = product.title;
      img.loading = 'eager';
      img.style.opacity = '0';
      img.style.transition = 'opacity 0.3s ease';

      // 图片加载完成后显示
      img.onload = () => {
        img.style.opacity = '1';
      };

      // 图片加载失败时移除图片区域
      img.onerror = () => {
        const imageContainer = card.querySelector('.preview-image');
        if (imageContainer) {
          imageContainer.remove();
          card.classList.add('no-image');
        }
      };

      // 设置图片源
      img.src = product.image;

      // 插入图片到预览图片容器
      const imageContainer = card.querySelector('.preview-image');
      imageContainer.appendChild(img);
    } else {
      // 无图片时的布局
      card.classList.add('no-image');
      card.innerHTML = `
        <div class="preview-content">
          <h4 class="preview-title">${product.title}</h4>
          <p class="preview-description">${product.description}</p>
          <div class="preview-footer">
            <span class="preview-category">${product.category}</span>
            <span class="preview-external">External Product</span>
          </div>
        </div>
      `;
    }

    return card;
  }

  // 初始化产品链接预览
  function initProductLinkPreviews() {
    const productLinks = document.querySelectorAll('a[href*="gnc-tech.com"], a[href*="/products/"]');

    productLinks.forEach(link => {
      let previewCard = null;
      let showTimeout = null;
      let hideTimeout = null;

      // 鼠标进入
      link.addEventListener('mouseenter', () => {
        clearTimeout(hideTimeout);

        showTimeout = setTimeout(() => {
          const productInfo = fetchProductInfo(link.href);
          previewCard = createPreviewCard(productInfo);

          // 定位预览卡片
          const rect = link.getBoundingClientRect();
          const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
          const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

          previewCard.style.position = 'absolute';
          previewCard.style.top = `${rect.bottom + scrollTop + 8}px`;
          previewCard.style.left = `${rect.left + scrollLeft}px`;
          previewCard.style.zIndex = '1000';

          // 确保不超出视窗
          document.body.appendChild(previewCard);
          const cardRect = previewCard.getBoundingClientRect();
          if (cardRect.right > window.innerWidth) {
            previewCard.style.left = `${rect.right + scrollLeft - cardRect.width}px`;
          }
          if (cardRect.bottom > window.innerHeight) {
            previewCard.style.top = `${rect.top + scrollTop - cardRect.height - 8}px`;
          }

          // 添加显示动画
          previewCard.style.opacity = '0';
          previewCard.style.transform = 'translateY(-10px)';
          requestAnimationFrame(() => {
            previewCard.style.transition = 'opacity 0.2s ease, transform 0.2s ease';
            previewCard.style.opacity = '1';
            previewCard.style.transform = 'translateY(0)';
          });
        }, 300); // 300ms延迟显示
      });

      // 鼠标离开
      link.addEventListener('mouseleave', () => {
        clearTimeout(showTimeout);

        if (previewCard) {
          hideTimeout = setTimeout(() => {
            if (previewCard && previewCard.parentNode) {
              previewCard.style.opacity = '0';
              previewCard.style.transform = 'translateY(-10px)';
              setTimeout(() => {
                if (previewCard && previewCard.parentNode) {
                  previewCard.parentNode.removeChild(previewCard);
                }
              }, 200);
            }
          }, 100); // 100ms延迟隐藏
        }
      });
    });
  }

  // 页面加载完成后初始化
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initProductLinkPreviews);
  } else {
    initProductLinkPreviews();
  }
</script>

<style is:global>
  .product-preview-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    border: 1px solid #e5e7eb;
    width: 320px;
    overflow: hidden;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  .product-preview-card.no-image {
    width: 280px; /* 稍微缩小无图片卡片的宽度 */
  }
  
  .preview-image {
    width: 100%;
    height: 160px;
    overflow: hidden;
    background: #f3f4f6;
  }
  
  .preview-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease, opacity 0.3s ease;
    background: #f3f4f6; /* 背景色，减少加载时的闪烁 */
  }
  
  .product-preview-card:hover .preview-image img {
    transform: scale(1.05);
  }
  
  .preview-content {
    padding: 16px;
  }
  
  .preview-title {
    font-size: 16px;
    font-weight: 600;
    color: #111827;
    margin: 0 0 8px 0;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .preview-description {
    font-size: 14px;
    color: #6b7280;
    margin: 0 0 12px 0;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .preview-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 8px;
  }

  .preview-category {
    display: inline-block;
    background: #f3f4f6;
    color: #374151;
    font-size: 12px;
    font-weight: 500;
    padding: 4px 8px;
    border-radius: 6px;
  }

  .preview-external {
    display: inline-block;
    background: #dbeafe;
    color: #1e40af;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 11px;
    font-weight: 500;
  }
  
  /* 响应式设计 */
  @media (max-width: 768px) {
    .product-preview-card {
      width: 280px;
    }
    
    .preview-image {
      height: 140px;
    }
  }
</style>