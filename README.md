# Aerospace & Defense News Platform

一个全自动化、SEO优化的航空航天与国防行业新闻平台。基于**Astro 5**构建，具备自动化内容规划、软广告集成和100%静态HTML生成功能。

## 🚀 核心特性

### 核心功能
- **100% 静态生成**: 零运行时，最高性能和安全性
- **自动化内容流水线**: 从构思到部署的最小人工干预
- **SEO优先方案**: 内置SEO优化和自定义检查
- **软广告集成**: 非侵入式产品推荐和分析
- **多语言就绪**: 框架支持英文/中文扩展
- **heroImage智能验证**: 自动检测和修复损坏的图片链接

### 技术亮点
- **Astro 5** 与内容集合和MDX支持
- **Tailwind CSS** 自定义设计系统
- **Fuse.js** 驱动的客户端搜索
- **Alpine.js** 交互组件
- **Vercel** 部署与边缘缓存
- **Plausible Analytics** 隐私友好的跟踪
- **完整API服务** 支持外部工作流集成

### 自动化特性
- **每日内容规划**: 使用OpenAI GPT-4的AI驱动主题生成
- **SEO检查**: 锚点密度、alt文本和元标签自动检查
- **GitHub Actions CI/CD**: 自动化测试、构建和部署
- **内容验证**: 所有文章的严格模式验证
- **图片链接验证**: 自动检测和替换死链图片
- **部署前检查**: 确保内容质量和图片完整性

## 🏗️ 系统架构

```
┌─────────────┐   计划/任务   ┌──────────────┐   PR   ┌─────────────┐  构建  ┌────────┐
│ 计划生成器   │──────────────▶│ GitHub Repo  │───────▶│ GitHub CI   │────────▶│ Vercel │
│ (定时任务)  │              │ (内容存储)    │        │ (Astro构建)  │         │  CDN   │
└─────────────┘              │   ▲   ▲      │        └─────────────┘         └────────┘
      ▲                      │   │   │  MD  │                ▲
      │ API调用             │   │   │      │                │
┌─────────────┐  REST API  ┌──┴───┴───┴───┐ │    图片        │
│    n8n      │───────────▶│ API服务器     │◀┘               │
│ (工作流编排) │            │ (内容生成)    │               │
└─────────────┘            └──────┬────────┘              搜索索引
      ▲                           │                         │
      │ 触发器/webhook            │ 图片生成               │
      │                          ▼                         │
   外部数据源              ┌─────────────┐                  │
   (Google Trends,         │ R2 Storage  │                  │
    News API等)            │ (图片存储)   │                  │
                          └─────────────┘                  │
                                                           ▼
                          ┌─────────────┐               静态资源
                          │ Hero Image  │               
                          │ 验证器      │               
                          └─────────────┘               
```

## 🔄 自动化流程

项目支持两种主要的自动化工作流：

### 流程1: 定时自动化流程
```
定时触发 → 内容计划生成 → 任务创建 → 内容抓取 → AI改写 → 图片生成 → heroImage验证 → 部署
```

1. **每日定时任务**: GitHub Actions定时运行
2. **内容计划生成**: AI分析趋势生成内容主题
3. **自动内容生成**: 抓取源内容并AI改写
4. **图片处理**: 自动生成heroImage或使用fallback
5. **质量检查**: heroImage验证、SEO检查
6. **自动部署**: 通过验证后自动发布

### 流程2: n8n工作流编排
```
外部触发 → n8n工作流 → API调用 → 内容处理 → 验证检查 → 手动/自动部署
```

1. **灵活触发**: Webhook、定时器、外部事件
2. **工作流编排**: n8n可视化流程设计
3. **API服务调用**: 调用内容生成和验证API
4. **条件分支**: 基于内容质量的智能决策
5. **多人协作**: 不同用户可编排不同时段的流程

## 📦 项目结构

```
├── .github/workflows/     # CI/CD自动化
├── apps/
│   ├── api/              # API服务 (Express + TypeScript)
│   │   ├── src/
│   │   │   ├── routes/   # API路由定义
│   │   │   ├── core/     # 核心工具和服务
│   │   │   │   ├── tools/        # 自动化工具
│   │   │   │   │   ├── hero-image-validator.ts  # 图片验证器
│   │   │   │   │   ├── deployment-tool.ts       # 部署工具
│   │   │   │   │   └── safe-image-tool.ts       # 安全图片生成
│   │   │   │   └── services/     # 业务服务
│   │   │   └── types/    # TypeScript类型定义
│   │   └── dist/         # 编译输出
│   └── web/              # 前端应用 (Astro)
│       ├── src/
│       │   ├── components/       # 可复用Astro组件
│       │   ├── content/          # Markdown内容集合
│       │   │   └── news/         # 按类别分类的新闻文章
│       │   ├── layouts/          # 页面布局
│       │   ├── pages/            # 路由页面
│       │   └── seo-lint.js      # SEO验证工具
│       └── public/       # 静态资源
├── docs/                 # 项目文档
├── scripts/              # 测试和维护脚本
├── tasks/                # 生成的内容计划 (JSON格式)
└── data/                 # 数据和配置文件
```

## 🚀 快速开始

### 前置条件
- Node.js 20+
- pnpm (推荐)
- Git
- Docker (可选，用于本地开发)

### 安装

1. **克隆仓库**
   ```bash
   git clone <repository-url>
   cd news-site
   ```

2. **安装依赖**
   ```bash
   pnpm install
   ```

3. **环境配置**
   ```bash
   cp .env.example .env.local
   # 添加你的API密钥
   ```

4. **启动API服务**
   ```bash
   pnpm dev:api
   ```

5. **启动前端服务**
   ```bash
   pnpm dev:web
   ```

6. **测试自动化流程**
   ```bash
   node scripts/test-automation.js
   ```

## 🔧 配置

### 环境变量

在`.env.local`文件中配置以下变量：

```env
# 必需 - 内容生成
OPENAI_API_KEY=your_openai_api_key
SERP_API_KEY=your_serp_api_key
TAVILY_API_KEY=your_tavily_api_key
FIRECRAWL_API_KEY=your_firecrawl_api_key
NEWS_API_KEY=your_news_api_key

# 必需 - 图片生成
FAL_KEY=your_fal_api_key
UNSPLASH_ACCESS_KEY=your_unsplash_key

# 必需 - 部署
VERCEL_TOKEN=your_vercel_token
VERCEL_ORG_ID=your_org_id
VERCEL_PROJECT_ID=your_project_id

# 可选 - 存储
CLOUDFLARE_R2_ENDPOINT=your_r2_endpoint
CLOUDFLARE_R2_ACCESS_KEY=your_access_key
CLOUDFLARE_R2_SECRET_KEY=your_secret_key
CLOUDFLARE_R2_BUCKET=your_bucket_name

# 可选 - 外部集成
N8N_WEBHOOK_URL=your_n8n_webhook_url
SLACK_WEBHOOK_URL=your_slack_webhook_url
```

### API服务配置

API服务运行在`http://localhost:3002`，提供以下主要端点：

#### 内容管理API
- `POST /api/v1/plans` - 生成内容计划
- `POST /api/v1/content/generate` - 生成单篇文章
- `POST /api/v1/content/batch` - 批量生成文章
- `GET /api/v1/content` - 获取内容列表

#### 自动化API
- `POST /api/automation/validate-hero-images` - 验证和修复heroImage
- `POST /api/automation/pre-deployment-check` - 部署前检查
- `POST /api/automation/run-full-cycle` - 运行完整自动化流程
- `GET /api/automation/status` - 获取系统状态

#### 图片处理API
- `POST /api/v1/image/generate` - 生成图片
- `POST /api/v1/image/analyze` - 分析图片内容
- `GET /api/v1/image/validate` - 验证图片URL

### 内容类别

平台支持六个内容类别：

- **Industry**: 最新发展和公司新闻
- **Research**: 研发洞察和技术突破
- **Events**: 会议、贸易展览和行业活动
- **Frontier**: 新兴技术和未来创新
- **Insight**: 专家分析和观点文章
- **Misc**: 其他相关新闻和更新

## 🤖 n8n工作流集成

### n8n安装和配置

1. **安装n8n**
   ```bash
   # 全局安装
   npm install n8n -g
   
   # 或使用Docker
   docker run -it --rm \
     --name n8n \
     -p 5678:5678 \
     n8nio/n8n
   ```

2. **启动n8n**
   ```bash
   n8n start
   # 访问: http://localhost:5678
   ```

### 基础工作流配置

#### 1. Webhook触发器配置
```json
{
  "httpMethod": "POST",
  "path": "news-automation",
  "responseMode": "onReceived",
  "options": {}
}
```

#### 2. API调用节点配置
```json
{
  "method": "POST",
  "url": "http://localhost:3002/api/v1/content/generate",
  "headers": {
    "Content-Type": "application/json"
  },
  "body": {
    "keyword": "{{ $json.keyword }}",
    "category": "{{ $json.category }}",
    "relatedProducts": "{{ $json.relatedProducts }}",
    "generateImage": true,
    "dryRun": false
  }
}
```

#### 3. 条件判断节点
```json
{
  "conditions": {
    "string": [
      {
        "value1": "{{ $json.status }}",
        "operation": "equal",
        "value2": "success"
      }
    ]
  }
}
```

### 工作流模板

#### 模板1: 基础内容生成流程
```
Webhook → HTTP Request (生成内容) → IF (检查结果) → HTTP Request (heroImage验证) → 通知
```

#### 模板2: 定时批量处理流程
```
Schedule → HTTP Request (获取待处理任务) → Split in Batches → HTTP Request (生成内容) → 合并结果 → 部署
```

#### 模板3: 外部触发响应流程
```
外部Webhook → 数据转换 → HTTP Request (内容生成) → 条件判断 → 发送通知/自动部署
```

### 多用户协作配置

#### 1. 用户角色设置
- **管理员**: 完整工作流编辑权限
- **编辑者**: 内容生成工作流权限
- **审核者**: 内容审核和发布权限
- **观察者**: 只读访问权限

#### 2. 时段分工
```json
{
  "工作流": {
    "早班(6:00-14:00)": {
      "负责人": "Editor-A",
      "任务": ["新闻抓取", "内容生成", "初步审核"]
    },
    "晚班(14:00-22:00)": {
      "负责人": "Editor-B", 
      "任务": ["内容完善", "SEO优化", "发布审核"]
    },
    "夜班(22:00-6:00)": {
      "负责人": "System",
      "任务": ["自动化处理", "数据清理", "系统维护"]
    }
  }
}
```

#### 3. 工作流权限配置
```javascript
// n8n工作流权限设置示例
const workflowPermissions = {
  "content-generation": {
    "allowed_users": ["editor-a", "editor-b"],
    "time_restrictions": "06:00-22:00",
    "require_approval": false
  },
  "content-publishing": {
    "allowed_users": ["admin", "senior-editor"],
    "time_restrictions": "08:00-20:00", 
    "require_approval": true
  },
  "system-maintenance": {
    "allowed_users": ["admin"],
    "time_restrictions": "22:00-06:00",
    "require_approval": false
  }
}
```

## 📝 内容管理

### 创建文章

1. **手动创建**
   ```bash
   # 创建新文章
   touch apps/web/src/content/news/industry/2024-12-15-your-article.md
   ```

2. **文章模板**
   ```yaml
   ---
   title: "Your Article Title"
   date: 2024-12-15
   category: industry
   tags: ["tag1", "tag2", "tag3"]
   related_products: ["product-id-1", "product-id-2"]
   description: "Brief description for SEO and social sharing"
   heroImage: "/hero-bg.jpg"  # 自动验证和修复
   author: "Author Name"
   featured: false
   draft: false
   ---

   Your article content in Markdown...

   <!--AD_HOOK-->

   More content...
   ```

3. **自动化规划**
   ```bash
   # 生成每日内容计划
   curl -X POST http://localhost:3002/api/v1/plans \
     -H "Content-Type: application/json" \
     -d '{"date":"2024-12-15","maxTasks":5}'
   
   # 检查生成的任务
   ls tasks/
   cat tasks/2024-12-15.json
   ```

### SEO优化

运行SEO检查：

```bash
# 检查所有内容
pnpm seo:lint

# 自动修复常见问题
pnpm seo:fix

# API方式检查
curl -X POST http://localhost:3002/api/automation/pre-deployment-check \
  -H "Content-Type: application/json" \
  -d '{"validateContent":true,"validateHeroImages":true}'
```

### heroImage验证和修复

```bash
# 验证所有heroImage链接
curl -X POST http://localhost:3002/api/automation/validate-hero-images \
  -H "Content-Type: application/json" \
  -d '{"validateUrls":true,"fixIssues":false,"dryRun":true}'

# 修复损坏的heroImage链接
curl -X POST http://localhost:3002/api/automation/validate-hero-images \
  -H "Content-Type: application/json" \
  -d '{"validateUrls":true,"fixIssues":true,"dryRun":false}'
```

### 软广告集成

在Markdown中添加`<!--AD_HOOK-->`，系统将自动：

- 基于`related_products`前置数据插入适当的产品卡片
- 生成JSON-LD结构化数据
- 跟踪点击率分析事件

## 🔍 搜索功能

平台包含强大的客户端搜索：

- **Fuse.js**集成用于模糊匹配
- **实时结果**随输入显示
- **结果高亮**匹配内容
- **类别过滤**和快速搜索标签
- **搜索分析**通过Plausible

## 🧪 测试和验证

### 自动化流程测试

```bash
# 运行完整测试套件
node scripts/test-automation.js

# 测试特定功能
node scripts/test-hero-image-validation.js

# API健康检查
curl http://localhost:3002/health

# 测试内容生成
curl -X POST http://localhost:3002/api/automation/test-content \
  -H "Content-Type: application/json" \
  -d '{"keyword":"quantum computing","relatedProducts":["tech-1"]}'
```

### 测试覆盖范围

- ✅ 链接发现功能
- ✅ 内容生成功能  
- ✅ 完整自动化流程
- ✅ 真实文章生成
- ✅ heroImage验证和修复
- ✅ 部署前检查流程

## 📊 分析和监控

### 内置分析
- **Plausible Analytics**: 隐私友好、GDPR合规
- **Vercel Analytics**: Core Web Vitals监控
- **自定义事件**: 软广告点击率、搜索查询、分享

### 性能监控
- **Lighthouse CI**: 自动化性能审计
- **Core Web Vitals**: LCP < 1.5s, CLS < 0.05
- **SEO评分**: 目标Lighthouse SEO ≥ 95
- **图片验证**: 自动检测和修复损坏链接

## 🚀 部署

### 自动部署

推送到`main`分支触发：

1. **SEO检查**: 验证所有内容
2. **heroImage验证**: 检查和修复图片链接
3. **类型检查**: 确保代码质量
4. **构建过程**: 生成静态站点
5. **Vercel部署**: 更新生产站点

### 手动部署

```bash
# 生产构建
pnpm build

# 本地预览构建
pnpm preview

# 部署到Vercel
vercel deploy --prod

# 部署前检查
curl -X POST http://localhost:3002/api/automation/pre-deployment-check
```

## 🛠️ 开发

### 可用脚本

```bash
pnpm dev:api       # 启动API服务器
pnpm dev:web       # 启动前端开发服务器
pnpm build         # 构建生产版本
pnpm preview       # 预览生产构建
pnpm seo:lint      # 运行SEO验证
pnpm test:automation # 测试自动化流程
```

### 代码质量

- **TypeScript**: 启用严格类型检查
- **ESLint**: 代码风格强制执行
- **Prettier**: 自动格式化
- **Husky**: 预提交钩子

### 测试

```bash
# 运行所有测试
pnpm test

# 运行特定测试套件
pnpm test:unit
pnpm test:e2e
pnpm test:accessibility
pnpm test:automation
```

## 🔒 安全性

### 静态站点安全
- **无运行时漏洞**: 100%静态生成
- **内容安全策略**: 通过头部实现
- **HTTPS全覆盖**: 所有页面强制SSL
- **输入验证**: 严格模式验证
- **图片安全**: 自动验证和清理

### API安全
- **环境变量**: 敏感数据保护
- **速率限制**: API调用适当节流
- **CORS策略**: 正确配置来源

## 🌍 多语言支持

框架为扩展做好准备：

```yaml
# 在前置数据中
lang: en  # 或 'zh' 中文
```

特定语言内容路由：
- `/en/news/article-slug`
- `/zh/news/article-slug`

## 🤝 贡献

1. Fork 仓库
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 开启Pull Request

### 开发指南

- 遵循现有代码风格和约定
- 为新功能添加测试
- 更新API更改的文档
- 确保内容更改通过SEO检查

## 📋 路线图

### 第一阶段 (当前)
- [x] 核心平台实现
- [x] 自动化内容流水线
- [x] SEO优化系统
- [x] 软广告集成
- [x] heroImage验证系统
- [x] 完整API服务

### 第二阶段 (进行中)
- [x] n8n工作流集成
- [ ] 多语言实现
- [ ] 高级分析仪表板
- [ ] 内容个性化

### 第三阶段 (未来)
- [ ] 用户生成评论
- [ ] 订阅/通讯系统
- [ ] 移动应用配套
- [ ] AI驱动的内容优化

## 📄 许可证

本项目基于MIT许可证 - 查看[LICENSE](LICENSE)文件了解详情。

## 🙋‍♂️ 支持

如有问题、问题或贡献：

- **文档**: 查看`/docs`目录
- **问题**: 开启GitHub问题
- **讨论**: 使用GitHub讨论
- **邮箱**: [<EMAIL>]

---

**为航空航天和国防行业用❤️构建** 