/**
 * 智能产品推荐系统配置
 * 可以通过修改这些参数来调整推荐算法的行为
 */

export const productRecommendationConfig = {
  // 匹配权重配置
  weights: {
    keywordMatch: 10,      // 关键词精确匹配权重
    titleMatch: 5,         // 产品标题匹配权重
    descriptionMatch: 2,   // 产品描述匹配权重
    categoryMatch: 3,      // 产品分类匹配权重
    useCaseMatch: 3,       // 使用场景匹配权重
    tagMatch: 2            // 标签匹配权重
  },

  // 匹配条件配置
  matching: {
    minWordLength: {
      title: 3,            // 标题匹配的最小单词长度
      description: 4       // 描述匹配的最小单词长度
    },
    caseSensitive: false,  // 是否区分大小写
    stemming: false        // 是否启用词干提取（暂未实现）
  },

  // 推荐结果配置
  results: {
    maxRecommendations: 3, // 最大推荐产品数量（减少推荐数量）
    minScore: 2.0,         // 提高最小匹配分数阈值，确保质量
    diversityFactor: 0.5   // 多样性因子（0-1，越高越多样化）
  },

  // 软链接插入配置
  linkInsertion: {
    maxLinks: 3,           // 最大插入链接数量（减少到1-3个）
    avoidDuplicates: true, // 避免重复链接
    preserveFormatting: true, // 保持原有格式
    linkPattern: '[{text}]({url})', // 链接格式模板
    minLinkDistance: 120,  // 增加链接之间的最小字符距离
    prioritizeCompoundTerms: true, // 优先匹配复合词
    excludePatterns: [     // 排除插入链接的模式
      /```[\s\S]*?```/g,   // 代码块
      /`[^`]+`/g,          // 行内代码
      /\[.*?\]\(.*?\)/g,   // 已有链接
      /#{1,6}\s+.*$/gm     // 标题行
    ]
  },

  // 分类映射配置
  categoryMapping: {
    'actuator': 'Propulsion',
    'battery': 'Propulsion', 
    'thermal': 'Propulsion',
    'radar': 'Detection',
    'sensor': 'Detection',
    'detector': 'Detection',
    'imaging': 'Detection',
    'camera': 'Detection',
    'cmos': 'Detection',
    'infrared': 'Detection',
    'quadrant': 'Detection',
    'gyroscope': 'Navigation',
    'accelerometer': 'Navigation',
    'navigation': 'Navigation',
    'gps': 'Navigation',
    'imu': 'Navigation',
    'fiber optic': 'Navigation',
    'communication': 'Communication',
    'antenna': 'Communication',
    'satellite': 'Communication'
  },

  // 缓存配置
  cache: {
    enabled: true,         // 是否启用缓存
    ttl: 3600000,         // 缓存生存时间（毫秒）
    maxSize: 1000         // 最大缓存条目数
  },

  // 日志配置
  logging: {
    enabled: true,         // 是否启用日志
    level: 'info',        // 日志级别: debug, info, warn, error
    logRecommendations: true, // 是否记录推荐结果
    logPerformance: true   // 是否记录性能指标
  },

  // 数据源配置
  dataSources: {
    productsDirectory: 'data/products', // 产品数据目录
    filePattern: '*.json',              // 文件匹配模式
    encoding: 'utf-8',                  // 文件编码
    watchForChanges: false              // 是否监听文件变化（开发模式）
  },

  // 性能配置
  performance: {
    maxProcessingTime: 5000, // 最大处理时间（毫秒）
    batchSize: 100,          // 批处理大小
    enableProfiling: false   // 是否启用性能分析
  }
};

/**
 * 获取配置值
 * @param {string} path - 配置路径，如 'weights.keywordMatch'
 * @param {any} defaultValue - 默认值
 * @returns {any} 配置值
 */
export function getConfig(path, defaultValue = null) {
  const keys = path.split('.');
  let value = productRecommendationConfig;
  
  for (const key of keys) {
    if (value && typeof value === 'object' && key in value) {
      value = value[key];
    } else {
      return defaultValue;
    }
  }
  
  return value;
}

/**
 * 设置配置值
 * @param {string} path - 配置路径
 * @param {any} value - 新值
 */
export function setConfig(path, value) {
  const keys = path.split('.');
  const lastKey = keys.pop();
  let target = productRecommendationConfig;
  
  for (const key of keys) {
    if (!target[key] || typeof target[key] !== 'object') {
      target[key] = {};
    }
    target = target[key];
  }
  
  target[lastKey] = value;
}

/**
 * 重置配置为默认值
 */
export function resetConfig() {
  // 这里可以重新加载默认配置
  console.log('Configuration reset to defaults');
}

/**
 * 验证配置
 * @returns {boolean} 配置是否有效
 */
export function validateConfig() {
  const errors = [];
  
  // 验证权重配置
  const weights = productRecommendationConfig.weights;
  for (const [key, value] of Object.entries(weights)) {
    if (typeof value !== 'number' || value < 0) {
      errors.push(`Invalid weight value for ${key}: ${value}`);
    }
  }
  
  // 验证结果配置
  const results = productRecommendationConfig.results;
  if (results.maxRecommendations < 1) {
    errors.push('maxRecommendations must be at least 1');
  }
  
  if (results.diversityFactor < 0 || results.diversityFactor > 1) {
    errors.push('diversityFactor must be between 0 and 1');
  }
  
  // 验证链接插入配置
  const linkInsertion = productRecommendationConfig.linkInsertion;
  if (linkInsertion.maxLinks < 0) {
    errors.push('maxLinks must be non-negative');
  }
  
  if (errors.length > 0) {
    console.error('Configuration validation errors:', errors);
    return false;
  }
  
  return true;
}

// 导出默认配置
export default productRecommendationConfig; 