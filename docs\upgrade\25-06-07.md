下面是一份可直接交给 **AI Coder** 的升级任务书，目标是 **彻底淘汰 Express，统一改用 Fastify + TypeScript**，让代码结构与端点命名一目了然，同时保持 n8n 只做“流程编排”。你只需把整份文档贴进待办系统，Coder 按步骤 PR 即可。

---

## 1 总体目标

| 维度         | 目标                                               |
| ---------- | ------------------------------------------------ |
| **框架统一**   | 服务端全部使用 **Fastify (v4)**，不再混用 Express            |
| **语言统一**   | **TypeScript**（ESM），严格启用 `noImplicitAny`         |
| **单仓库**    | 仍保持 **mono-repo**，多服务多包由 pnpm workspace 管理       |
| **端点规范**   | REST 命名清晰、版本前缀 `/v1`；未来新版本 `/v2` 并存              |
| **可扩展**    | 新 Tool / 新 Agent 只需在 `agents/` 或 `tools/` 下加文件即可 |
| **n8n 职责** | 仅调度 HTTP + GitHub，不存业务代码                         |

---

## 2 目录结构（最终形态）

```
news-site/
├── apps/
│   ├── agent-service/          # Fastify 主服务 (所有 API v1)
│   │   ├── src/
│   │   │   ├── routes/
│   │   │   │   ├── plan.ts        # /v1/plan/*
│   │   │   │   ├── tasks.ts       # /v1/tasks/*
│   │   │   │   └── write.ts       # /v1/write
│   │   │   ├── tools/             # LangChain-Ts Tools
│   │   │   │   ├── serpTool.ts
│   │   │   │   ├── firecrawlTool.ts
│   │   │   │   ├── newsapiTool.ts
│   │   │   │   └── imageTool.ts
│   │   │   ├── agents/            # 业务 Agent
│   │   │   │   ├── keywordAgent.ts
│   │   │   │   ├── gatherAgent.ts
│   │   │   │   └── writerAgent.ts
│   │   │   ├── queues/            # BullMQ 队列
│   │   │   │   ├── writeQueue.ts
│   │   │   │   └── worker.ts
│   │   │   ├── lib/               # 通用 util、types
│   │   │   └── index.ts           # Fastify bootstrap
│   │   └── tsconfig.json
│   └── astro-site/               # Astro 纯静态前端
├── tasks/                         # plan & task JSON 文件
├── n8n/                           # 工作流 JSON 备份
├── scripts/
│   └── dev-all.ts                 # 本地并发启动
├── ecosystem.config.js            # PM2 守护 (生产)
├── package.json
└── pnpm-workspace.yaml
```

---

## 3 端点设计（v1）

| 路由                  | Method | 描述             | 备注                                  |
| ------------------- | ------ | -------------- | ----------------------------------- |
| `/v1/plan/generate` | `POST` | ⏱ 计划生成（覆盖今日文件） | Body 可接 `maxKeywords`, `categories` |
| `/v1/plan/latest`   | `GET`  | 📄 读取今日计划文件    | 供 n8n 读取任务                          |
| `/v1/tasks/append`  | `POST` | ➕ 追加/更新单条任务    | 去重 + 字段补全                           |
| `/v1/write`         | `POST` | 📝 写作任务入队      | Worker 产出 Markdown+slug & GitHub 提交 |
| `/v1/health`        | `GET`  | 心跳             | 用于监控                                |

> **约定**：未来任何新功能先加 `/v2/...`，不破坏旧流。

---

## 4 TypeScript & 项目规范

1. **编译目标**：`ES2022`；`moduleResolution: node16`；启用 `isolatedModules`
2. **路径别名**：在 `tsconfig` 里 `"paths": { "@/*": ["src/*"] }`
3. **Lint**：ESLint + Prettier，根配置继承 `eslint-config-airbnb-typescript/base`
4. **日志**：Fastify `pino` logger；禁止 `console.log` 出现在源码
5. **测试**：Vitest / Jest 至少覆盖 Tools & Agents 逻辑
6. **Env**：统一 `.env` → `dotenv-flow`；绝不在代码硬编码 KEY

---

## 5 升级实施步骤

| #  | 任务                           | 目录 / 文件                             | 说明                                      |
| -- | ---------------------------- | ----------------------------------- | --------------------------------------- |
| 1  | 删除旧 Express 依赖               | `package.json`                      | `npm uninstall express body-parser`     |
| 2  | 安装 Fastify & 插件              | root                                | `pnpm add fastify @fastify/swagger zod` |
| 3  | 搭建 Fastify bootstrap         | `apps/agent-service/src/index.ts`   | 见下方代码片段                                 |
| 4  | 把旧 `/plan`, `/tasks` 路由迁移    | `routes/plan.ts`, `routes/tasks.ts` | 使用 Fastify schema+zod                   |
| 5  | LangChain Tools & Agents 拆文件 | `tools/`, `agents/`                 | 每个 Tool 单文件，方便增删                        |
| 6  | BullMQ 队列                    | `queues/writeQueue.ts`              | `REDIS_URL` 从 env 读取                    |
| 7  | Worker 写作 → GitHub           | `queues/worker.ts`                  | 用 `@octokit/rest`                       |
| 8  | n8n URL 更新                   | n8n UI                              | `http://localhost:3002/v1/...`          |
| 9  | PM2 守护脚本                     | `ecosystem.config.js`               | 三进程：agent-service / n8n / astro-preview |
| 10 | README 更新                    | root                                | 描述启动、测试、部署流程                            |

### Fastify bootstrap 示例

```ts
// apps/agent-service/src/index.ts
import Fastify from 'fastify';
import planRoutes  from './routes/plan';
import taskRoutes  from './routes/tasks';
import writeRoutes from './routes/write';

const app = Fastify({ logger: true });

app.register(planRoutes,  { prefix: '/v1/plan'  });
app.register(taskRoutes,  { prefix: '/v1/tasks' });
app.register(writeRoutes, { prefix: '/v1'       });
app.get('/v1/health', () => ({ ok: true, ts: Date.now() }));

app.ready().then(() => {
  app.swagger();               // expose /documentation
  app.listen({ port: 3002 }, () => console.log('⚡ agent-service @3002'));
});
```

---

## 6 后续功能扩展指引

| 新需求                       | 扩展方式                                                     |
| ------------------------- | -------------------------------------------------------- |
| 加 **Twitter Trends Tool** | `tools/twitterTool.ts` + 在 `keywordAgent.ts` 注入即可        |
| 新模型 (Claude)              | 在 `writerAgent.ts` 内替换 `ChatOpenAI` 为 `ChatAnthropic`    |
| 多语言站                      | 任务对象加 `lang`，writerAgent 选不同 Prompt；Astro `slug.lang.md` |
| 监控/告警                     | 在 PM2 或 Fastify `onError` 钩子推送钉钉/Slack                   |

---

## 7 交付验收 Checklist ✅

* [ ] `pnpm dev` 一条命令本地跑起 **Fastify + Astro + n8n**
* [ ] `curl -X POST :3002/v1/plan/generate` 返回 `{success:true}`，文件写入 `/tasks/YYYY-MM-DD.json`
* [ ] n8n 手动触发 `article.main`，2 min 内 PR 自动打开，任务状态改 `done`
* [ ] `pnpm -F astro-site build` 通过，SEO-lint 全绿
* [ ] `pm2 start ecosystem.config.js` 后重启服务器不丢任务

---

> **请把本任务书给 AI Coder 按顺序 PR**：
> 1️⃣ 目录 scaffold → 2️⃣ Fastify 路由 → 3️⃣ Tools & Agents → 4️⃣ Queue → 5️⃣ n8n URL 调整 → 6️⃣ README + Scripts
> 每一步保留单独 commit，便于 code-review。
