export interface AutomationConfig {
  // API Keys
  serpApiKey?: string;
  tavilyApiKey?: string;
  firecrawlApiKey?: string;
  newsApiKey?: string;
  openaiApiKey?: string;
  unsplashApiKey?: string;
  falApiKey?: string;
  cloudflareWorkerUrl?: string;
  cloudflareApiKey?: string;

  // Link Discovery Settings
  linkDiscovery: {
    maxUrls: number;
    minArticles: number;
    retryAttempts: number;
    timeout: number;
    enableFirecrawl: boolean;
    enableNewsAPI: boolean;
    priority: {
      serpapi: number;
      tavily: number;
      trends: number;
    };
  };

  // Content Generation Settings
  contentGeneration: {
    model: string;
    temperature: number;
    maxTokens: number;
    enableImageGeneration: boolean;
    imageProvider: 'workers'; // 简化为仅支持Worker
  };

  // Simplified Image Generation Settings (Worker-only)
  imageGeneration: {
    enableFallback: boolean;
    fallbackImageUrl: string;
    worker: {
      enabled: boolean;
      timeout: number;
    };
  };

  // Plan Execution Settings
  planExecution: {
    maxConcurrentTasks: number;
    batchDelay: number; // milliseconds
    defaultCategories: string[];
    defaultMaxTasks: number;
  };
}

export function getAutomationConfig(): AutomationConfig {
  return {
    // API Keys from environment
    serpApiKey: process.env.SERP_API_KEY,
    tavilyApiKey: process.env.TAVILY_API_KEY,
    firecrawlApiKey: process.env.FIRECRAWL_API_KEY,
    newsApiKey: process.env.NEWS_API_KEY,
    openaiApiKey: process.env.OPENAI_API_KEY,
    unsplashApiKey: process.env.UNSPLASH_ACCESS_KEY,
    falApiKey: process.env.FAL_API_KEY,
    cloudflareWorkerUrl: process.env.CLOUDFLARE_WORKER_URL,
    cloudflareApiKey: process.env.CLOUDFLARE_API_KEY,

    // Link Discovery Settings
    linkDiscovery: {
      maxUrls: parseInt(process.env.LINK_DISCOVERY_MAX_URLS || '10'),
      minArticles: parseInt(process.env.LINK_DISCOVERY_MIN_ARTICLES || '3'),
      retryAttempts: parseInt(process.env.LINK_DISCOVERY_RETRY_ATTEMPTS || '2'),
      timeout: parseInt(process.env.LINK_DISCOVERY_TIMEOUT || '15000'),
      enableFirecrawl: process.env.ENABLE_FIRECRAWL !== 'false',
      enableNewsAPI: process.env.ENABLE_NEWS_API !== 'false',
      priority: {
        serpapi: parseInt(process.env.SERPAPI_PRIORITY || '1'),
        tavily: parseInt(process.env.TAVILY_PRIORITY || '2'),
        trends: parseInt(process.env.TRENDS_PRIORITY || '3')
      }
    },

    // Content Generation Settings
    contentGeneration: {
      model: process.env.OPENAI_MODEL || 'gpt-4o-mini',
      temperature: parseFloat(process.env.CONTENT_TEMPERATURE || '0.7'),
      maxTokens: parseInt(process.env.CONTENT_MAX_TOKENS || '4000'),
      enableImageGeneration: process.env.ENABLE_IMAGE_GENERATION !== 'false',
      imageProvider: 'workers' // 简化为仅支持Worker
    },

    // Simplified Image Generation Settings (Worker-only)
    imageGeneration: {
      enableFallback: process.env.ENABLE_IMAGE_FALLBACK !== 'false',
      fallbackImageUrl: process.env.FALLBACK_IMAGE_URL || '/hero-bg.jpg', // 使用本地默认背景
      worker: {
        enabled: !!(process.env.CLOUDFLARE_WORKER_URL && process.env.IMAGE_API_KEY),
        timeout: parseInt(process.env.WORKERS_TIMEOUT || '90000') // 90秒超时，符合USAGE_GUIDE.md建议
      }
    },

    // Plan Execution Settings
    planExecution: {
      maxConcurrentTasks: parseInt(process.env.MAX_CONCURRENT_TASKS || '3'),
      batchDelay: parseInt(process.env.BATCH_DELAY || '2000'),
      defaultCategories: process.env.DEFAULT_CATEGORIES?.split(',') || ['industry', 'research', 'frontier'],
      defaultMaxTasks: parseInt(process.env.DEFAULT_MAX_TASKS || '5')
    }
  };
}

export function validateConfig(config: AutomationConfig): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Check for at least one search provider
  if (!config.serpApiKey && !config.tavilyApiKey) {
    errors.push('At least one search provider API key (SERP_API_KEY or TAVILY_API_KEY) is required');
  }

  // Check OpenAI API key for content generation
  if (!config.openaiApiKey) {
    errors.push('OPENAI_API_KEY is required for content generation');
  }

  // Validate numeric settings
  if (config.linkDiscovery.maxUrls < 1) {
    errors.push('Link discovery maxUrls must be at least 1');
  }

  if (config.linkDiscovery.minArticles < 1) {
    errors.push('Link discovery minArticles must be at least 1');
  }

  if (config.contentGeneration.temperature < 0 || config.contentGeneration.temperature > 2) {
    errors.push('Content generation temperature must be between 0 and 2');
  }

  if (config.planExecution.maxConcurrentTasks < 1) {
    errors.push('Plan execution maxConcurrentTasks must be at least 1');
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

// Export singleton instance
export const automationConfig = getAutomationConfig(); 