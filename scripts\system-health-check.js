#!/usr/bin/env node

/**
 * 系统健康检查脚本
 * 检查整个自动化新闻系统的状态
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

// 配置
const CONFIG = {
  apiBaseUrl: 'http://localhost:3002',
  webBaseUrl: 'http://localhost:4321',
  contentDir: join(projectRoot, 'apps/web/src/content/news'),
  tasksDir: join(projectRoot, 'tasks'),
  productsDir: join(projectRoot, 'data/products'),
  distDir: join(projectRoot, 'apps/web/dist')
};

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  magenta: '\x1b[35m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function success(message) { log(`✅ ${message}`, 'green'); }
function error(message) { log(`❌ ${message}`, 'red'); }
function warning(message) { log(`⚠️  ${message}`, 'yellow'); }
function info(message) { log(`ℹ️  ${message}`, 'blue'); }
function highlight(message) { log(`🔍 ${message}`, 'cyan'); }

/**
 * 检查API服务状态
 */
async function checkApiHealth() {
  highlight('检查API服务状态...');
  
  try {
    const response = await fetch(`${CONFIG.apiBaseUrl}/health`);
    if (response.ok) {
      const data = await response.json();
      success(`API服务正常 (运行时间: ${Math.round(data.uptime)}秒)`);
      return true;
    } else {
      error(`API服务响应异常: ${response.status}`);
      return false;
    }
  } catch (err) {
    error(`API服务连接失败: ${err.message}`);
    return false;
  }
}

/**
 * 检查分类目录结构
 */
function checkCategoryStructure() {
  highlight('检查分类目录结构...');
  
  const predefinedCategories = ['industry', 'research', 'events', 'frontier', 'insight', 'misc'];
  const results = {};
  let allGood = true;
  
  for (const category of predefinedCategories) {
    const categoryDir = join(CONFIG.contentDir, category);
    const exists = fs.existsSync(categoryDir);
    
    if (!exists) {
      warning(`分类目录缺失: ${category}`);
      allGood = false;
    }
    
    const files = exists 
      ? fs.readdirSync(categoryDir).filter(f => f.endsWith('.md'))
      : [];
    
    results[category] = {
      exists,
      articleCount: files.length
    };
    
    if (exists) {
      info(`${category}: ${files.length} 篇文章`);
    }
  }
  
  if (allGood) {
    success('所有分类目录结构正常');
  }
  
  return results;
}

/**
 * 检查构建输出
 */
function checkBuildOutput() {
  highlight('检查构建输出...');
  
  if (!fs.existsSync(CONFIG.distDir)) {
    error('构建输出目录不存在，请运行 pnpm build');
    return false;
  }
  
  const predefinedCategories = ['industry', 'research', 'events', 'frontier', 'insight', 'misc'];
  let missingPages = [];
  
  for (const category of predefinedCategories) {
    const categoryPagePath = join(CONFIG.distDir, 'category', category, 'index.html');
    if (!fs.existsSync(categoryPagePath)) {
      missingPages.push(category);
    }
  }
  
  if (missingPages.length > 0) {
    error(`缺失分类页面: ${missingPages.join(', ')}`);
    return false;
  } else {
    success('所有分类页面已生成');
    return true;
  }
}

/**
 * 检查产品数据
 */
function checkProductData() {
  highlight('检查产品数据...');
  
  if (!fs.existsSync(CONFIG.productsDir)) {
    error('产品数据目录不存在');
    return false;
  }
  
  const productFiles = fs.readdirSync(CONFIG.productsDir).filter(f => f.endsWith('.json'));
  
  if (productFiles.length === 0) {
    error('没有找到产品数据文件');
    return false;
  }
  
  let totalProducts = 0;
  
  for (const file of productFiles) {
    try {
      const filePath = join(CONFIG.productsDir, file);
      const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
      
      if (data.products && Array.isArray(data.products)) {
        totalProducts += data.products.length;
        info(`${file}: ${data.products.length} 个产品`);
      }
    } catch (err) {
      error(`产品文件解析失败 ${file}: ${err.message}`);
      return false;
    }
  }
  
  success(`产品数据正常 (总计 ${totalProducts} 个产品)`);
  return true;
}

/**
 * 检查任务文件
 */
function checkTaskFiles() {
  highlight('检查任务文件...');
  
  if (!fs.existsSync(CONFIG.tasksDir)) {
    warning('任务目录不存在');
    return false;
  }
  
  const taskFiles = fs.readdirSync(CONFIG.tasksDir).filter(f => f.endsWith('.json'));
  
  if (taskFiles.length === 0) {
    warning('没有找到任务文件');
    return false;
  }
  
  const today = new Date().toISOString().split('T')[0];
  const todayTaskFile = `${today}.json`;
  const hasTodayTasks = taskFiles.includes(todayTaskFile);
  
  info(`任务文件数量: ${taskFiles.length}`);
  
  if (hasTodayTasks) {
    success(`今日任务文件存在: ${todayTaskFile}`);
  } else {
    warning(`今日任务文件不存在: ${todayTaskFile}`);
  }
  
  return true;
}

/**
 * 检查文章链接质量
 */
function checkArticleLinkQuality() {
  highlight('检查文章链接质量...');
  
  const categories = ['industry', 'research', 'events', 'frontier', 'insight', 'misc'];
  let totalArticles = 0;
  let articlesWithIssues = 0;
  
  for (const category of categories) {
    const categoryDir = join(CONFIG.contentDir, category);
    
    if (!fs.existsSync(categoryDir)) continue;
    
    const files = fs.readdirSync(categoryDir).filter(f => f.endsWith('.md'));
    
    for (const file of files) {
      totalArticles++;
      const filePath = join(categoryDir, file);
      const content = fs.readFileSync(filePath, 'utf8');
      
      // 检查嵌套链接
      const nestedLinkPattern = /\[([^\]]*)\]\(([^)]*)\[([^\]]*)\]\(([^)]*)\)/g;
      const hasNestedLinks = nestedLinkPattern.test(content);
      
      // 检查链接密度（适合元器件级产品）
      const linkPattern = /\[([^\]]+)\]\(([^)]+)\)/g;
      const links = content.match(linkPattern) || [];
      const hasHighLinkDensity = links.length > 5; // 提高阈值，适合元器件文章
      
      if (hasNestedLinks || hasHighLinkDensity) {
        articlesWithIssues++;
        warning(`文章有链接问题: ${file} (嵌套链接: ${hasNestedLinks}, 高密度: ${hasHighLinkDensity})`);
      }
    }
  }
  
  if (articlesWithIssues === 0) {
    success(`所有 ${totalArticles} 篇文章的链接质量良好`);
  } else {
    warning(`${articlesWithIssues}/${totalArticles} 篇文章存在链接问题`);
  }
  
  return articlesWithIssues === 0;
}

/**
 * 主函数
 */
async function main() {
  console.log('🔍 系统健康检查');
  console.log('================\n');
  
  const checks = [
    { name: 'API服务', fn: checkApiHealth },
    { name: '分类目录结构', fn: checkCategoryStructure },
    { name: '构建输出', fn: checkBuildOutput },
    { name: '产品数据', fn: checkProductData },
    { name: '任务文件', fn: checkTaskFiles },
    { name: '文章链接质量', fn: checkArticleLinkQuality }
  ];
  
  const results = {};
  
  for (const check of checks) {
    try {
      results[check.name] = await check.fn();
    } catch (err) {
      error(`检查 ${check.name} 时出错: ${err.message}`);
      results[check.name] = false;
    }
    console.log(''); // 空行分隔
  }
  
  // 总结
  console.log('📊 检查结果总结:');
  console.log('================');
  
  const passedChecks = Object.values(results).filter(Boolean).length;
  const totalChecks = Object.keys(results).length;
  
  for (const [checkName, passed] of Object.entries(results)) {
    if (passed) {
      success(checkName);
    } else {
      error(checkName);
    }
  }
  
  console.log('');
  
  if (passedChecks === totalChecks) {
    success(`🎉 所有检查通过 (${passedChecks}/${totalChecks})`);
    console.log('\n✨ 系统运行状态良好！');
  } else {
    warning(`⚠️  部分检查失败 (${passedChecks}/${totalChecks})`);
    console.log('\n📋 建议修复失败的检查项目');
  }
}

// 运行主函数
main().catch(console.error);
