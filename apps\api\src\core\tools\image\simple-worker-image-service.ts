import { logger } from '../../../utils/logger.js';

export interface SimpleImageGenerationRequest {
  prompt: string;
  keyword?: string;
  title?: string;
  category?: string;
  articleId?: string; // 可选，用于R2存储
}

export interface SimpleImageGenerationResult {
  success: boolean;
  url?: string;
  provider?: string;
  error?: string;
  elapsedMs?: number;
  r2Stored?: boolean;
  r2Error?: string;
}

export interface SimpleWorkerImageConfig {
  workerUrl: string;
  apiKey?: string; // 可选，某些Worker可能不需要API密钥
  timeout: number; // 超时时间，默认90秒
  fallbackImageUrl: string; // 回退图片URL，默认 /hero-bg.jpg
  enableLogging: boolean;
}

/**
 * 简化的Worker图片生成服务
 * 
 * 基于USAGE_GUIDE.md文档，直接调用Cloudflare Worker API
 * 特点：
 * - 仅使用Worker作为图片提供商
 * - 支持90秒超时（符合Worker的Replicate优先级策略）
 * - 失败时使用本地默认背景图片 /hero-bg.jpg
 * - 支持R2存储（通过articleId参数）
 * - 简单直接，无复杂的多级回退机制
 */
export class SimpleWorkerImageService {
  private config: SimpleWorkerImageConfig;

  constructor(config: SimpleWorkerImageConfig) {
    this.config = config;
    
    if (this.config.enableLogging) {
      logger.info('Simple Worker Image Service initialized', {
        workerUrl: this.config.workerUrl,
        timeout: this.config.timeout,
        fallbackImage: this.config.fallbackImageUrl
      });
    }
  }

  /**
   * 生成图片
   * 直接调用Worker API，支持90秒超时
   */
  async generateImage(request: SimpleImageGenerationRequest): Promise<SimpleImageGenerationResult> {
    const startTime = Date.now();
    
    if (this.config.enableLogging) {
      logger.info('Starting Worker image generation', {
        prompt: request.prompt?.substring(0, 100),
        keyword: request.keyword,
        title: request.title,
        category: request.category,
        hasArticleId: !!request.articleId
      });
    }

    try {
      // 构建Worker API请求
      const workerRequest = {
        prompt: request.prompt,
        ...(request.articleId && { articleId: request.articleId }) // 仅在有articleId时添加，触发R2存储
      };

      // 调用Worker API
      const response = await this.callWorkerAPI(workerRequest);
      const elapsedMs = Date.now() - startTime;

      if (response.success && response.url) {
        if (this.config.enableLogging) {
          logger.info('Worker image generation successful', {
            url: response.url,
            provider: response.provider,
            elapsedMs,
            r2Stored: response.r2Stored
          });
        }

        return {
          success: true,
          url: response.url,
          provider: response.provider || 'worker',
          elapsedMs,
          r2Stored: response.r2Stored,
          r2Error: response.r2Error
        };
      } else {
        // Worker生成失败，使用默认背景图片
        if (this.config.enableLogging) {
          logger.warn('Worker image generation failed, using fallback image', {
            error: response.error,
            elapsedMs,
            fallbackUrl: this.config.fallbackImageUrl
          });
        }

        return {
          success: true, // 仍然返回成功，因为有回退图片
          url: this.config.fallbackImageUrl,
          provider: 'fallback',
          elapsedMs,
          error: `Worker failed: ${response.error}`
        };
      }

    } catch (error) {
      const elapsedMs = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      if (this.config.enableLogging) {
        logger.error('Worker image generation error, using fallback image', {
          error: errorMessage,
          elapsedMs,
          fallbackUrl: this.config.fallbackImageUrl
        });
      }

      // 即使出错也返回成功，使用默认背景图片
      return {
        success: true,
        url: this.config.fallbackImageUrl,
        provider: 'fallback',
        elapsedMs,
        error: `Worker error: ${errorMessage}`
      };
    }
  }

  /**
   * 调用Worker API
   * 基于USAGE_GUIDE.md的API规范
   */
  private async callWorkerAPI(request: { prompt: string; articleId?: string }): Promise<{
    success: boolean;
    url?: string;
    provider?: string;
    error?: string;
    elapsedMs?: number;
    r2Stored?: boolean;
    r2Error?: string;
  }> {
    if (!this.config.workerUrl) {
      throw new Error('Worker URL not configured');
    }

    if (!this.config.apiKey) {
      throw new Error('IMAGE_API_KEY not configured - required for Worker authentication');
    }

    // 构建请求头 - 根据USAGE_GUIDE.md，需要Authorization认证
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.config.apiKey}` // 必需的认证头
    };

    // 创建AbortController用于超时控制
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

    try {
      // 根据USAGE_GUIDE.md，调用 /images/generate 端点
      const response = await fetch(`${this.config.workerUrl}/images/generate`, {
        method: 'POST',
        headers,
        body: JSON.stringify(request),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        const errorMessage = `Worker API error: ${response.status} ${errorText}`;

        if (this.config.enableLogging) {
          logger.error('Worker API request failed', {
            status: response.status,
            statusText: response.statusText,
            errorText,
            workerUrl: this.config.workerUrl,
            hasApiKey: !!this.config.apiKey
          });
        }

        throw new Error(errorMessage);
      }

      const result = await response.json();

      // 根据USAGE_GUIDE.md的响应格式解析结果
      return {
        success: result.success !== false, // 默认为true，除非明确为false
        url: result.url,
        provider: result.provider,
        error: result.error,
        elapsedMs: result.elapsedMs,
        r2Stored: result.r2Stored,
        r2Error: result.r2Error
      };

    } catch (error) {
      clearTimeout(timeoutId);

      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error(`Worker timeout after ${this.config.timeout}ms`);
      }

      throw error;
    }
  }

  /**
   * 构建图片提示词
   * 基于关键词、标题和类别生成详细的英文提示词
   */
  buildImagePrompt(title?: string, keyword?: string, category?: string): string {
    let prompt = '';

    // 基于类别添加风格前缀
    const categoryStyles: Record<string, string> = {
      'industry': 'professional industrial scene, modern technology, business environment',
      'research': 'scientific research laboratory, high-tech equipment, innovation',
      'events': 'professional conference, business meeting, networking event',
      'frontier': 'cutting-edge technology, futuristic design, advanced innovation',
      'insight': 'data visualization, analytics dashboard, business intelligence',
      'misc': 'modern professional setting, clean design'
    };

    if (category && categoryStyles[category]) {
      prompt += categoryStyles[category];
    }

    // 添加关键词
    if (keyword) {
      prompt += prompt ? `, ${keyword}` : keyword;
    }

    // 添加标题相关内容（提取关键概念）
    if (title) {
      // 简单提取标题中的关键词（去除常见停用词）
      const titleKeywords = title
        .toLowerCase()
        .replace(/[^\w\s]/g, ' ')
        .split(/\s+/)
        .filter(word => word.length > 3 && !['the', 'and', 'for', 'with', 'this', 'that', 'from', 'they', 'have', 'will', 'been', 'their'].includes(word))
        .slice(0, 3) // 最多取3个关键词
        .join(' ');
      
      if (titleKeywords) {
        prompt += prompt ? `, ${titleKeywords}` : titleKeywords;
      }
    }

    // 添加通用的质量和风格后缀
    prompt += ', high quality, professional photography, clean composition, modern design';

    return prompt || 'modern professional technology scene, high quality';
  }
}

/**
 * 创建简化的Worker图片生成服务实例
 */
export function createSimpleWorkerImageService(): SimpleWorkerImageService {
  const config: SimpleWorkerImageConfig = {
    workerUrl: process.env.CLOUDFLARE_WORKER_URL || '',
    apiKey: process.env.IMAGE_API_KEY || undefined, // 使用新的IMAGE_API_KEY环境变量
    timeout: 90000, // 90秒超时，符合USAGE_GUIDE.md的建议
    fallbackImageUrl: '/hero-bg.jpg', // 使用本地默认背景图片
    enableLogging: process.env.NODE_ENV !== 'production'
  };

  return new SimpleWorkerImageService(config);
}
