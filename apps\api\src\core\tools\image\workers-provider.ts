import { BaseImageProvider, ImageGenerationRequest, ImageGenerationResult, ImageProviderConfig } from './base-image-provider.js';

export interface WorkersConfig extends ImageProviderConfig {
  workerUrl: string;
  apiKey: string; // 必需，用于Worker认证
}

export class WorkersProvider extends BaseImageProvider {
  private workerUrl: string;
  private apiKey: string;

  constructor(config: WorkersConfig) {
    super('workers', config);
    this.workerUrl = config.workerUrl;
    this.apiKey = config.apiKey;
  }

  async generate(request: ImageGenerationRequest): Promise<ImageGenerationResult> {
    if (!this.workerUrl) {
      return {
        success: false,
        provider: this.name,
        error: 'Cloudflare Workers URL not configured'
      };
    }

    if (!this.apiKey) {
      return {
        success: false,
        provider: this.name,
        error: 'Cloudflare Workers API key not configured'
      };
    }

    try {
      return await this.withTimeout(
        this.withRetries(() => this.generateFromWorker(request)),
        this.config.timeout || 30000
      );
    } catch (error) {
      return {
        success: false,
        provider: this.name,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  async healthCheck(): Promise<boolean> {
    if (!this.workerUrl || !this.apiKey) {
      return false;
    }

    try {
      // 简单的OPTIONS请求检查Worker是否可达
      const response = await fetch(this.workerUrl, {
        method: 'OPTIONS',
        headers: this.buildHeaders()
      });
      return response.ok || response.status === 405; // 405 Method Not Allowed也算健康
    } catch (error) {
      return false;
    }
  }

  private async generateFromWorker(request: ImageGenerationRequest): Promise<ImageGenerationResult> {
    if (!this.apiKey) {
      throw new Error('Worker API token is required');
    }

    const prompt = this.buildPrompt(request);
    const model = this.getModel(request.style);
    
    const requestBody = {
      prompt,
      model: model || "black-forest-labs/flux-schnell",
      // webhook支持 - 这是触发实际图片生成的关键
      // webhook URL指向Worker自己的端点，用于接收Replicate的回调
      webhook: `${this.workerUrl}/webhook`,
      webhookEvents: ["completed", "output"]
    };

    // 调用 Worker 根路径
    const response = await fetch(this.workerUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      let errorMessage = `HTTP ${response.status}`;
      try {
        const errorData = await response.json();
        errorMessage = errorData.error || errorMessage;
      } catch {
        errorMessage = await response.text() || errorMessage;
      }
      throw new Error(errorMessage);
    }

    const result = await response.json();

    // Worker 返回格式: { id, imageUrl, status }
    if (result.error) {
      return {
        success: false,
        provider: this.name,
        error: result.error
      };
    }

    // 检查必要字段
    if (!result.id || !result.imageUrl) {
      return {
        success: false,
        provider: this.name,
        error: 'Invalid response from worker: missing id or imageUrl'
      };
    }

    // Worker 返回 imageUrl 就直接使用，不需要轮询
    // 图片会在后台生成，URL 地址是有效的
    return {
      success: true,
      url: result.imageUrl,
      provider: this.name,
      metadata: {
        prompt,
        model,
        predictionId: result.id,
        status: result.status,
        style: 'ai-generated'
      }
    };
  }

  private buildHeaders(): Record<string, string> {
    const headers: Record<string, string> = {};

    if (this.apiKey) {
      headers['Authorization'] = `Bearer ${this.apiKey}`;
    }

    return headers;
  }

  private buildPrompt(request: ImageGenerationRequest): string {
    if (request.prompt) {
      return request.prompt;
    }

    const parts: string[] = [];

    if (request.keyword) {
      parts.push(request.keyword);
    }

    if (request.title) {
      parts.push(request.title);
    }

    if (request.category) {
      const categoryPrompt = this.getCategoryPrompt(request.category);
      if (categoryPrompt) {
        parts.push(categoryPrompt);
      }
    }

    return parts.join(' ') || 'technology';
  }

  private getCategoryPrompt(category: string): string {
    const categoryMap: Record<string, string> = {
      industry: 'industrial technology manufacturing',
      research: 'scientific research laboratory',
      events: 'conference technology event',
      frontier: 'cutting edge futuristic technology',
      insight: 'data analytics business intelligence',
      misc: 'technology innovation'
    };

    return categoryMap[category] || categoryMap.misc;
  }

  private getModel(style?: string): string {
    // 根据样式选择模型
    const modelMap: Record<string, string> = {
      'fast': 'black-forest-labs/flux-schnell',
      'quality': 'black-forest-labs/flux-dev', 
      'stable': 'stability-ai/stable-diffusion-xl-base-1.0'
    };

    return modelMap[style || 'fast'] || 'black-forest-labs/flux-schnell';
  }

  private async pollForCompletion(predictionId: string, prompt: string, model: string): Promise<ImageGenerationResult> {
    const maxAttempts = 60; // 最多轮询60次 (约5分钟)
    const pollInterval = 5000; // 每5秒查询一次

    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      try {
        // 可以添加状态查询端点，但这里简化处理
        // const response = await fetch(`${this.workerUrl}/status/${predictionId}`, {
        //   headers: { 'Authorization': `Bearer ${this.apiKey}` }
        // });
        
        // 暂时返回异步处理中的状态
        await new Promise(resolve => setTimeout(resolve, pollInterval));
        
        // 实际应该调用状态查询API，这里简化为失败
        if (attempt === maxAttempts - 1) {
          return {
            success: false,
            provider: this.name,
            error: `Image generation timeout after ${maxAttempts * pollInterval / 1000} seconds`
          };
        }
      } catch (error) {
        return {
          success: false,
          provider: this.name,
          error: `Status polling failed: ${error instanceof Error ? error.message : 'Unknown error'}`
        };
      }
    }

    return {
      success: false,
      provider: this.name,
      error: 'Image generation timeout'
    };
  }
} 