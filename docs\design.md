# Aerospace / Defense News Platform

## 0. Document Overview

This single file contains **two parts**:

* **Part A – Requirement Specification**: What the system **must** do.
* **Part B – Technical Design**: How the system **will** fulfil those requirements.

---

# Part A – Requirement Specification

## 1. Vision & Goals

| ID | Goal                                                                           | Rationale                                                          |
| -- | ------------------------------------------------------------------------------ | ------------------------------------------------------------------ |
| G1 | Deliver a high‑authority news portal for aerospace, space, and defense sectors | Attract industry readership and funnel traffic to product websites |
| G2 | 100 % static HTML output for all publicly crawled pages                        | Maximise speed, security, and SEO                                  |
| G3 | Fully automated end‑to‑end pipeline (ideation → content → build → deploy)      | Minimise editorial overhead and enable high publishing cadence     |
| G4 | Embed *soft* product recommendations with SEO‑beneficial keywords              | Monetise traffic and improve internal linking without harming UX   |
| G5 | Architecture ready for multilingual & multi‑author expansion                   | Future‑proof                                                       |

## 2. Scope

Included:

* News, research digests, event coverage, technical frontier, opinion pieces
* Content planning, scraping, summarising, image generation, markdown assembly
* Static‑site generation, CI/CD, image optimisation, SEO artefacts (Sitemap, RSS, JSON‑LD)
* Soft‑ad injection & keyword anchoring
* Remote (R2) + optional local image storage

Excluded:

* User‑generated comments (can be added later)
* Paywall / subscription logic

## 3. Functional Requirements

| Ref | Description                                                                                                                               |
| --- | ----------------------------------------------------------------------------------------------------------------------------------------- |
| F1  | **Category system** with at least six root categories: `industry`, `research`, `events`, `frontier`, `insight`, `misc`                    |
| F2  | Each article stored as Markdown (`.md`) with YAML front‑matter:<br>`title, date, category, tags[], related_products[], lang`              |
| F3  | One front‑matter placeholder `<!--AD_HOOK-->` where the build inserts soft‑ad component                                                   |
| F4  | **Task list** (JSON) auto‑generated daily describing candidate stories (title, category, sources, deadline)                               |
| F5  | n8n workflow consumes tasks, scrapes sources, summarises via OpenAI, generates image(s), uploads image to R2, composes Markdown, opens PR |
| F6  | GitHub PR must pass **SEO‑Lint** checks (anchoring density, alt text, meta) before merge                                                  |
| F7  | Merge or push triggers CI to **incrementally** rebuild & deploy to Vercel; rollback selectable via Vercel UI                              |
| F8  | Search page offers client‑side full‑text search (Fuse.js) across all published articles                                                   |
| F9  | Related product card automatically displays on article pages (desktop = pinned card, mobile = collapsible banner)                         |
| F10 | Analytics events (`pageview`, `productClick`) sent to Plausible; soft‑ad CTR measurable                                                   |

## 4. Non‑Functional Requirements

| Cat                  | Requirement                                                   |
| -------------------- | ------------------------------------------------------------- |
| Performance          | Largest Contentful Paint ≤ 1.5 s on Slow 3G; CLS < 0.05       |
| SEO                  | Lighthouse SEO ≥ 95; valid `Article` & `Product` JSON‑LD      |
| Security             | No server‑side runtime; depend solely on static hosting/CDN   |
| Accessibility        | WCAG 2.1 AA, keyboard navigation, images with alt             |
| Maintainability      | Clean monorepo; automated lint, type‑check, formatting        |
| Internationalisation | Framework must allow en/zh duplication by adding `lang` field |

## 5. User Roles

| Role           | Capability                                             |
| -------------- | ------------------------------------------------------ |
| Editor         | Approve/merge PR, trigger rebuild, manual copy‑editing |
| Automation Bot | Create tasks, run n8n flows, raise PRs                 |
| Developer      | Maintain CI, workflows, infrastructure                 |

---

# Part B – Technical Design

## 6. Solution Architecture Diagram (textual)

```
┌────────────┐   daily   ┌──────────────┐   PR   ┌─────────────┐  build  ┌────────┐
│ Plan Gen   │──────────▶│ GitHub Repo  │────────▶│ GitHub CI   │────────▶│ Vercel │
│ (GH Act)   │           │ (content)    │        │ (Astro)     │         │  CDN   │
└────────────┘           │   ▲   ▲     │        └─────────────┘         └────────┘
      ▲                  │   │   │     │                ▲
      │ tasks/*.json     │   │   │ MD  │                │
┌────────────┐  fetch  ┌──┴───┴───┴──┐ │    images      │
│  n8n       │────────▶│  R2 Storage │◀┘                │
└────────────┘         └─────────────┘                 │
      ▲                                               search‑index.json
      │ Google Trends / News API
```

## 7. Technology Stack

| Layer                      | Technology                                   | Notes                            |
| -------------------------- | -------------------------------------------- | -------------------------------- |
| **Static Generator**       | Astro 4 + `@astrojs/mdx`                     | ZeroJS HTML, content collections |
| **Styling**                | Tailwind CSS + SCSS utilities                | JIT mode                         |
| **Animation**              | Framer‑Motion (home hero) & Alpine.js (tiny) | Loaded only on demand            |
| **Content Planning**       | GitHub Action `plan-generator.ts`            | Uses OpenAI + pytrends           |
| **Workflow Orchestration** | n8n self‑hosted                              | Trigger via webhook or Git event |
| **LLM**                    | OpenAI GPT‑4o                                | summarise & rewrite              |
| **Image Gen**              | SD XL / DALL·E 3 via API                     | Return WebP 1600×900             |
| **Object Storage**         | Cloudflare R2                                | Public bucket `news-img/`        |
| **Search**                 | Fuse.js + pre‑built `search-index.json`      | < 50 kB compressed               |
| **CI/CD**                  | GitHub Actions + `vercel deploy --prod`      | Token via secret                 |
| **Analytics**              | Plausible + Vercel Analytics                 | No cookies                       |

## 8. Directory Structure

```
repo/
├─ .github/workflows/
│   ├─ plan.yml           # generate task list
│   └─ ci.yml             # lint, build, deploy
├─ tasks/                 # *.json daily backlog
├─ src/
│   ├─ content/news/
│   │   └─ industry/2025-05-30-hypersonic.md
│   ├─ layouts/
│   ├─ components/
│   ├─ pages/[category]/[...slug].astro
│   └─ seo-lint.ts        # custom lint script
├─ .n8n/                  # exported workflows JSON
└─ astro.config.mjs
```

## 9. Key Workflows

### 9.1 Plan Generation (`plan.yml`)

```yaml
on:
  schedule: [{cron: '10 16 * * *'}] # 00:10 SGT
jobs:
  generate:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - name: gen plan
      run: node tools/plan-generator.js > tasks/$(date +%F).json
    - name: commit
      uses: EndBug/add-and-commit@v9
      with:
        add: 'tasks/*.json'
```

### 9.2 n8n Core Steps

1. **Git Trigger** → get newest tasks
2. Loop each task

   * HTTP → fetch sources
   * GPT summarise (system prompt w/ style guide)
   * Function → build image prompt
   * Image Gen → SDXL API
   * S3 (R2) Upload → receive URL
   * Function → compose Markdown (insert `<!--AD_HOOK-->`)
   * GitHub PR (branch `news/auto/${ISO}`)

### 9.3 CI (`ci.yml`) Highlights

```yaml
- run: pnpm run seo:lint # node seo-lint.ts
- run: pnpm build        # astro build --incremental
- run: npx vercel deploy --prod --token ${{ secrets.VERCEL_TOKEN }}
```

## 10. SEO & Soft‑Ad Implementation

* **Keyword Anchoring**: seo‑enricher replaces first occurrence of top‑3 trend keywords with internal link `<a href="/search?query=...">`.
* **Soft‑Ad Component** `RelatedProducts.astro`:

  * Props: `list[]`, `position` (default `top-right`)
  * Injected via build when `<!--AD_HOOK-->` present
  * Produces JSON‑LD `ItemList` linked to each product page
* **Density Guardrails** via `seo-lint.ts` (fail if >3 % anchors or >1 soft‑ad block)

## 11. Image Strategy

| Case                  | Storage                                                           | Markup                                                                                |
| --------------------- | ----------------------------------------------------------------- | ------------------------------------------------------------------------------------- |
| Hero / article header | R2 remote                                                         | `<Image src="https://r2cdn/news/2025/05/slug-hero.webp" width="1200" height="675" />` |
| Product thumbs        | R2 `/products/thumbs`                                             | Loaded lazily in RelatedProducts                                                      |
| Offline fallback      | Optionally mirrored to `public/news-img/` by CI if R2 unreachable |                                                                                       |

## 12. Performance & Monitoring

* Netlify/Vercel Analytics track Web Vitals per deployment
* Lighthouse CI optional
* Plausible custom events: `softAdView`, `softAdClick`

## 13. Security & Rollback

* No write capability on production host
* PR reviews mandatory for auto content; bots have no merge rights
* Vercel keeps 100 previous deployments for instant rollback

## 14. Open Tasks for Implementation Phase

| # | Task                                                | Owner    |
| - | --------------------------------------------------- | -------- |
| 1 | Scaffold Astro project & Tailwind                   | dev‑team |
| 2 | Build `RelatedProducts.astro` & CSS                 | dev‑team |
| 3 | Implement `seo-lint.ts`                             | dev‑team |
| 4 | Write `plan-generator.ts` (OpenAI + pytrends)       | dev‑team |
| 5 | Export n8n workflows & deploy instance              | infra    |
| 6 | Configure R2 bucket & public policy                 | infra    |
| 7 | Set up GitHub secrets (`VERCEL_TOKEN`, `CF_R2_KEY`) | ops      |
| 8 | End‑to‑end test (mock task → live article)          | QA       |

---

**End of Document**
