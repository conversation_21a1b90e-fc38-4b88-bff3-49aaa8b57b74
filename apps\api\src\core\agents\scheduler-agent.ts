import { z } from 'zod';
import { BaseAgent, type AgentContext } from './base-agent.js';
import { taskRepository, planRepository, configRepository } from '../database/repositories.js';
import { PlanGeneratorAgent } from './plan-generator-agent.js';
import { ContentGeneratorAgent } from './content-generator-agent.js';

// 调度任务类型
export enum ScheduleType {
  DAILY = 'daily',
  WEEKLY = 'weekly', 
  MONTHLY = 'monthly',
  CUSTOM = 'custom'
}

// 调度输入schema
export const SchedulerInputSchema = z.object({
  type: z.enum(['plan_generation', 'content_generation', 'batch_processing']),
  schedule: z.object({
    type: z.nativeEnum(ScheduleType),
    interval: z.number().optional(), // 自定义间隔（分钟）
    time: z.string().optional(), // 执行时间 "HH:MM"
    days: z.array(z.number()).optional(), // 周几执行 [1-7]
    enabled: z.boolean().default(true)
  }),
  config: z.record(z.any()).optional(),
  dryRun: z.boolean().default(false)
});

export type SchedulerInput = z.infer<typeof SchedulerInputSchema>;

// 调度任务状态
export interface ScheduledTask {
  id: string;
  type: string;
  schedule: any;
  config: any;
  nextRun: Date;
  lastRun?: Date;
  lastResult?: any;
  enabled: boolean;
  createdAt: Date;
}

/**
 * 调度系统Agent
 * 负责管理和执行各种定时任务，替代n8n工作流
 */
export class SchedulerAgent extends BaseAgent {
  private scheduledTasks: Map<string, ScheduledTask> = new Map();
  private runningTasks: Set<string> = new Set();
  private planGenerator?: PlanGeneratorAgent;
  private contentGenerator?: ContentGeneratorAgent;
  
  constructor() {
    super({
      name: 'SchedulerAgent',
      description: 'Manages and executes scheduled tasks for content pipeline',
      version: '2.0.0',
      maxRetries: 2,
      timeout: 300000 // 5 minutes
    });
  }

  protected async onInitialize(): Promise<void> {
    // 初始化子Agent
    this.planGenerator = new PlanGeneratorAgent();
    this.contentGenerator = new ContentGeneratorAgent();
    
    await this.planGenerator.initialize();
    await this.contentGenerator.initialize();
    
    // 加载现有的调度任务
    await this.loadScheduledTasks();
    
    // 启动调度器
    await this.startScheduler();
  }

  protected async validateInput<T = any>(input: T): Promise<T> {
    const validated = SchedulerInputSchema.parse(input);
    return validated as T;
  }

  protected async onExecute(input: SchedulerInput, context: AgentContext): Promise<any> {
    switch (input.type) {
      case 'plan_generation':
        return await this.schedulePlanGeneration(input);
      
      case 'content_generation':
        return await this.scheduleContentGeneration(input);
      
      case 'batch_processing':
        return await this.scheduleBatchProcessing(input);
      
      default:
        throw new Error(`Unknown schedule type: ${input.type}`);
    }
  }

  protected async onHealthCheck(): Promise<Record<string, any>> {
    const activeTasks = Array.from(this.scheduledTasks.values()).filter(t => t.enabled);
    const runningTasksCount = this.runningTasks.size;
    
    return {
      totalScheduledTasks: this.scheduledTasks.size,
      activeTasks: activeTasks.length,
      runningTasks: runningTasksCount,
      nextRuns: activeTasks
        .sort((a, b) => a.nextRun.getTime() - b.nextRun.getTime())
        .slice(0, 5)
        .map(t => ({
          id: t.id,
          type: t.type,
          nextRun: t.nextRun.toISOString()
        })),
      planGeneratorHealth: await this.planGenerator?.healthCheck(),
      contentGeneratorHealth: await this.contentGenerator?.healthCheck()
    };
  }

  /**
   * 调度计划生成任务
   */
  private async schedulePlanGeneration(input: SchedulerInput): Promise<any> {
    const taskId = `plan_gen_${Date.now()}`;
    
    const scheduledTask: ScheduledTask = {
      id: taskId,
      type: 'plan_generation',
      schedule: input.schedule,
      config: input.config || {
        maxTasks: 10,
        categories: ['technology', 'events', 'products', 'industry']
      },
      nextRun: this.calculateNextRun(input.schedule),
      enabled: input.schedule.enabled,
      createdAt: new Date()
    };

    this.scheduledTasks.set(taskId, scheduledTask);
    
    await this.log('info', 'Plan generation task scheduled', {
      taskId,
      nextRun: scheduledTask.nextRun.toISOString(),
      schedule: input.schedule
    });

    // 如果不是试运行，保存到配置
    if (!input.dryRun) {
      await this.saveScheduledTask(scheduledTask);
    }

    return {
      taskId,
      scheduled: true,
      nextRun: scheduledTask.nextRun.toISOString(),
      type: 'plan_generation'
    };
  }

  /**
   * 调度内容生成任务
   */
  private async scheduleContentGeneration(input: SchedulerInput): Promise<any> {
    const taskId = `content_gen_${Date.now()}`;
    
    const scheduledTask: ScheduledTask = {
      id: taskId,
      type: 'content_generation',
      schedule: input.schedule,
      config: input.config || {
        maxConcurrent: 3,
        generateImages: true,
        categories: ['technology', 'events']
      },
      nextRun: this.calculateNextRun(input.schedule),
      enabled: input.schedule.enabled,
      createdAt: new Date()
    };

    this.scheduledTasks.set(taskId, scheduledTask);
    
    await this.log('info', 'Content generation task scheduled', {
      taskId,
      nextRun: scheduledTask.nextRun.toISOString(),
      schedule: input.schedule
    });

    if (!input.dryRun) {
      await this.saveScheduledTask(scheduledTask);
    }

    return {
      taskId,
      scheduled: true,
      nextRun: scheduledTask.nextRun.toISOString(),
      type: 'content_generation'
    };
  }

  /**
   * 调度批处理任务
   */
  private async scheduleBatchProcessing(input: SchedulerInput): Promise<any> {
    const taskId = `batch_${Date.now()}`;
    
    const scheduledTask: ScheduledTask = {
      id: taskId,
      type: 'batch_processing',
      schedule: input.schedule,
      config: input.config || {
        batchSize: 5,
        processType: 'pending_tasks'
      },
      nextRun: this.calculateNextRun(input.schedule),
      enabled: input.schedule.enabled,
      createdAt: new Date()
    };

    this.scheduledTasks.set(taskId, scheduledTask);
    
    await this.log('info', 'Batch processing task scheduled', {
      taskId,
      nextRun: scheduledTask.nextRun.toISOString(),
      schedule: input.schedule
    });

    if (!input.dryRun) {
      await this.saveScheduledTask(scheduledTask);
    }

    return {
      taskId,
      scheduled: true,
      nextRun: scheduledTask.nextRun.toISOString(),
      type: 'batch_processing'
    };
  }

  /**
   * 启动调度器
   */
  private async startScheduler(): Promise<void> {
    // 每分钟检查一次需要执行的任务
    setInterval(async () => {
      await this.checkAndExecuteTasks();
    }, 60000); // 60秒

    await this.log('info', 'Scheduler started', {
      checkInterval: '60s',
      tasksLoaded: this.scheduledTasks.size
    });
  }

  /**
   * 检查并执行到期的任务
   */
  private async checkAndExecuteTasks(): Promise<void> {
    const now = new Date();
    
    for (const [taskId, task] of this.scheduledTasks) {
      if (!task.enabled || this.runningTasks.has(taskId)) {
        continue;
      }
      
      if (task.nextRun <= now) {
        // 执行任务
        this.runningTasks.add(taskId);
        
        try {
          await this.executeScheduledTask(task);
        } catch (error) {
          await this.log('error', 'Scheduled task execution failed', {
            taskId,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        } finally {
          this.runningTasks.delete(taskId);
        }
      }
    }
  }

  /**
   * 执行调度任务
   */
  private async executeScheduledTask(task: ScheduledTask): Promise<void> {
    await this.log('info', 'Executing scheduled task', {
      taskId: task.id,
      type: task.type
    });

    const startTime = Date.now();
    let result: any;

    try {
      switch (task.type) {
        case 'plan_generation':
          result = await this.executePlanGeneration(task.config);
          break;
          
        case 'content_generation':
          result = await this.executeContentGeneration(task.config);
          break;
          
        case 'batch_processing':
          result = await this.executeBatchProcessing(task.config);
          break;
          
        default:
          throw new Error(`Unknown task type: ${task.type}`);
      }

      // 更新任务信息
      task.lastRun = new Date();
      task.lastResult = result;
      task.nextRun = this.calculateNextRun(task.schedule);

      await this.log('info', 'Scheduled task completed successfully', {
        taskId: task.id,
        duration: Date.now() - startTime,
        nextRun: task.nextRun.toISOString()
      });

    } catch (error) {
      await this.log('error', 'Scheduled task failed', {
        taskId: task.id,
        error: error instanceof Error ? error.message : 'Unknown error',
        duration: Date.now() - startTime
      });

      // 仍然更新下次运行时间
      task.lastRun = new Date();
      task.nextRun = this.calculateNextRun(task.schedule);
    }

    // 保存更新的任务信息
    await this.saveScheduledTask(task);
  }

  /**
   * 执行计划生成
   */
  private async executePlanGeneration(config: any): Promise<any> {
    if (!this.planGenerator) {
      throw new Error('Plan generator not initialized');
    }

    const input = {
      date: new Date().toISOString().split('T')[0],
      maxTasks: config.maxTasks || 10,
      categories: config.categories || ['technology', 'events', 'products', 'industry'],
      customPrompt: config.customPrompt || ''
    };

    return await this.planGenerator.execute(input, {
      requestId: `scheduled_${Date.now()}`,
      userId: 'scheduler',
      metadata: { source: 'scheduled_task' }
    });
  }

  /**
   * 执行内容生成
   */
  private async executeContentGeneration(config: any): Promise<any> {
    if (!this.contentGenerator) {
      throw new Error('Content generator not initialized');
    }

    // 获取待处理的任务
    const pendingTasks = await taskRepository.getByStatus('pending', config.maxConcurrent || 3);
    
    if (pendingTasks.length === 0) {
      return { message: 'No pending tasks found for content generation' };
    }

    const results = [];
    
    for (const task of pendingTasks) {
      try {
        const input = {
          taskId: task.id,
          keyword: task.keyword,
          sourceUrl: task.sourceUrl,
          relatedProducts: task.relatedProducts || [],
          generateImage: config.generateImages !== false,
          dryRun: false
        };

        const result = await this.contentGenerator.execute(input, {
          requestId: `scheduled_${Date.now()}_${task.id}`,
          userId: 'scheduler',
          metadata: { source: 'scheduled_task', taskId: task.id }
        });

        results.push(result);

      } catch (error) {
        await this.log('error', 'Content generation failed for task', {
          taskId: task.id,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return {
      processedTasks: results.length,
      successful: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length,
      results
    };
  }

  /**
   * 执行批处理
   */
  private async executeBatchProcessing(config: any): Promise<any> {
    const batchSize = config.batchSize || 5;
    
    switch (config.processType) {
      case 'pending_tasks':
        return await this.processPendingTasks(batchSize);
      
      case 'failed_tasks':
        return await this.retryFailedTasks(batchSize);
      
      case 'cleanup':
        return await this.cleanupOldData(config.daysToKeep || 30);
      
      default:
        throw new Error(`Unknown batch process type: ${config.processType}`);
    }
  }

  /**
   * 处理待处理任务
   */
  private async processPendingTasks(batchSize: number): Promise<any> {
    const pendingTasks = await taskRepository.getByStatus('pending', batchSize);
    
    if (pendingTasks.length === 0) {
      return { message: 'No pending tasks to process' };
    }

    const results = [];
    
    for (const task of pendingTasks) {
      // 这里可以根据任务类型决定处理方式
      if (task.status === 'pending' && task.sourceUrl) {
        // 自动触发内容生成
        try {
          const input = {
            taskId: task.id,
            keyword: task.keyword,
            sourceUrl: task.sourceUrl,
            relatedProducts: task.relatedProducts || [],
            generateImage: true,
            dryRun: false
          };

          const result = await this.contentGenerator!.execute(input, {
            requestId: `batch_${Date.now()}_${task.id}`,
            userId: 'scheduler',
            metadata: { source: 'batch_processing' }
          });

          results.push(result);

        } catch (error) {
          await this.log('error', 'Batch task processing failed', {
            taskId: task.id,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }
    }

    return {
      batchSize,
      processed: results.length,
      successful: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length
    };
  }

  /**
   * 重试失败任务
   */
  private async retryFailedTasks(batchSize: number): Promise<any> {
    const failedTasks = await taskRepository.getByStatus('failed', batchSize);
    
    if (failedTasks.length === 0) {
      return { message: 'No failed tasks to retry' };
    }

    const results = [];
    
    for (const task of failedTasks) {
      try {
        // 重置任务状态
        await taskRepository.update(task.id, {
          status: 'pending',
          errorMessage: null
        });

        results.push({ taskId: task.id, action: 'reset_to_pending' });

      } catch (error) {
        await this.log('error', 'Failed to reset task', {
          taskId: task.id,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return {
      batchSize,
      processed: results.length,
      resetTasks: results
    };
  }

  /**
   * 清理旧数据
   */
  private async cleanupOldData(daysToKeep: number): Promise<any> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

    // 这里可以实现数据清理逻辑
    // 例如：删除旧的日志、清理临时文件等
    
    return {
      action: 'cleanup_completed',
      cutoffDate: cutoffDate.toISOString(),
      daysKept: daysToKeep
    };
  }

  /**
   * 计算下次运行时间
   */
  private calculateNextRun(schedule: any): Date {
    const now = new Date();
    const nextRun = new Date(now);
    
    switch (schedule.type) {
      case ScheduleType.DAILY:
        if (schedule.time) {
          const [hours, minutes] = schedule.time.split(':').map(Number);
          nextRun.setHours(hours, minutes, 0, 0);
          
          // 如果已经过了今天的时间，设置为明天
          if (nextRun <= now) {
            nextRun.setDate(nextRun.getDate() + 1);
          }
        } else {
          nextRun.setDate(nextRun.getDate() + 1);
        }
        break;
        
      case ScheduleType.WEEKLY:
        const targetDay = schedule.days?.[0] || 1; // 默认周一
        const currentDay = nextRun.getDay() || 7; // 周日为7
        const daysUntilTarget = (targetDay - currentDay + 7) % 7 || 7;
        
        nextRun.setDate(nextRun.getDate() + daysUntilTarget);
        
        if (schedule.time) {
          const [hours, minutes] = schedule.time.split(':').map(Number);
          nextRun.setHours(hours, minutes, 0, 0);
        }
        break;
        
      case ScheduleType.MONTHLY:
        nextRun.setMonth(nextRun.getMonth() + 1, 1); // 下个月第一天
        
        if (schedule.time) {
          const [hours, minutes] = schedule.time.split(':').map(Number);
          nextRun.setHours(hours, minutes, 0, 0);
        }
        break;
        
      case ScheduleType.CUSTOM:
        const intervalMs = (schedule.interval || 60) * 60 * 1000; // 默认1小时
        nextRun.setTime(now.getTime() + intervalMs);
        break;
        
      default:
        nextRun.setHours(nextRun.getHours() + 1); // 默认1小时后
    }
    
    return nextRun;
  }

  /**
   * 加载调度任务
   */
  private async loadScheduledTasks(): Promise<void> {
    try {
      const scheduledTasksConfig = await configRepository.get('scheduled_tasks');
      
      if (scheduledTasksConfig) {
        const tasks = JSON.parse(scheduledTasksConfig) as ScheduledTask[];
        
        for (const task of tasks) {
          // 重新计算下次运行时间
          task.nextRun = new Date(task.nextRun);
          if (task.lastRun) {
            task.lastRun = new Date(task.lastRun);
          }
          
          this.scheduledTasks.set(task.id, task);
        }
        
        await this.log('info', 'Scheduled tasks loaded', {
          count: tasks.length
        });
      }
      
    } catch (error) {
      await this.log('warn', 'Failed to load scheduled tasks', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * 保存调度任务
   */
  private async saveScheduledTask(task: ScheduledTask): Promise<void> {
    try {
      const allTasks = Array.from(this.scheduledTasks.values());
      await configRepository.set('scheduled_tasks', JSON.stringify(allTasks));
      
    } catch (error) {
      await this.log('error', 'Failed to save scheduled task', {
        taskId: task.id,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * 获取所有调度任务
   */
  async getScheduledTasks(): Promise<ScheduledTask[]> {
    return Array.from(this.scheduledTasks.values());
  }

  /**
   * 删除调度任务
   */
  async removeScheduledTask(taskId: string): Promise<boolean> {
    if (this.scheduledTasks.has(taskId)) {
      this.scheduledTasks.delete(taskId);
      
      // 保存更新
      const allTasks = Array.from(this.scheduledTasks.values());
      await configRepository.set('scheduled_tasks', JSON.stringify(allTasks));
      
      await this.log('info', 'Scheduled task removed', { taskId });
      return true;
    }
    
    return false;
  }

  /**
   * 启用/禁用调度任务
   */
  async toggleScheduledTask(taskId: string, enabled: boolean): Promise<boolean> {
    const task = this.scheduledTasks.get(taskId);
    
    if (task) {
      task.enabled = enabled;
      await this.saveScheduledTask(task);
      
      await this.log('info', 'Scheduled task toggled', { 
        taskId, 
        enabled 
      });
      
      return true;
    }
    
    return false;
  }
}