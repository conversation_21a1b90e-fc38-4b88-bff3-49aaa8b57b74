import fetch from 'node-fetch';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';
import { parseFrontmatter, generateFrontmatter } from '@news-site/shared';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

export interface HeroImageValidationResult {
  success: boolean;
  totalFiles: number;
  fixedFiles: number;
  validFiles: number;
  fixedDetails: Array<{
    file: string;
    reason: string;
    oldImage: string | null;
    newImage: string;
  }>;
  errors: Array<{
    file: string;
    error: string;
  }>;
  logs: string[];
}

export interface HeroImageValidatorConfig {
  contentDir?: string;
  fallbackImage?: string;
  timeout?: number;
  validateUrls?: boolean;
}

export class HeroImageValidator {
  private config: Required<HeroImageValidatorConfig>;
  private logs: string[] = [];

  constructor(config: HeroImageValidatorConfig = {}) {
    // 计算默认的content目录路径
    const projectRoot = join(__dirname, '../../../../../');
    
    this.config = {
      contentDir: config.contentDir || join(projectRoot, 'apps/web/src/content/news'),
      fallbackImage: config.fallbackImage || '/hero-bg.jpg',
      timeout: config.timeout || 5000,
      validateUrls: config.validateUrls ?? true,
      ...config
    };
  }

  /**
   * 验证并修复所有文章的heroImage
   */
  async validateAndFix(): Promise<HeroImageValidationResult> {
    this.logs = [];
    this.log('🔍 开始heroImage验证和修复...');
    this.log(`📂 扫描目录: ${this.config.contentDir}`);
    this.log(`🖼️ fallback图片: ${this.config.fallbackImage}`);

    const result: HeroImageValidationResult = {
      success: true,
      totalFiles: 0,
      fixedFiles: 0,
      validFiles: 0,
      fixedDetails: [],
      errors: [],
      logs: []
    };

    try {
      // 检查目录是否存在
      if (!fs.existsSync(this.config.contentDir)) {
        throw new Error(`内容目录不存在: ${this.config.contentDir}`);
      }

      // 获取所有markdown文件
      const markdownFiles = this.getAllMarkdownFiles(this.config.contentDir);
      result.totalFiles = markdownFiles.length;
      this.log(`📄 找到 ${markdownFiles.length} 个markdown文件`);

      // 处理每个文件
      for (const filePath of markdownFiles) {
        try {
          const fileResult = await this.validateAndFixFile(filePath);
          
          if (fileResult.fixed) {
            result.fixedFiles++;
            result.fixedDetails.push({
              file: join(this.config.contentDir, filePath).replace(this.config.contentDir, ''),
              reason: fileResult.reason,
              oldImage: fileResult.oldImage,
              newImage: fileResult.newImage
            });
          } else {
            result.validFiles++;
          }

          // 避免请求过于频繁
          if (this.config.validateUrls) {
            await new Promise(resolve => setTimeout(resolve, 200));
          }
        } catch (error) {
          result.errors.push({
            file: join(this.config.contentDir, filePath).replace(this.config.contentDir, ''),
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

      // 统计结果
      this.log('✅ heroImage验证完成');
      this.log(`📊 总文件数: ${result.totalFiles}`);
      this.log(`🔧 需要修复: ${result.fixedFiles}`);
      this.log(`✅ 无需修复: ${result.validFiles}`);
      
      if (result.errors.length > 0) {
        this.log(`❌ 处理失败: ${result.errors.length}`);
        result.success = false;
      }

      result.logs = [...this.logs];
      return result;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.log(`❌ heroImage验证失败: ${errorMessage}`);
      result.success = false;
      result.errors.push({
        file: 'GENERAL',
        error: errorMessage
      });
      result.logs = [...this.logs];
      return result;
    }
  }

  /**
   * 验证并修复单个文件
   */
  private async validateAndFixFile(filePath: string): Promise<{
    fixed: boolean;
    reason: string;
    oldImage: string | null;
    newImage: string;
  }> {
    const content = fs.readFileSync(filePath, 'utf-8');
    const { frontmatter, content: articleContent } = parseFrontmatter(content);

    const originalHeroImage = frontmatter.heroImage;
    let needsFix = false;
    let reason = '';

    // 检查是否需要修复
    if (!originalHeroImage || 
        originalHeroImage === 'undefined' || 
        originalHeroImage.trim() === '') {
      needsFix = true;
      reason = 'heroImage未定义';
    } else if (originalHeroImage !== this.config.fallbackImage && this.config.validateUrls) {
      // 检查图片链接是否有效
      const isAlive = await this.isImageUrlAlive(originalHeroImage);
      if (!isAlive) {
        needsFix = true;
        reason = 'heroImage链接无效';
      }
    }

    if (needsFix) {
      this.log(`🔧 修复文件: ${join(this.config.contentDir, filePath).replace(this.config.contentDir, '')} - ${reason}`);
      
      // 更新frontmatter
      frontmatter.heroImage = this.config.fallbackImage;

      // 生成新内容
      const newContent = generateFrontmatter(frontmatter) + articleContent;

      // 写入文件
      fs.writeFileSync(filePath, newContent, 'utf-8');
      
      return {
        fixed: true,
        reason,
        oldImage: originalHeroImage,
        newImage: this.config.fallbackImage
      };
    }

    return {
      fixed: false,
      reason: 'heroImage有效',
      oldImage: originalHeroImage,
      newImage: originalHeroImage || this.config.fallbackImage
    };
  }

  /**
   * 检查图片URL是否可访问
   */
  async isImageUrlAlive(url: string): Promise<boolean> {
    try {
      // 跳过相对路径
      if (url.startsWith('/')) {
        return true;
      }

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

      const response = await fetch(url, {
        method: 'HEAD',
        signal: controller.signal,
        headers: {
          'User-Agent': 'HeroImageValidator/1.0'
        }
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        return false;
      }

      const contentType = response.headers.get('content-type');
      return contentType ? contentType.startsWith('image/') : false;

    } catch (error) {
      return false;
    }
  }

  /**
   * 递归获取所有markdown文件
   */
  private getAllMarkdownFiles(dir: string): string[] {
    const files: string[] = [];
    
    const scanDir = (currentDir: string) => {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          scanDir(fullPath);
        } else if (item.endsWith('.md')) {
          files.push(item);
        }
      }
    };
    
    scanDir(dir);
    return files;
  }

  private log(message: string): void {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ${message}`;
    this.logs.push(logMessage);
    console.log(logMessage);
  }

  /**
   * 获取日志
   */
  getLogs(): string[] {
    return [...this.logs];
  }
}

// 创建默认实例
export const heroImageValidator = new HeroImageValidator(); 