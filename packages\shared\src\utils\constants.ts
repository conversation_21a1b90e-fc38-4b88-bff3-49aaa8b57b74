// API相关常量
export const API_VERSION = 'v1';
export const API_PREFIX = `/api/${API_VERSION}`;

// 默认配置
export const DEFAULT_CONFIG = {
  maxTasks: 10,
  defaultWordCount: 1000,
  maxWordCount: 5000,
  minWordCount: 500,
  defaultPriority: 3,
  defaultEstimatedDuration: 60, // minutes
} as const;

// 分页配置
export const PAGINATION = {
  defaultPage: 1,
  defaultLimit: 20,
  maxLimit: 100,
} as const;

// 类别配置
export const CATEGORIES = [
  'industry',
  'research',
  'events', 
  'frontier',
  'insight',
  'misc'
] as const;

// 任务状态
export const TASK_STATUSES = [
  'pending',
  'in-progress',
  'completed',
  'failed'
] as const;

// 计划状态  
export const PLAN_STATUSES = [
  'draft',
  'active',
  'completed'
] as const;

// HTTP状态码
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
} as const;

// 错误代码
export const ERROR_CODES = {
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  TASK_NOT_FOUND: 'TASK_NOT_FOUND',
  PLAN_NOT_FOUND: 'PLAN_NOT_FOUND',
  GENERATION_FAILED: 'GENERATION_FAILED',
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  INTERNAL_ERROR: 'INTERNAL_ERROR',
} as const;

// 文件路径模式
export const FILE_PATTERNS = {
  taskFile: (date: string) => `tasks/${date}.json`,
  contentFile: (category: string, slug: string) => `src/content/news/${category}/${slug}.md`,
  planFile: (date: string) => `data/plans/${date}.json`,
} as const; 