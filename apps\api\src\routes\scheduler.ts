import type { FastifyInstance } from 'fastify';
import { SchedulerAgent } from '../core/agents/scheduler-agent.js';

export async function schedulerRoutes(fastify: FastifyInstance) {
  const scheduler = new SchedulerAgent();
  await scheduler.initialize();

  // 创建调度任务
  fastify.post('/schedule', async (request, reply) => {
    try {
      const result = await scheduler.execute(request.body, {
        requestId: request.id,
        userId: 'api-user',
        dryRun: false,
        metadata: { source: 'api', endpoint: '/api/scheduler/schedule' }
      });

      return {
        success: true,
        data: result
      };
    } catch (error) {
      reply.code(400);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  // 获取所有调度任务
  fastify.get('/tasks', async (request, reply) => {
    try {
      const tasks = await scheduler.getScheduledTasks();
      return {
        success: true,
        data: {
          total: tasks.length,
          tasks
        }
      };
    } catch (error) {
      reply.code(500);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  // 删除调度任务
  fastify.delete('/tasks/:taskId', async (request, reply) => {
    try {
      const { taskId } = request.params as { taskId: string };
      const success = await scheduler.removeScheduledTask(taskId);
      
      if (success) {
        return {
          success: true,
          message: `Scheduled task ${taskId} removed successfully`
        };
      } else {
        reply.code(404);
        return {
          success: false,
          error: `Scheduled task ${taskId} not found`
        };
      }
    } catch (error) {
      reply.code(500);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  // 启用/禁用调度任务
  fastify.patch('/tasks/:taskId/toggle', async (request, reply) => {
    try {
      const { taskId } = request.params as { taskId: string };
      const { enabled } = request.body as { enabled: boolean };
      
      const success = await scheduler.toggleScheduledTask(taskId, enabled);
      
      if (success) {
        return {
          success: true,
          message: `Scheduled task ${taskId} ${enabled ? 'enabled' : 'disabled'}`
        };
      } else {
        reply.code(404);
        return {
          success: false,
          error: `Scheduled task ${taskId} not found`
        };
      }
    } catch (error) {
      reply.code(500);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  // 调度器健康检查
  fastify.get('/health', async (request, reply) => {
    try {
      const health = await scheduler.healthCheck();
      return {
        success: true,
        data: health
      };
    } catch (error) {
      reply.code(500);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });
} 