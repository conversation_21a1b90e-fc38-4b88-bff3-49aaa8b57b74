---
import { getCollection } from 'astro:content';
import BaseLayout from '../layouts/BaseLayout.astro';
import Header from '../components/Header.astro';
import Footer from '../components/Footer.astro';

// 获取所有已发布的文章
const allPosts = await getCollection('news', ({ data }) => {
  return data.draft !== true;
});

// 按日期排序（最新的在前）
const sortedPosts = allPosts.sort((a, b) => 
  new Date(b.data.date).getTime() - new Date(a.data.date).getTime()
);

// 按年份分组
const postsByYear = sortedPosts.reduce((acc, post) => {
  const year = new Date(post.data.date).getFullYear();
  if (!acc[year]) {
    acc[year] = [];
  }
  acc[year].push(post);
  return acc;
}, {} as Record<number, typeof sortedPosts>);

// 获取年份列表（降序）
const years = Object.keys(postsByYear).map(Number).sort((a, b) => b - a);

// 分类信息
const categories = [
  { id: 'industry', name: 'Industry', icon: '🏭', color: 'blue' },
  { id: 'research', name: 'Research', icon: '🔬', color: 'green' },
  { id: 'events', name: 'Events', icon: '📅', color: 'purple' },
  { id: 'frontier', name: 'Frontier', icon: '🚀', color: 'red' },
  { id: 'insight', name: 'Insight', icon: '💡', color: 'yellow' },
  { id: 'misc', name: 'Misc', icon: '📰', color: 'gray' }
];

// 获取分类样式
function getCategoryStyles(categoryId: string) {
  const styles = {
    industry: { bg: 'bg-blue-100', text: 'text-blue-800', hover: 'hover:bg-blue-200' },
    research: { bg: 'bg-green-100', text: 'text-green-800', hover: 'hover:bg-green-200' },
    events: { bg: 'bg-purple-100', text: 'text-purple-800', hover: 'hover:bg-purple-200' },
    frontier: { bg: 'bg-red-100', text: 'text-red-800', hover: 'hover:bg-red-200' },
    insight: { bg: 'bg-yellow-100', text: 'text-yellow-800', hover: 'hover:bg-yellow-200' },
    misc: { bg: 'bg-gray-100', text: 'text-gray-800', hover: 'hover:bg-gray-200' }
  };
  return styles[categoryId as keyof typeof styles] || styles.misc;
}

// 格式化日期
function formatDate(date: Date) {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }).format(date);
}
---

<BaseLayout 
  title="Article Archive | Aerospace & Defense News"
  description="Browse our complete archive of aerospace and defense industry articles, organized by year and category."
>
  <Header />
  
  <main class="min-h-screen bg-gray-50">
    <!-- Hero Section -->
    <section class="bg-gradient-to-br from-primary-900 via-primary-800 to-primary-700 text-white py-16">
      <div class="container-page">
        <div class="max-w-4xl mx-auto text-center">
          <h1 class="heading-xl mb-6">
            Article Archive
          </h1>
          <p class="body-lg text-primary-100 mb-8">
            Explore our complete collection of {allPosts.length} articles covering aerospace, defense, and space technology.
          </p>
          
          <!-- Stats -->
          <div class="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-2xl mx-auto">
            <div class="text-center">
              <div class="text-2xl font-bold text-accent-400">{allPosts.length}</div>
              <div class="text-sm text-primary-200">Total Articles</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-accent-400">{years.length}</div>
              <div class="text-sm text-primary-200">Years Covered</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-accent-400">{categories.length}</div>
              <div class="text-sm text-primary-200">Categories</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-accent-400">{allPosts.filter(post => post.data.featured).length}</div>
              <div class="text-sm text-primary-200">Featured</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Filter Section -->
    <section class="py-8 bg-white border-b border-gray-200">
      <div class="container-page">
        <div class="flex flex-wrap items-center justify-between gap-4">
          <div class="flex flex-wrap items-center gap-2">
            <span class="text-sm font-medium text-gray-700">Filter by category:</span>
            <a href="/archive" class="px-3 py-1 text-xs font-medium bg-primary-100 text-primary-700 rounded-full hover:bg-primary-200 transition-colors">
              All
            </a>
            {categories.map(category => {
              const styles = getCategoryStyles(category.id);
              return (
                <a
                  href={`/category/${category.id}`}
                  class={`px-3 py-1 text-xs font-medium ${styles.bg} ${styles.text} rounded-full ${styles.hover} transition-colors`}
                >
                  {category.icon} {category.name}
                </a>
              );
            })}
          </div>
          
          <div class="text-sm text-gray-500">
            Showing {allPosts.length} articles
          </div>
        </div>
      </div>
    </section>

    <!-- Articles by Year -->
    <section class="py-12">
      <div class="container-page">
        <div class="max-w-4xl mx-auto">
          {years.map(year => (
            <div class="mb-12">
              <!-- Year Header -->
              <div class="flex items-center mb-8">
                <h2 class="text-3xl font-bold text-gray-900 mr-4">{year}</h2>
                <div class="flex-1 h-px bg-gray-300"></div>
                <span class="ml-4 px-3 py-1 bg-gray-100 text-gray-600 text-sm font-medium rounded-full">
                  {postsByYear[year].length} articles
                </span>
              </div>

              <!-- Articles Grid -->
              <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {postsByYear[year].map(post => (
                  <article class="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200">
                    {post.data.heroImage && (
                      <div class="aspect-video overflow-hidden rounded-t-lg">
                        <img 
                          src={post.data.heroImage} 
                          alt={post.data.title}
                          class="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                          loading="lazy"
                        />
                      </div>
                    )}
                    
                    <div class="p-6">
                      <!-- Category Badge -->
                      <div class="flex items-center justify-between mb-3">
                        <span class={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getCategoryStyles(post.data.category).bg} ${getCategoryStyles(post.data.category).text}`}>
                          {categories.find(cat => cat.id === post.data.category)?.icon}
                          <span class="ml-1 capitalize">{post.data.category}</span>
                        </span>
                        
                        {post.data.featured && (
                          <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                            ⭐ Featured
                          </span>
                        )}
                      </div>

                      <!-- Title -->
                      <h3 class="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                        <a href={`/news/${post.id}`} class="hover:text-primary-600 transition-colors">
                          {post.data.title}
                        </a>
                      </h3>

                      <!-- Description -->
                      {post.data.description && (
                        <p class="text-gray-600 text-sm mb-4 line-clamp-3">
                          {post.data.description}
                        </p>
                      )}

                      <!-- Meta -->
                      <div class="flex items-center justify-between text-xs text-gray-500">
                        <time datetime={post.data.date.toISOString()}>
                          {formatDate(post.data.date)}
                        </time>
                        
                        {post.data.readTime && (
                          <span>{post.data.readTime} min read</span>
                        )}
                      </div>

                      <!-- Tags -->
                      {post.data.tags && post.data.tags.length > 0 && (
                        <div class="mt-3 flex flex-wrap gap-1">
                          {post.data.tags.slice(0, 3).map(tag => (
                            <span class="inline-block px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                              {tag}
                            </span>
                          ))}
                          {post.data.tags.length > 3 && (
                            <span class="inline-block px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                              +{post.data.tags.length - 3} more
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                  </article>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>

    <!-- Back to Top -->
    <div class="text-center py-8">
      <button 
        onclick="window.scrollTo({ top: 0, behavior: 'smooth' })"
        class="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
      >
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
        </svg>
        Back to Top
      </button>
    </div>
  </main>

  <Footer />
</BaseLayout>

<style>
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>
