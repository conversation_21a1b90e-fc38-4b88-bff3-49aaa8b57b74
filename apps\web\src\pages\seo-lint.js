#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class SEOLinter {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.maxAnchorDensity = 0.03; // 3% max anchor density
    this.maxSoftAdBlocks = 1; // Max 1 soft-ad block per article
  }

  log(level, file, message) {
    const logEntry = { level, file, message };
    if (level === 'error') {
      this.errors.push(logEntry);
    } else {
      this.warnings.push(logEntry);
    }
  }

  // Check markdown files for SEO issues
  async lintMarkdownFile(filePath) {
    const content = fs.readFileSync(filePath, 'utf-8');
    const relativePath = path.relative(process.cwd(), filePath);
    
    // Extract frontmatter
    const frontmatterMatch = content.match(/^---\n([\s\S]*?)\n---/);
    if (!frontmatterMatch) {
      this.log('error', relativePath, 'Missing frontmatter');
      return;
    }

    const frontmatter = frontmatterMatch[1];
    const body = content.replace(frontmatterMatch[0], '').trim();

    // Check required frontmatter fields
    this.checkFrontmatter(frontmatter, relativePath);
    
    // Check body content
    this.checkBodyContent(body, relativePath);
    
    // Check content structure and readability
    this.checkContentStructure(body, relativePath);
  }

  checkFrontmatter(frontmatter, filePath) {
    // Enhanced required fields check
    const requiredFields = ['title', 'date', 'category', 'author', 'lang'];
    const recommendedFields = ['description', 'tags', 'heroImage', 'readTime'];
    
    requiredFields.forEach(field => {
      if (!frontmatter.includes(`${field}:`)) {
        this.log('error', filePath, `Missing required frontmatter field: ${field}`);
      }
    });

    recommendedFields.forEach(field => {
      if (!frontmatter.includes(`${field}:`)) {
        this.log('warning', filePath, `Missing recommended frontmatter field: ${field}`);
      }
    });

    // Enhanced title validation
    const titleMatch = frontmatter.match(/title:\s*['"]([^'"]+)['"]/);
    if (titleMatch) {
      const title = titleMatch[1];
      if (title.length < 30) {
        this.log('warning', filePath, `Title is too short (${title.length} characters, recommend 30-60)`);
      }
      if (title.length > 60) {
        this.log('error', filePath, `Title is too long (${title.length} characters, max 60 for SEO)`);
      }
      
      // Check if title contains power words
      const powerWords = ['advanced', 'innovative', 'breakthrough', 'next-generation', 'cutting-edge', 'revolutionary'];
      const hasPowerWord = powerWords.some(word => title.toLowerCase().includes(word));
      if (!hasPowerWord) {
        this.log('info', filePath, 'Consider adding power words to title for better engagement');
      }
    }

    // Enhanced description validation
    const descMatch = frontmatter.match(/description:\s*['"]([^'"]+)['"]/);
    if (descMatch) {
      const description = descMatch[1];
      if (description.length < 120) {
        this.log('warning', filePath, `Meta description too short (${description.length} chars, recommend 120-155)`);
      }
      if (description.length > 155) {
        this.log('error', filePath, `Meta description too long (${description.length} chars, max 155)`);
      }
      
      // Check if description ends with call-to-action
      const ctaWords = ['learn more', 'discover', 'explore', 'find out', 'read more'];
      const hasCallToAction = ctaWords.some(cta => description.toLowerCase().includes(cta));
      if (!hasCallToAction) {
        this.log('info', filePath, 'Consider adding call-to-action to description');
      }
    } else {
      this.log('error', filePath, 'Missing description in frontmatter (required for SEO)');
    }

    // Check tags
    const tagsMatch = frontmatter.match(/tags:\s*\[([^\]]+)\]/);
    if (tagsMatch) {
      const tags = tagsMatch[1].split(',').map(tag => tag.trim().replace(/['"]/g, ''));
      if (tags.length < 3) {
        this.log('warning', filePath, `Too few tags (${tags.length}, recommend 3-8)`);
      }
      if (tags.length > 8) {
        this.log('warning', filePath, `Too many tags (${tags.length}, recommend 3-8)`);
      }
      
      // Check for industry-relevant tags
      const industryTags = ['aerospace', 'defense', 'technology', 'innovation', 'military', 'aviation'];
      const hasIndustryTag = tags.some(tag => industryTags.includes(tag.toLowerCase()));
      if (!hasIndustryTag) {
        this.log('info', filePath, 'Consider adding industry-relevant tags');
      }
    }

    // Check heroImage
    const heroImageMatch = frontmatter.match(/heroImage:\s*['"]([^'"]+)['"]/);
    if (heroImageMatch) {
      const imageUrl = heroImageMatch[1];
      if (!imageUrl.includes('w=1200') && !imageUrl.includes('1200x')) {
        this.log('warning', filePath, 'Hero image should be optimized for 1200px width');
      }
    }

    // Check relatedProducts
    const relatedProductsMatch = frontmatter.match(/relatedProducts:\s*\[([^\]]*)\]/);
    if (relatedProductsMatch) {
      const products = relatedProductsMatch[1].split(',').filter(p => p.trim());
      if (products.length === 0) {
        this.log('warning', filePath, 'Empty relatedProducts array (consider removing or adding products)');
      }
      if (products.length > 5) {
        this.log('warning', filePath, `Too many related products (${products.length}, recommend 2-5)`);
      }
    }

    // Check readTime
    const readTimeMatch = frontmatter.match(/readTime:\s*(\d+)/);
    if (readTimeMatch) {
      const readTime = parseInt(readTimeMatch[1]);
      if (readTime < 2) {
        this.log('warning', filePath, 'Read time seems too short (< 2 minutes)');
      }
      if (readTime > 15) {
        this.log('warning', filePath, 'Read time seems too long (> 15 minutes)');
      }
    }
  }

  checkBodyContent(body, filePath) {
    // Enhanced word count analysis
    const wordCount = body.split(/\s+/).filter(word => word.length > 0).length;
    
    if (wordCount < 800) {
      this.log('warning', filePath, `Article is too short (${wordCount} words, recommend 800-2000 for SEO)`);
    } else if (wordCount > 3000) {
      this.log('warning', filePath, `Article is very long (${wordCount} words, consider breaking into series)`);
    }

    // Check keyword density
    this.checkKeywordDensity(body, filePath);

    // Enhanced image validation
    const imageRegex = /!\[([^\]]*)\]\([^)]+\)/g;
    const images = [...body.matchAll(imageRegex)];
    
    if (images.length === 0) {
      this.log('warning', filePath, 'No images found (images improve engagement and SEO)');
    }
    
    images.forEach((match, index) => {
      const altText = match[1];
      if (!altText || altText.trim().length === 0) {
        this.log('error', filePath, `Image ${index + 1} missing alt text (critical for accessibility)`);
      } else if (altText.length < 10) {
        this.log('warning', filePath, `Image ${index + 1} alt text too short (${altText.length} chars, recommend 10-125)`);
      } else if (altText.length > 125) {
        this.log('warning', filePath, `Image ${index + 1} alt text too long (${altText.length} chars, max 125)`);
      }
    });

    // Check anchor density
    const linkRegex = /\[([^\]]+)\]\([^)]+\)/g;
    const links = [...body.matchAll(linkRegex)];
    const linkWordCount = links.reduce((sum, match) => {
      return sum + match[1].split(/\s+/).length;
    }, 0);
    
    const anchorDensity = linkWordCount / wordCount;
    if (anchorDensity > this.maxAnchorDensity) {
      this.log('error', filePath, `Anchor density too high: ${(anchorDensity * 100).toFixed(1)}% (max ${this.maxAnchorDensity * 100}%)`);
    }

    // Check for soft-ad blocks
    const softAdBlocks = (body.match(/<!--AD_HOOK-->/g) || []).length;
    if (softAdBlocks > this.maxSoftAdBlocks) {
      this.log('error', filePath, `Too many soft-ad blocks: ${softAdBlocks} (max ${this.maxSoftAdBlocks})`);
    }

    // Check heading structure
    const headings = [...body.matchAll(/^(#{1,6})\s+(.+)$/gm)];
    let lastLevel = 0;
    
    headings.forEach((match, index) => {
      const level = match[1].length;
      const text = match[2];
      
      if (level > lastLevel + 1) {
        this.log('warning', filePath, `Heading level skip: h${lastLevel} to h${level} ("${text}")`);
      }
      
      if (text.length > 100) {
        this.log('warning', filePath, `Heading too long: "${text}" (${text.length} chars)`);
      }
      
      lastLevel = level;
    });

    // Check for keyword stuffing (simple check)
    const sentences = body.split(/[.!?]+/);
    sentences.forEach((sentence, index) => {
      const words = sentence.toLowerCase().split(/\s+/);
      const wordCounts = {};
      
      words.forEach(word => {
        if (word.length > 4) { // Only check longer words
          wordCounts[word] = (wordCounts[word] || 0) + 1;
        }
      });
      
      Object.entries(wordCounts).forEach(([word, count]) => {
        if (count > 3 && words.length > 10) {
          this.log('warning', filePath, `Possible keyword stuffing: "${word}" appears ${count} times in sentence ${index + 1}`);
        }
      });
    });
  }

  // Check keyword density
  checkKeywordDensity(body, filePath) {
    const words = body.toLowerCase().split(/\s+/).filter(word => word.length > 3);
    const totalWords = words.length;
    
    if (totalWords === 0) return;
    
    // Count word frequency
    const wordCounts = {};
    words.forEach(word => {
      // Clean word (remove punctuation)
      const cleanWord = word.replace(/[^\w]/g, '');
      if (cleanWord.length > 3) {
        wordCounts[cleanWord] = (wordCounts[cleanWord] || 0) + 1;
      }
    });
    
    // Check for potential keyword stuffing
    Object.entries(wordCounts).forEach(([word, count]) => {
      const density = (count / totalWords) * 100;
      
      if (density > 3) {
        this.log('error', filePath, `Keyword stuffing detected: "${word}" appears ${count} times (${density.toFixed(1)}% density, max 3%)`);
      } else if (density > 2) {
        this.log('warning', filePath, `High keyword density: "${word}" appears ${count} times (${density.toFixed(1)}% density)`);
      }
    });
    
    // Check for industry keywords
    const industryKeywords = [
      'aerospace', 'defense', 'aviation', 'military', 'radar', 'guidance',
      'navigation', 'propulsion', 'missile', 'gyroscope', 'accelerometer',
      'thermal', 'battery', 'fiber', 'optic', 'mems', 'sensor', 'detector'
    ];
    
    const foundIndustryKeywords = industryKeywords.filter(keyword => 
      body.toLowerCase().includes(keyword)
    );
    
    if (foundIndustryKeywords.length === 0) {
      this.log('warning', filePath, 'No industry-specific keywords found (consider adding relevant technical terms)');
    } else if (foundIndustryKeywords.length === 1) {
      this.log('info', filePath, `Found ${foundIndustryKeywords.length} industry keyword: ${foundIndustryKeywords.join(', ')}`);
    } else {
      this.log('info', filePath, `Found ${foundIndustryKeywords.length} industry keywords: ${foundIndustryKeywords.slice(0, 3).join(', ')}${foundIndustryKeywords.length > 3 ? '...' : ''}`);
    }
  }

  // Check content structure and readability
  checkContentStructure(body, filePath) {
    // Check paragraph length
    const paragraphs = body.split('\n\n').filter(p => p.trim().length > 0);
    
    paragraphs.forEach((paragraph, index) => {
      const words = paragraph.split(/\s+/).length;
      if (words > 150) {
        this.log('warning', filePath, `Paragraph ${index + 1} is too long (${words} words, recommend < 150)`);
      }
    });
    
    // Check sentence length
    const sentences = body.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const longSentences = sentences.filter(s => s.split(/\s+/).length > 25);
    
    if (longSentences.length > 0) {
      this.log('warning', filePath, `${longSentences.length} sentences are too long (> 25 words, affects readability)`);
    }
    
    // Check for transition words
    const transitionWords = [
      'however', 'therefore', 'furthermore', 'moreover', 'additionally',
      'consequently', 'meanwhile', 'nevertheless', 'subsequently', 'similarly'
    ];
    
    const foundTransitions = transitionWords.filter(word => 
      body.toLowerCase().includes(word)
    );
    
    if (foundTransitions.length < 2) {
      this.log('info', filePath, 'Consider adding transition words to improve readability');
    }
    
    // Check for lists and bullet points
    const hasBulletPoints = /^[-*+]\s/m.test(body);
    const hasNumberedList = /^\d+\.\s/m.test(body);
    
    if (!hasBulletPoints && !hasNumberedList) {
      this.log('info', filePath, 'Consider adding lists or bullet points to improve scannability');
    }
  }

  // Lint HTML files (for generated pages)
  async lintHTMLFile(filePath) {
    const content = fs.readFileSync(filePath, 'utf-8');
    const relativePath = path.relative(process.cwd(), filePath);

    // Check for basic SEO elements
    if (!content.includes('<title>')) {
      this.log('error', relativePath, 'Missing <title> tag');
    }

    if (!content.includes('name="description"')) {
      this.log('error', relativePath, 'Missing meta description');
    }

    if (!content.includes('property="og:')) {
      this.log('warning', relativePath, 'Missing Open Graph tags');
    }

    // Check for JSON-LD
    if (!content.includes('application/ld+json')) {
      this.log('warning', relativePath, 'Missing JSON-LD structured data');
    }

    // Check image alt attributes
    const imgRegex = /<img[^>]+>/gi;
    const images = [...content.matchAll(imgRegex)];
    
    images.forEach((match, index) => {
      const imgTag = match[0];
      if (!imgTag.includes('alt=')) {
        this.log('error', relativePath, `Image ${index + 1} missing alt attribute`);
      }
    });
  }

  async lintDirectory(dirPath, extensions = ['.md', '.mdx']) {
    const files = fs.readdirSync(dirPath, { withFileTypes: true });
    
    for (const file of files) {
      const fullPath = path.join(dirPath, file.name);
      
      if (file.isDirectory()) {
        await this.lintDirectory(fullPath, extensions);
      } else if (extensions.some(ext => file.name.endsWith(ext))) {
        await this.lintMarkdownFile(fullPath);
      }
    }
  }

  generateReport() {
    console.log('\n🔍 SEO Lint Report\n');
    console.log('==================\n');

    if (this.errors.length === 0 && this.warnings.length === 0) {
      console.log('✅ No issues found!\n');
      return true;
    }

    if (this.errors.length > 0) {
      console.log(`❌ Errors (${this.errors.length}):\n`);
      this.errors.forEach(error => {
        console.log(`   ${error.file}: ${error.message}`);
      });
      console.log('');
    }

    if (this.warnings.length > 0) {
      console.log(`⚠️  Warnings (${this.warnings.length}):\n`);
      this.warnings.forEach(warning => {
        console.log(`   ${warning.file}: ${warning.message}`);
      });
      console.log('');
    }

    console.log(`Summary: ${this.errors.length} errors, ${this.warnings.length} warnings\n`);
    
    // Exit with error code if there are errors
    return this.errors.length === 0;
  }
}

// Main execution
async function main() {
  const linter = new SEOLinter();
  
  // Lint content directory
  const contentDir = path.join(process.cwd(), 'src', 'content', 'news');
  
  if (fs.existsSync(contentDir)) {
    console.log('🔍 Linting content files...');
    await linter.lintDirectory(contentDir);
  } else {
    console.log('⚠️  Content directory not found:', contentDir);
  }

  // Generate report
  const success = linter.generateReport();
  
  // Exit with appropriate code
  process.exit(success ? 0 : 1);
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export default SEOLinter; 