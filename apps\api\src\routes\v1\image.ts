import { FastifyPluginAsync } from 'fastify';
import { z } from 'zod';
import { createImageService } from '../../core/tools/image/image-service.js';

// 图片生成请求schema
const ImageGenerationRequestSchema = z.object({
  prompt: z.string().optional(),
  keyword: z.string().optional(),
  title: z.string().optional(),
  category: z.enum(['industry', 'research', 'events', 'frontier', 'insight', 'misc']).optional(),
  dimensions: z.object({
    width: z.number().min(256).max(2048).default(1200),
    height: z.number().min(256).max(2048).default(675)
  }).optional(),
  orientation: z.enum(['landscape', 'portrait', 'square']).default('landscape'),
  style: z.string().optional(),
  provider: z.enum(['workers']).optional() // 仅支持Worker提供商
});

type ImageGenerationRequest = z.infer<typeof ImageGenerationRequestSchema>;

const imageRoutes: FastifyPluginAsync = async (fastify) => {
  // 初始化图片服务
  const imageService = createImageService((level, message, data) => {
    try {
      const logMethod = fastify.log[level as keyof typeof fastify.log];
      if (typeof logMethod === 'function') {
        (logMethod as any)(data || {}, message);
      } else {
        console.log(`[${level.toUpperCase()}] ${message}`, data || '');
      }
    } catch (error) {
      console.log(`[${level.toUpperCase()}] ${message}`, data || '');
    }
  });

  // 生成图片接口
  fastify.post<{ Body: ImageGenerationRequest }>('/generate', {
    schema: {
      description: 'Generate an image using the configured image providers with fallback chain',
      tags: ['image'],
      body: {
        type: 'object',
        properties: {
          prompt: { type: 'string', description: 'Image prompt description' },
          keyword: { type: 'string', description: 'Keyword for image search' },
          title: { type: 'string', description: 'Article title for context' },
          category: { 
            type: 'string', 
            enum: ['industry', 'research', 'events', 'frontier', 'insight', 'misc'],
            description: 'Content category for style adaptation'
          },
          dimensions: {
            type: 'object',
            properties: {
              width: { type: 'number', minimum: 256, maximum: 2048, default: 1200 },
              height: { type: 'number', minimum: 256, maximum: 2048, default: 675 }
            }
          },
          orientation: { 
            type: 'string', 
            enum: ['landscape', 'portrait', 'square'],
            default: 'landscape'
          },
          style: { type: 'string', description: 'Additional style instructions' },
          provider: { 
            type: 'string', 
            enum: ['workers', 'fal', 'unsplash'],
            description: 'Force use of specific provider (bypasses fallback chain)'
          }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            url: { type: 'string' },
            provider: { type: 'string' },
            metadata: { type: 'object' },
            processingTime: { type: 'number' }
          }
        },
        400: {
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' }
          }
        },
        500: {
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const startTime = Date.now();
      
      // 验证输入
      const validatedInput = ImageGenerationRequestSchema.parse(request.body);
      
      // 检查是否至少提供了一个输入
      if (!validatedInput.prompt && !validatedInput.keyword && !validatedInput.title) {
        return reply.status(400).send({
          error: 'INVALID_REQUEST',
          message: 'At least one of prompt, keyword, or title must be provided'
        });
      }

      fastify.log.info('Image generation request received');

      // 如果指定了特定提供商，需要修改服务配置（这里简化处理）
      const result = await imageService.generateImage({
        prompt: validatedInput.prompt,
        keyword: validatedInput.keyword,
        title: validatedInput.title,
        category: validatedInput.category,
        dimensions: validatedInput.dimensions ? {
          width: validatedInput.dimensions.width ?? 1200,
          height: validatedInput.dimensions.height ?? 675
        } : { width: 1200, height: 675 },
        orientation: validatedInput.orientation,
        style: validatedInput.style
      });

      const processingTime = Date.now() - startTime;

      if (result.success) {
        fastify.log.info('Image generated successfully');

        reply.send({
          success: true,
          url: result.url,
          provider: result.provider,
          metadata: result.metadata,
          processingTime
        });
      } else {
        fastify.log.warn('Image generation failed');

        reply.status(500).send({
          error: 'GENERATION_FAILED',
          message: result.error || 'Unknown error occurred'
        });
      }
    } catch (error) {
      fastify.log.error(`Image generation error: ${error instanceof Error ? error.message : String(error)}`);
      
      if (error instanceof z.ZodError) {
        reply.status(400).send({
          error: 'VALIDATION_ERROR',
          message: error.errors.map(e => `${e.path.join('.')}: ${e.message}`).join(', ')
        });
      } else {
        reply.status(500).send({
          error: 'INTERNAL_ERROR',
          message: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
  });

  // 健康检查接口
  fastify.get('/health', {
    schema: {
      description: 'Check the health status of all image providers',
      tags: ['image'],
      response: {
        200: {
          type: 'object',
          properties: {
            providers: { type: 'object' },
            stats: { type: 'object' },
            overallHealth: { type: 'boolean' }
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      fastify.log.info('Performing image service health check');
      
      const healthStatus = await imageService.healthCheck();
      
      fastify.log.info('Health check completed');
      
      // 确保返回正确的格式
      const response = {
        providers: healthStatus.providers || {},
        stats: healthStatus.stats || {},
        overallHealth: healthStatus.overallHealth !== undefined ? healthStatus.overallHealth : false
      };
      
      reply.send(response);
    } catch (error) {
      fastify.log.error(`Image service health check failed: ${error instanceof Error ? error.message : String(error)}`);
      reply.status(500).send({
        error: 'HEALTH_CHECK_FAILED',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // 统计信息接口
  fastify.get('/stats', {
    schema: {
      description: 'Get image service usage statistics',
      tags: ['image'],
      response: {
        200: {
          type: 'object',
          properties: {
            totalRequests: { type: 'number' },
            successfulRequests: { type: 'number' },
            failedRequests: { type: 'number' },
            providerStats: { type: 'object' }
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const stats = imageService.getStats();
      reply.send(stats);
    } catch (error) {
      fastify.log.error(`Failed to get image service stats: ${error instanceof Error ? error.message : String(error)}`);
      reply.status(500).send({
        error: 'STATS_ERROR',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });
};

export default imageRoutes;