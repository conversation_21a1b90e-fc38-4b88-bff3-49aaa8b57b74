import { configRepository } from '../database/repositories.js';
import type { ConfigKey, ConfigValue } from '@news-site/shared';
import { logger } from '../../utils/logger.js';

export interface AppConfig {
  openai: {
    apiKey: string;
    model: string;
    maxTokens: number;
  };
  content: {
    outputDir: string;
    defaultCategory: string;
    minWordCount: number;
    maxWordCount: number;
  };
  scheduler: {
    enabled: boolean;
    interval: number; // in hours
    maxTasksPerRun: number;
  };
  image: {
    enabled: boolean;
    provider: 'openai' | 'unsplash';
    unsplashApiKey?: string;
  };
  seo: {
    minKeywordDensity: number;
    maxKeywordDensity: number;
    targetWordCount: number;
  };
}

export class ConfigService {
  private cache: Map<string, any> = new Map();
  private cacheExpiry: Map<string, number> = new Map();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  constructor() {
    // Initialize with defaults
    this.loadDefaults();
  }

  /**
   * 获取完整的应用配置
   */
  async getAppConfig(): Promise<AppConfig> {
    const config: AppConfig = {
      openai: {
        apiKey: await this.get('openai.apiKey', process.env.OPENAI_API_KEY || ''),
        model: await this.get('openai.model', 'gpt-4o-mini'),
        maxTokens: await this.get('openai.maxTokens', 4000)
      },
      content: {
        outputDir: await this.get('content.outputDir', './src/content/news'),
        defaultCategory: await this.get('content.defaultCategory', 'misc'),
        minWordCount: await this.get('content.minWordCount', 600),
        maxWordCount: await this.get('content.maxWordCount', 1200)
      },
      scheduler: {
        enabled: await this.get('scheduler.enabled', true),
        interval: await this.get('scheduler.interval', 24), // 24 hours
        maxTasksPerRun: await this.get('scheduler.maxTasksPerRun', 5)
      },
      image: {
        enabled: await this.get('image.enabled', true),
        provider: await this.get('image.provider', 'unsplash'),
        unsplashApiKey: await this.get('image.unsplashApiKey', process.env.UNSPLASH_API_KEY)
      },
      seo: {
        minKeywordDensity: await this.get('seo.minKeywordDensity', 1.0),
        maxKeywordDensity: await this.get('seo.maxKeywordDensity', 3.0),
        targetWordCount: await this.get('seo.targetWordCount', 800)
      }
    };

    return config;
  }

  /**
   * 获取单个配置值
   */
  async get<T = any>(key: ConfigKey, defaultValue?: T): Promise<T> {
    // Check cache first
    const cacheKey = key;
    const cached = this.cache.get(cacheKey);
    const expiry = this.cacheExpiry.get(cacheKey);
    
    if (cached !== undefined && expiry && expiry > Date.now()) {
      return cached;
    }

    try {
      const config = await configRepository.get(key);
      const value = config ? this.parseValue(config.value) : defaultValue;
      
      // Update cache
      this.cache.set(cacheKey, value);
      this.cacheExpiry.set(cacheKey, Date.now() + this.CACHE_TTL);
      
      return value;
    } catch (error) {
      logger.error(`Failed to get config for key: ${key}`, error);
      return defaultValue as T;
    }
  }

  /**
   * 设置配置值
   */
  async set(key: ConfigKey, value: ConfigValue): Promise<void> {
    try {
      const serializedValue = this.serializeValue(value);
      
      await configRepository.set(key, serializedValue, this.getConfigDescription(key));

      // Update cache
      this.cache.set(key, value);
      this.cacheExpiry.set(key, Date.now() + this.CACHE_TTL);
      
      logger.info(`Config updated: ${key} = ${serializedValue}`);
    } catch (error) {
      logger.error(`Failed to set config for key: ${key}`, error);
      throw error;
    }
  }

  /**
   * 获取所有配置
   */
  async getAll(): Promise<Record<string, any>> {
    try {
      const configs = await configRepository.getAll();
      const result: Record<string, any> = {};
      
      for (const config of configs) {
        result[config.key] = this.parseValue(config.value);
      }
      
      return result;
    } catch (error) {
      logger.error('Failed to get all configs', error);
      throw error;
    }
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.cache.clear();
    this.cacheExpiry.clear();
    logger.info('Config cache cleared');
  }

  private async loadDefaults(): Promise<void> {
    // Set basic defaults that don't require external APIs
    const basicDefaults: Partial<Record<ConfigKey, ConfigValue>> = {
      'openai.model': 'gpt-4o-mini',
      'openai.maxTokens': 4000,
      'content.outputDir': './src/content/news',
      'content.defaultCategory': 'misc',
      'content.minWordCount': 600,
      'content.maxWordCount': 1200,
      'scheduler.enabled': true,
      'scheduler.interval': 24,
      'scheduler.maxTasksPerRun': 5,
      'image.enabled': true,
      'image.provider': 'unsplash',
      'seo.minKeywordDensity': 1.0,
      'seo.maxKeywordDensity': 3.0,
      'seo.targetWordCount': 800
    };

    // Only set defaults if they don't exist
    for (const [key, value] of Object.entries(basicDefaults)) {
      try {
        const existing = await configRepository.get(key as ConfigKey);
        if (!existing) {
          await this.set(key as ConfigKey, value!);
        }
      } catch (error) {
        logger.warn(`Failed to set default for ${key}:`, error);
      }
    }
  }

  private parseValue(value: string): any {
    if (value === 'true') return true;
    if (value === 'false') return false;
    if (value === 'null') return null;
    if (value === 'undefined') return undefined;
    
    // Try to parse as number
    const num = Number(value);
    if (!isNaN(num) && isFinite(num)) {
      return num;
    }
    
    // Try to parse as JSON
    try {
      return JSON.parse(value);
    } catch {
      // Return as string
      return value;
    }
  }

  private serializeValue(value: any): string {
    if (typeof value === 'string') {
      return value;
    }
    return JSON.stringify(value);
  }

  private getConfigDescription(key: ConfigKey): string {
    const descriptions: Record<string, string> = {
      'openai.apiKey': 'OpenAI API key for content generation',
      'openai.model': 'OpenAI model to use for content generation',
      'openai.maxTokens': 'Maximum tokens for OpenAI API calls',
      'content.outputDir': 'Directory to save generated content',
      'content.defaultCategory': 'Default category for new articles',
      'content.minWordCount': 'Minimum word count for articles',
      'content.maxWordCount': 'Maximum word count for articles',
      'scheduler.enabled': 'Enable automatic task scheduling',
      'scheduler.interval': 'Hours between scheduler runs',
      'scheduler.maxTasksPerRun': 'Maximum tasks to process per run',
      'image.enabled': 'Enable automatic image generation',
      'image.provider': 'Image generation provider (openai|unsplash)',
      'image.unsplashApiKey': 'Unsplash API key for image search',
      'seo.minKeywordDensity': 'Minimum keyword density percentage',
      'seo.maxKeywordDensity': 'Maximum keyword density percentage',
      'seo.targetWordCount': 'Target word count for SEO optimization'
    };

    return descriptions[key] || 'Application configuration value';
  }
}

export const configService = new ConfigService(); 