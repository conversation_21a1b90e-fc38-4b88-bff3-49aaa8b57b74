---
import { getCollection } from 'astro:content';
import BaseLayout from '../layouts/BaseLayout.astro';
import Header from '../components/Header.astro';
import Footer from '../components/Footer.astro';
import RelatedProducts from '../components/RelatedProducts.astro';

const allPosts = await getCollection('news', ({ data }) => {
  return data.draft !== true;
});

const featuredPosts = allPosts.filter(post => post.data.featured).slice(0, 3);
const recentPosts = allPosts
  .sort((a, b) => new Date(b.data.date).getTime() - new Date(a.data.date).getTime())
  .slice(0, 8);

const categories = [
  {
    name: 'Industry',
    id: 'industry',
    description: 'Latest industry news and market updates',
    icon: '🏭',
    gradient: 'from-blue-500 to-blue-700',
    posts: allPosts.filter(post => post.data.category === 'industry').length
  },
  {
    name: 'Research',
    id: 'research',
    description: 'Scientific research and development',
    icon: '🔬',
    gradient: 'from-green-500 to-green-700',
    posts: allPosts.filter(post => post.data.category === 'research').length
  },
  {
    name: 'Events',
    id: 'events',
    description: 'Industry events and conferences',
    icon: '📅',
    gradient: 'from-purple-500 to-purple-700',
    posts: allPosts.filter(post => post.data.category === 'events').length
  },
  {
    name: 'Frontier',
    id: 'frontier',
    description: 'Cutting-edge technology and innovation',
    icon: '🚀',
    gradient: 'from-red-500 to-red-700',
    posts: allPosts.filter(post => post.data.category === 'frontier').length
  },
  {
    name: 'Insight',
    id: 'insight',
    description: 'Expert analysis and commentary',
    icon: '💡',
    gradient: 'from-yellow-500 to-yellow-700',
    posts: allPosts.filter(post => post.data.category === 'insight').length
  },
  {
    name: 'Misc',
    id: 'misc',
    description: 'Other news and updates',
    icon: '📰',
    gradient: 'from-gray-500 to-gray-700',
    posts: allPosts.filter(post => post.data.category === 'misc').length
  }
];
---

<BaseLayout title="Aerospace & Defense News - Latest Industry Updates">
  <Header />
  
  <!-- Hero Section -->
  <section class="relative bg-hero-gradient overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 bg-grid-white/[0.05] bg-[size:40px_40px]"></div>
    <div class="absolute inset-0 bg-gradient-to-t from-primary-950/20 to-transparent"></div>
    
    <div class="relative container-page section-spacing">
      <div class="text-center max-w-5xl mx-auto animate-fade-in">
        <h1 class="heading-xl text-white mb-6">
          Aerospace & Defense
          <span class="block text-gradient bg-gradient-to-r from-accent-400 to-accent-200 bg-clip-text text-transparent">
            News Hub
          </span>
        </h1>
        <p class="body-lg text-primary-100 mb-10 max-w-3xl mx-auto">
          Your trusted source for the latest developments in aerospace, space exploration, 
          and defense technologies. Stay ahead with comprehensive industry coverage and expert analysis.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <a href="/category/industry" class="btn-primary group">
            Latest Industry News
            <svg class="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
          </a>
          <a href="/search" class="btn-outline border-primary-300 text-primary-100 hover:bg-primary-100 hover:text-primary-900">
            Search Articles
          </a>
        </div>
      </div>
    </div>
    
    <!-- Scroll indicator -->
    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce-soft">
      <svg class="w-6 h-6 text-primary-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
      </svg>
    </div>
  </section>

  <!-- Featured Articles -->
  {featuredPosts.length > 0 && (
    <section class="bg-white section-spacing">
      <div class="container-page">
        <div class="text-center mb-12">
          <h2 class="heading-lg text-secondary-900 mb-4">Featured Stories</h2>
          <p class="body-lg text-secondary-600 max-w-2xl mx-auto">
            Don't miss these important developments shaping the aerospace and defense industry
          </p>
        </div>
        
        <div class="grid-cards">
          {featuredPosts.map((post, index) => (
            <article class="card-interactive group animate-fade-in" style={`animation-delay: ${index * 150}ms`}>
              {post.data.heroImage && (
                <div class="aspect-ratio-16-9 overflow-hidden rounded-t-xl">
                  <img 
                    src={post.data.heroImage}
                    alt={post.data.title}
                    class="object-cover group-hover:scale-105 transition-transform duration-500"
                    loading="lazy"
                  />
                </div>
              )}
              <div class="p-6">
                <div class="flex items-center mb-4">
                  <span class="badge badge-primary">
                    {post.data.category}
                  </span>
                  <time class="text-sm text-secondary-500 ml-3" datetime={new Date(post.data.date).toISOString()}>
                    {new Date(post.data.date).toLocaleDateString('en-US', { 
                      year: 'numeric', 
                      month: 'long', 
                      day: 'numeric' 
                    })}
                  </time>
                </div>
                <h3 class="text-xl font-bold text-secondary-900 mb-3 line-clamp-2 group-hover:text-primary-600 transition-colors">
                  <a href={`/news/${post.id}`} class="focus-outline">
                    {post.data.title}
                  </a>
                </h3>
                {post.data.description && (
                  <p class="text-secondary-600 mb-4 line-clamp-3">
                    {post.data.description}
                  </p>
                )}
                <a 
                  href={`/news/${post.id}`}
                  class="inline-flex items-center text-primary-600 hover:text-primary-700 font-semibold transition-colors group"
                >
                  Read full story
                  <svg class="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                  </svg>
                </a>
              </div>
            </article>
          ))}
        </div>
      </div>
    </section>
  )}

  <!-- Categories Grid -->
  <section class="bg-secondary-50 section-spacing">
    <div class="container-page">
      <div class="text-center mb-12">
        <h2 class="heading-lg text-secondary-900 mb-4">Explore by Category</h2>
        <p class="body-lg text-secondary-600 max-w-2xl mx-auto">
          Discover news and insights across different sectors of the aerospace and defense industry
        </p>
      </div>
      
      <div class="grid-features">
        {categories.map((category, index) => (
          <a 
            href={`/category/${category.id}`}
            class="group card-interactive animate-fade-in"
            style={`animation-delay: ${index * 100}ms`}
          >
            <div class="p-8 text-center">
              <div class={`w-16 h-16 mx-auto mb-4 bg-gradient-to-br ${category.gradient} rounded-2xl flex items-center justify-center text-white text-3xl shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110`}>
                {category.icon}
              </div>
              <h3 class="text-xl font-bold text-secondary-900 mb-2 group-hover:text-primary-600 transition-colors">
                {category.name}
              </h3>
              <p class="text-secondary-600 text-sm mb-3">
                {category.description}
              </p>
              <div class="flex items-center justify-center text-xs text-secondary-500">
                <span class="bg-secondary-100 px-2 py-1 rounded-full">
                  {category.posts} articles
                </span>
              </div>
            </div>
          </a>
        ))}
      </div>
    </div>
  </section>

  <!-- Recent Articles -->
  <section class="bg-white section-spacing">
    <div class="container-page">
      <div class="text-center mb-12">
        <h2 class="heading-lg text-secondary-900 mb-4">Latest Articles</h2>
        <p class="body-lg text-secondary-600 max-w-2xl mx-auto">
          Stay updated with the newest developments and breaking news
        </p>
      </div>
      
      <div class="grid-cards">
        {recentPosts.map((post, index) => (
          <article class="group card animate-fade-in" style={`animation-delay: ${index * 100}ms`}>
            {post.data.heroImage && (
              <div class="aspect-ratio-16-9 overflow-hidden rounded-t-xl">
                <img 
                  src={post.data.heroImage}
                  alt={post.data.title}
                  class="object-cover group-hover:scale-105 transition-transform duration-300"
                  loading="lazy"
                />
              </div>
            )}
            <div class="p-6">
              <div class="flex items-center mb-3">
                <span class="badge badge-secondary">
                  {post.data.category}
                </span>
                <time class="text-sm text-secondary-500 ml-3" datetime={new Date(post.data.date).toISOString()}>
                  {new Date(post.data.date).toLocaleDateString()}
                </time>
              </div>
              <h3 class="text-lg font-bold text-secondary-900 mb-2 line-clamp-2 group-hover:text-primary-600 transition-colors">
                <a href={`/news/${post.id}`} class="focus-outline">
                  {post.data.title}
                </a>
              </h3>
              {post.data.description && (
                <p class="text-secondary-600 text-sm mb-4 line-clamp-2">
                  {post.data.description}
                </p>
              )}
              <a 
                href={`/news/${post.id}`}
                class="inline-flex items-center text-primary-600 hover:text-primary-700 font-medium text-sm transition-colors group"
              >
                Read more
                <svg class="w-3 h-3 ml-1 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
              </a>
            </div>
          </article>
        ))}
      </div>
      
      <div class="text-center mt-12">
        <a href="/archive" class="btn-primary">
          View All Articles
        </a>
      </div>
    </div>
  </section>

  <!-- Newsletter Signup -->
  <section class="bg-gradient-to-br from-primary-900 via-primary-800 to-primary-700 section-spacing relative overflow-hidden">
    <!-- Background elements -->
    <div class="absolute inset-0 bg-grid-white/[0.05] bg-[size:20px_20px]"></div>
    <div class="absolute -top-24 -right-24 w-96 h-96 bg-primary-600 rounded-full mix-blend-multiply filter blur-xl opacity-70"></div>
    <div class="absolute -bottom-24 -left-24 w-96 h-96 bg-accent-500 rounded-full mix-blend-multiply filter blur-xl opacity-70"></div>
    
    <div class="container-narrow relative">
      <div class="text-center">
        <h2 class="heading-lg text-white mb-4">Stay Informed</h2>
        <p class="body-lg text-primary-100 mb-8 max-w-2xl mx-auto">
          Get the latest aerospace and defense news delivered to your inbox. 
          Join thousands of industry professionals staying ahead of the curve.
        </p>
        <div class="max-w-md mx-auto">
          <form class="flex flex-col sm:flex-row gap-4" data-netlify="true">
            <input 
              type="email" 
              name="email"
              placeholder="Enter your email address"
              class="flex-1 px-4 py-3 rounded-lg border-0 text-secondary-900 placeholder-secondary-500 focus:outline-none focus:ring-2 focus:ring-accent-500 shadow-soft"
              required
            />
            <button 
              type="submit"
              class="bg-accent-500 hover:bg-accent-600 text-white font-semibold px-6 py-3 rounded-lg transition-all duration-200 shadow-soft hover:shadow-medium whitespace-nowrap"
            >
              Subscribe
            </button>
          </form>
          <p class="text-sm text-primary-200 mt-4">
            No spam. Unsubscribe at any time. Privacy policy applies.
          </p>
        </div>
      </div>
    </div>
  </section>

  <Footer />

  <!-- Related Products (mobile bottom position) -->
  <RelatedProducts 
    products={['hypersonic-systems', 'satellite-communication', 'radar-systems']}
    position="bottom"
  />
</BaseLayout>

<style>
  /* Grid background pattern */
  .bg-grid-white\/\[0\.05\] {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' width='32' height='32' fill='none' stroke='rgb(255 255 255 / 0.05)'%3e%3cpath d='m0 .5h32m-32 32v-32'/%3e%3c/svg%3e");
  }
  
  /* Animation delays for staggered entrance */
  .animate-fade-in {
    animation: fadeIn 0.6s ease-out both;
  }
  
  @media (prefers-reduced-motion: reduce) {
    .animate-fade-in {
      animation: none;
    }
  }
</style> 