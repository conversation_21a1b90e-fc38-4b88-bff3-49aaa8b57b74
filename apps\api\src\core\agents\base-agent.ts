import { z } from 'zod';
import { logRepository } from '../database/repositories.js';

// Agent配置schema
export const AgentConfigSchema = z.object({
  name: z.string(),
  description: z.string(),
  version: z.string().default('1.0.0'),
  maxRetries: z.number().default(3),
  timeout: z.number().default(30000), // 30 seconds
  apiKeys: z.record(z.string()).optional()
});

// Agent执行上下文
export const AgentContextSchema = z.object({
  requestId: z.string(),
  userId: z.string().optional(),
  metadata: z.record(z.any()).default({}),
  dryRun: z.boolean().default(false)
});

// Agent执行结果
export const AgentResultSchema = z.object({
  success: z.boolean(),
  data: z.any().optional(),
  error: z.string().optional(),
  warnings: z.array(z.string()).default([]),
  metadata: z.record(z.any()).default({}),
  executionTime: z.number()
});

export type AgentConfig = z.infer<typeof AgentConfigSchema>;
export type AgentContext = z.infer<typeof AgentContextSchema>;
export type AgentResult = z.infer<typeof AgentResultSchema>;

/**
 * 基础Agent抽象类
 * 所有AI Agent都应该继承此类
 */
export abstract class BaseAgent {
  protected config: AgentConfig;
  protected isInitialized = false;

  constructor(config: AgentConfig) {
    this.config = AgentConfigSchema.parse(config);
  }

  /**
   * 初始化Agent
   */
  async initialize(): Promise<void> {
    try {
      await this.onInitialize();
      this.isInitialized = true;
      await this.log('info', `Agent ${this.config.name} initialized successfully`);
    } catch (error) {
      await this.log('error', `Agent ${this.config.name} initialization failed`, { error });
      throw error;
    }
  }

  /**
   * 执行Agent任务
   */
  async execute<T = any>(input: T, context: AgentContext): Promise<AgentResult> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const startTime = Date.now();
    const parsedContext = AgentContextSchema.parse(context);

    try {
      await this.log('info', `Agent ${this.config.name} execution started`, { 
        requestId: parsedContext.requestId,
        input: this.sanitizeLogData(input)
      });

      // 验证输入
      const validatedInput = await this.validateInput(input);
      
      // 执行具体逻辑
      const result = await this.onExecute(validatedInput, parsedContext);
      
      const executionTime = Date.now() - startTime;
      
      const finalResult: AgentResult = {
        success: true,
        data: result,
        warnings: [],
        executionTime,
        metadata: {
          agentName: this.config.name,
          agentVersion: this.config.version,
          ...parsedContext.metadata
        }
      };

      await this.log('info', `Agent ${this.config.name} execution completed`, {
        requestId: parsedContext.requestId,
        executionTime,
        success: true
      });

      return finalResult;

    } catch (error) {
      const executionTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      await this.log('error', `Agent ${this.config.name} execution failed`, {
        requestId: parsedContext.requestId,
        error: errorMessage,
        executionTime
      });

      return {
        success: false,
        error: errorMessage,
        warnings: [],
        executionTime,
        metadata: {
          agentName: this.config.name,
          agentVersion: this.config.version,
          ...parsedContext.metadata
        }
      };
    }
  }

  /**
   * 执行重试逻辑
   */
  async executeWithRetry<T = any>(input: T, context: AgentContext): Promise<AgentResult> {
    let lastError: Error | undefined;
    
    for (let attempt = 1; attempt <= this.config.maxRetries; attempt++) {
      try {
        const result = await this.execute(input, {
          ...context,
          metadata: {
            ...context.metadata,
            attempt,
            maxRetries: this.config.maxRetries
          }
        });

        if (result.success) {
          return result;
        }

        // 如果不是最后一次尝试，等待后重试
        if (attempt < this.config.maxRetries) {
          const delay = this.calculateRetryDelay(attempt);
          await this.log('warn', `Agent ${this.config.name} attempt ${attempt} failed, retrying in ${delay}ms`, {
            attempt,
            error: result.error
          });
          await new Promise(resolve => setTimeout(resolve, delay));
        }

        lastError = new Error(result.error || 'Agent execution failed');
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
        
        if (attempt < this.config.maxRetries) {
          const delay = this.calculateRetryDelay(attempt);
          await this.log('warn', `Agent ${this.config.name} attempt ${attempt} failed with exception, retrying in ${delay}ms`, {
            attempt,
            error: lastError.message
          });
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    // 所有重试都失败了
    await this.log('error', `Agent ${this.config.name} failed after ${this.config.maxRetries} attempts`, {
      error: lastError?.message
    });

    return {
      success: false,
      error: lastError?.message || 'All retry attempts failed',
      warnings: [],
      executionTime: 0,
      metadata: {
        agentName: this.config.name,
        agentVersion: this.config.version,
        attempts: this.config.maxRetries
      }
    };
  }

  /**
   * 获取Agent信息
   */
  getInfo(): AgentConfig {
    return { ...this.config };
  }

  /**
   * 检查Agent健康状态
   */
  async healthCheck(): Promise<{ healthy: boolean; details: Record<string, any> }> {
    try {
      const details = await this.onHealthCheck();
      return {
        healthy: true,
        details: {
          name: this.config.name,
          version: this.config.version,
          initialized: this.isInitialized,
          ...details
        }
      };
    } catch (error) {
      return {
        healthy: false,
        details: {
          name: this.config.name,
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      };
    }
  }

  // 抽象方法 - 子类必须实现
  protected abstract onInitialize(): Promise<void>;
  protected abstract onExecute<T = any>(input: T, context: AgentContext): Promise<any>;
  protected abstract validateInput<T = any>(input: T): Promise<T>;
  
  // 可选覆盖的方法
  protected async onHealthCheck(): Promise<Record<string, any>> {
    return {};
  }

  // 工具方法
  protected calculateRetryDelay(attempt: number): number {
    // 指数退避: 1s, 2s, 4s, 8s...
    return Math.min(1000 * Math.pow(2, attempt - 1), 10000);
  }

  protected sanitizeLogData(data: any): any {
    // 移除敏感信息的简单实现
    if (typeof data !== 'object' || data === null) {
      return data;
    }

    const sanitized = { ...data };
    const sensitiveKeys = ['password', 'apiKey', 'token', 'secret', 'key'];
    
    for (const key of sensitiveKeys) {
      if (key in sanitized) {
        sanitized[key] = '[REDACTED]';
      }
    }

    return sanitized;
  }

  protected async log(level: 'debug' | 'info' | 'warn' | 'error', message: string, data?: any): Promise<void> {
    try {
      await logRepository.add(level, message, data, `agent:${this.config.name}`);
    } catch (error) {
      // 记录日志失败不应该中断主流程
      console.error('Failed to log message:', error);
    }
  }
} 