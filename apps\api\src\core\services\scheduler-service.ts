import cron from 'node-cron';
import { planService } from './plan-service.js';
import { deploymentTool } from '../tools/deployment-tool.js';
import { logger } from '../../utils/logger.js';
import { automationConfigService } from './automation-config-service.js';

export interface ScheduleConfig {
  enabled: boolean;
  cronExpression: string;
  maxTasks: number;
  categories: string[];
  autoDeploy: boolean;
  deployEnvironment: 'production' | 'preview';
  timezone: string;
}

export type AutomationMode = 'scheduler' | 'n8n';

export interface AutomationConfig {
  mode: AutomationMode;
  scheduler: ScheduleConfig;
}

export interface ScheduleJob {
  id: string;
  name: string;
  config: ScheduleConfig;
  task: cron.ScheduledTask | null;
  lastRun?: Date;
  nextRun?: Date;
  status: 'running' | 'stopped' | 'error';
  lastResult?: any;
}

export class SchedulerService {
  private jobs: Map<string, ScheduleJob> = new Map();
  private defaultConfig: ScheduleConfig = {
    enabled: false,
    cronExpression: '0 9 * * *', // 每天上午9点
    maxTasks: 3,
    categories: ['industry', 'research', 'frontier'],
    autoDeploy: true,
    deployEnvironment: 'production',
    timezone: 'Asia/Shanghai'
  };

  constructor() {
    this.loadConfigFromEnv();
  }

  private loadConfigFromEnv(): void {
    // 检查自动化模式
    if (!automationConfigService.isSchedulerMode()) {
      logger.info(`Automation mode set to ${automationConfigService.getMode()} - scheduler disabled`);
      return;
    }

    const config: ScheduleConfig = {
      enabled: automationConfigService.isSchedulerEnabled(),
      cronExpression: process.env.SCHEDULER_CRON || this.defaultConfig.cronExpression,
      maxTasks: parseInt(process.env.SCHEDULER_MAX_TASKS || '3'),
      categories: process.env.SCHEDULER_CATEGORIES?.split(',') || this.defaultConfig.categories,
      autoDeploy: process.env.SCHEDULER_AUTO_DEPLOY !== 'false',
      deployEnvironment: (process.env.SCHEDULER_DEPLOY_ENV as 'production' | 'preview') || 'production',
      timezone: process.env.SCHEDULER_TIMEZONE || this.defaultConfig.timezone
    };

    if (config.enabled) {
      this.createJob('daily-content-generation', config);
      logger.info('Scheduler loaded from environment', { mode: 'scheduler', config });
    }
  }

  createJob(id: string, config: ScheduleConfig): ScheduleJob {
    // 如果任务已存在，先停止它
    if (this.jobs.has(id)) {
      this.stopJob(id);
    }

    const job: ScheduleJob = {
      id,
      name: `Content Generation Job - ${id}`,
      config,
      task: null,
      status: 'stopped'
    };

    if (config.enabled) {
      try {
        job.task = cron.schedule(config.cronExpression, async () => {
          await this.executeJob(job);
        }, {
          scheduled: false,
          timezone: config.timezone
        });

        job.status = 'running';
        job.nextRun = this.getNextRunTime(config.cronExpression, config.timezone);
        
        // 启动任务
        job.task.start();
        
        logger.info(`Scheduled job created: ${id}`, {
          cronExpression: config.cronExpression,
          timezone: config.timezone,
          nextRun: job.nextRun
        });
      } catch (error) {
        job.status = 'error';
        logger.error(`Failed to create scheduled job: ${id}`, { error });
      }
    }

    this.jobs.set(id, job);
    return job;
  }

  private async executeJob(job: ScheduleJob): Promise<void> {
    const startTime = Date.now();
    job.lastRun = new Date();

    try {
      logger.info(`Executing scheduled job: ${job.id}`, {
        config: job.config,
        lastRun: job.lastRun
      });

      const date = new Date().toISOString().split('T')[0];

      // 1. 创建并执行计划
      const plan = await planService.createPlan({
        date,
        maxTasks: job.config.maxTasks,
        categories: job.config.categories,
        forceRegenerate: true
      });

      logger.info(`Scheduled job created plan with ${plan.tasks.length} tasks`, {
        jobId: job.id,
        planId: plan.id
      });

      const executionResults = await planService.executePlan(plan.id, 2);
      
      logger.info('Scheduled job plan execution completed', {
        jobId: job.id,
        completed: executionResults.completed,
        failed: executionResults.failed
      });

      let deploymentResult = null;

      // 2. 如果有成功的任务且启用自动部署
      if (job.config.autoDeploy && executionResults.completed > 0) {
        logger.info('Starting scheduled deployment...', { jobId: job.id });
        
        deploymentResult = await deploymentTool.deploy();
        
        if (deploymentResult.success) {
          logger.info('Scheduled deployment completed', {
            jobId: job.id,
            url: deploymentResult.deploymentUrl,
            buildTime: deploymentResult.buildTime,
            deployTime: deploymentResult.deployTime
          });
        } else {
          logger.error('Scheduled deployment failed', {
            jobId: job.id,
            error: deploymentResult.error
          });
        }
      }

      // 保存执行结果
      job.lastResult = {
        success: true,
        executionTime: Date.now() - startTime,
        plan,
        execution: executionResults,
        deployment: deploymentResult
      };

      // 更新下次运行时间
      job.nextRun = this.getNextRunTime(job.config.cronExpression, job.config.timezone);

      logger.info(`Scheduled job completed successfully: ${job.id}`, {
        executionTime: job.lastResult.executionTime,
        nextRun: job.nextRun
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      job.lastResult = {
        success: false,
        executionTime: Date.now() - startTime,
        error: errorMessage
      };

      job.status = 'error';
      
      logger.error(`Scheduled job failed: ${job.id}`, {
        error: errorMessage,
        executionTime: job.lastResult.executionTime
      });
    }
  }

  stopJob(id: string): boolean {
    const job = this.jobs.get(id);
    if (!job) {
      return false;
    }

    if (job.task) {
      job.task.stop();
      job.task = null;
    }

    job.status = 'stopped';
    job.nextRun = undefined;

    logger.info(`Scheduled job stopped: ${id}`);
    return true;
  }

  startJob(id: string): boolean {
    const job = this.jobs.get(id);
    if (!job || !job.config.enabled) {
      return false;
    }

    if (job.task) {
      job.task.start();
      job.status = 'running';
      job.nextRun = this.getNextRunTime(job.config.cronExpression, job.config.timezone);
      
      logger.info(`Scheduled job started: ${id}`, { nextRun: job.nextRun });
      return true;
    }

    return false;
  }

  updateJob(id: string, config: Partial<ScheduleConfig>): ScheduleJob | null {
    const job = this.jobs.get(id);
    if (!job) {
      return null;
    }

    // 合并配置
    const newConfig = { ...job.config, ...config };
    
    // 重新创建任务
    return this.createJob(id, newConfig);
  }

  deleteJob(id: string): boolean {
    const job = this.jobs.get(id);
    if (!job) {
      return false;
    }

    this.stopJob(id);
    this.jobs.delete(id);
    
    logger.info(`Scheduled job deleted: ${id}`);
    return true;
  }

  getJob(id: string): ScheduleJob | null {
    return this.jobs.get(id) || null;
  }

  getAllJobs(): ScheduleJob[] {
    return Array.from(this.jobs.values());
  }

  getJobStatus(): Record<string, any> {
    const jobs = this.getAllJobs();
    
    return {
      totalJobs: jobs.length,
      runningJobs: jobs.filter(j => j.status === 'running').length,
      stoppedJobs: jobs.filter(j => j.status === 'stopped').length,
      errorJobs: jobs.filter(j => j.status === 'error').length,
      jobs: jobs.map(job => ({
        id: job.id,
        name: job.name,
        status: job.status,
        lastRun: job.lastRun,
        nextRun: job.nextRun,
        config: job.config,
        lastResult: job.lastResult ? {
          success: job.lastResult.success,
          executionTime: job.lastResult.executionTime,
          error: job.lastResult.error
        } : null
      }))
    };
  }

  private getNextRunTime(cronExpression: string, timezone: string): Date | undefined {
    try {
      // 这里简化处理，实际应该使用cron库计算下次运行时间
      const now = new Date();
      const tomorrow = new Date(now);
      tomorrow.setDate(tomorrow.getDate() + 1);
      tomorrow.setHours(9, 0, 0, 0); // 假设是每天9点
      return tomorrow;
    } catch (error) {
      logger.error('Failed to calculate next run time', { error, cronExpression });
      return undefined;
    }
  }

  // 手动触发任务
  async triggerJob(id: string): Promise<any> {
    const job = this.jobs.get(id);
    if (!job) {
      throw new Error(`Job not found: ${id}`);
    }

    logger.info(`Manually triggering job: ${id}`);
    await this.executeJob(job);
    
    return job.lastResult;
  }

  // 停止所有任务
  stopAll(): void {
    for (const [id] of this.jobs) {
      this.stopJob(id);
    }
    logger.info('All scheduled jobs stopped');
  }

  // 启动所有任务
  startAll(): void {
    for (const [id, job] of this.jobs) {
      if (job.config.enabled) {
        this.startJob(id);
      }
    }
    logger.info('All enabled scheduled jobs started');
  }
}

// 创建单例实例
export const schedulerService = new SchedulerService(); 