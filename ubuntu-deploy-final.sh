#!/bin/bash

echo "🚀 Ubuntu 最终部署脚本 v2.0"
echo "============================"

# 检查环境
echo "📋 检查环境..."
node --version
pnpm --version

# 设置错误时退出
set -e

echo "🔧 更新代码..."
git pull origin main

# 清理构建文件
echo "🧹 清理构建文件..."
rm -rf packages/shared/dist
rm -rf apps/api/dist

# 如果没有 node_modules，安装依赖
if [ ! -d "node_modules" ]; then
    echo "📦 安装依赖..."
    pnpm install
fi

# 修复 shared 包的 tsconfig.json
echo "🔧 修复 shared 包配置..."
cd packages/shared

# 确保使用正确的 tsconfig.json
cat > tsconfig.json << 'EOF'
{
  "compilerOptions": {
    "target": "ES2022",
    "module": "ES2022",
    "moduleResolution": "node",
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": false,
    "skipLibCheck": true,
    "outDir": "./dist",
    "rootDir": "./src",
    "declaration": true,
    "declarationMap": true,
    "composite": true,
    "noEmit": false,
    "resolveJsonModule": true
  },
  "include": ["src/**/*"],
  "exclude": ["dist", "node_modules"]
}
EOF

echo "🏗️  构建 shared 包..."
pnpm run build

# 验证 shared 包
if [ ! -f "dist/index.js" ] || [ ! -f "dist/index.d.ts" ]; then
    echo "❌ shared 包构建失败"
    exit 1
fi
echo "✅ shared 包构建成功"

# 检查导出
echo "🔍 验证导出..."
if grep -q "export.*ConfigKey" dist/types/api.d.ts; then
    echo "✅ ConfigKey 导出正常"
else
    echo "❌ ConfigKey 导出缺失"
fi

if grep -q "export.*generateFrontmatter" dist/utils/frontmatter.d.ts; then
    echo "✅ generateFrontmatter 导出正常"
else
    echo "❌ generateFrontmatter 导出缺失"
fi

cd ../..

echo "🏗️  构建 API..."
cd apps/api

# 修复 API 的 tsconfig.json
cat > tsconfig.json << 'EOF'
{
  "compilerOptions": {
    "target": "ES2022",
    "module": "ES2022",
    "moduleResolution": "node",
    "outDir": "./dist",
    "rootDir": "./src",
    "resolveJsonModule": true,
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "strict": false,
    "noImplicitAny": false,
    "allowJs": true,
    "declaration": true,
    "declarationMap": true,
    "sourceMap": false
  },
  "include": ["src/**/*"],
  "exclude": ["dist", "node_modules"],
  "references": [
    {
      "path": "../../packages/shared"
    }
  ]
}
EOF

pnpm run build

# 验证 API
if [ ! -f "dist/index.js" ]; then
    echo "❌ API 构建失败"
    exit 1
fi
echo "✅ API 构建成功"

echo "🎉 部署完成！"
echo ""
echo "启动命令："
echo "export PORT=3002"
echo "pnpm start"
echo ""
echo "或者直接："
echo "PORT=3002 node dist/index.js"
