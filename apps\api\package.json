{"name": "@news-site/api", "version": "0.1.0", "type": "module", "main": "./dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "lint": "eslint src/", "type-check": "tsc --noEmit"}, "dependencies": {"@fastify/cors": "^9.0.1", "@fastify/rate-limit": "^9.1.0", "@fastify/static": "^7.0.1", "@fastify/swagger": "^8.13.0", "@fastify/swagger-ui": "^3.0.0", "@langchain/community": "^0.0.35", "@langchain/openai": "^0.0.19", "@news-site/shared": "workspace:*", "better-sqlite3": "^9.6.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.29.5", "fastify": "^4.24.3", "fastify-plugin": "^5.0.1", "langchain": "^0.1.36", "openai": "^4.103.0", "pino": "^8.17.2", "zod": "^3.22.4", "axios": "^1.6.8", "@mendable/firecrawl-js": "^0.0.36", "newsapi": "^2.4.1", "serpapi": "^2.0.0", "node-cron": "^3.0.3"}, "devDependencies": {"@types/better-sqlite3": "^7.6.13", "@types/node": "^20.11.16", "@types/node-cron": "^3.0.11", "drizzle-kit": "^0.20.18", "pino-pretty": "^13.0.0", "tsx": "^4.7.0", "typescript": "^5.3.3"}}