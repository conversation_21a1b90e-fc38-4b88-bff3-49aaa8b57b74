# 环境变量配置说明

## 立即可用配置

基于您现有的.env配置，您的Unsplash已经可以作为图片服务使用。以下是可选的增强配置：

### 1. 图片服务增强配置 (可选)

```bash
# Fal.ai - AI图片生成服务 (可选，优先级2)
FAL_API_KEY=your_fal_api_key

# Cloudflare Workers - 最高优先级服务 (可选，优先级1)  
CLOUDFLARE_WORKER_URL=https://your-worker.your-subdomain.workers.dev
CLOUDFLARE_API_KEY=your_cloudflare_api_key
CLOUDFLARE_ACCOUNT_ID=your_account_id

# 图片服务基础配置
ENABLE_IMAGE_GENERATION=true
ENABLE_IMAGE_FALLBACK=true
FALLBACK_IMAGE_URL=https://images.unsplash.com/photo-*************-43490279c0fa?w=1200&h=675&fit=crop
```

### 2. 自动化模式配置

```bash
# 自动化模式选择
AUTOMATION_MODE=scheduler  # 'scheduler' (默认) 或 'n8n'

# 内置调度器配置 (当 AUTOMATION_MODE=scheduler 时使用)
SCHEDULER_ENABLED=true
SCHEDULER_CRON=0 9 * * *
SCHEDULER_MAX_TASKS=3
SCHEDULER_CATEGORIES=industry,research,frontier
SCHEDULER_AUTO_DEPLOY=true
SCHEDULER_DEPLOY_ENV=production
SCHEDULER_TIMEZONE=Asia/Shanghai

# n8n编排模式配置 (当 AUTOMATION_MODE=n8n 时使用)
N8N_WEBHOOK_URL=https://your-n8n.domain.com/webhook/content-automation
N8N_API_KEY=your_secure_api_key  # 可选，用于API认证
```

### 3. 内容生成优化

```bash
# 启用图片生成 (已有但确保开启)
ENABLE_IMAGE_GENERATION=true
IMAGE_PROVIDER=unsplash  # 当前可用的兜底服务

# 内容输出目录 (可选，默认为apps/web/src/content/news)
CONTENT_OUTPUT_DIR=./apps/web/src/content/news
```

## 完整的.env文件模板

```bash
# =============================================================================
# 核心配置 (您已有的)
# =============================================================================
OPENAI_API_KEY=sk-proj...
DATABASE_URL=./apps/api/data/database.sqlite
NODE_ENV=development
API_PORT=3002

# =============================================================================
# 图片服务配置 (新增)
# =============================================================================
# Unsplash (您已有)
UNSPLASH_ACCESS_KEY=KNH63...

# Fal.ai AI图片生成
FAL_API_KEY=your_fal_api_key

# Cloudflare Workers 图片服务
CLOUDFLARE_WORKER_URL=https://your-worker.workers.dev
CLOUDFLARE_API_KEY=your_cf_api_key
CLOUDFLARE_ACCOUNT_ID=your_cf_account_id

# 图片服务配置
ENABLE_IMAGE_GENERATION=true
IMAGE_PROVIDER=unsplash  # 使用Unsplash作为可靠的兜底服务
ENABLE_IMAGE_FALLBACK=true

# =============================================================================
# 自动化配置 (新增)
# =============================================================================
# 自动化模式
AUTOMATION_MODE=scheduler

# 内置调度器配置
SCHEDULER_ENABLED=true
SCHEDULER_CRON=0 9 * * *
SCHEDULER_MAX_TASKS=3
SCHEDULER_AUTO_DEPLOY=true
SCHEDULER_DEPLOY_ENV=production

# n8n编排配置 (可选)
N8N_WEBHOOK_URL=https://your-n8n.domain.com/webhook
N8N_API_KEY=your_secure_api_key

# =============================================================================
# 内容抓取配置 (您已有的)
# =============================================================================
SERP_API_KEY=104e08fa2...
FIRECRAWL_API_KEY=fc-...
TAVILY_API_KEY=tvly-...
NEWS_API_KEY=24ffc87b...
```

## 服务优先级说明

### 图片生成服务链式回退机制：
1. **Primary**: Cloudflare Workers (最快，可扩展)
2. **Fallback1**: Fal.ai (AI生成，质量高)  
3. **Fallback2**: Unsplash (照片库，兜底保障)

当第一个服务失败时，自动切换到下一个服务，确保始终能生成配图。

## 获取API密钥说明

### Fal.ai
1. 访问 https://fal.ai/
2. 注册账号并获取API密钥
3. 用于AI图片生成

### Cloudflare Workers (可选)
1. 如果您有Cloudflare账号，可以部署Worker服务
2. Worker可以调用其他图片生成API
3. 提供最快的响应速度

### 现有服务
- **Unsplash**: 您已配置，将作为可靠的兜底图片服务
- **OpenAI**: 您已配置
- **其他抓取服务**: 您已配置

## 下一步操作

1. **安装新依赖**:
```bash
cd apps/api
pnpm install
```

2. **添加基础配置** (可以直接使用现有的Unsplash配置)

3. **启动服务测试**:
```bash
# 在项目根目录
pnpm dev
```

4. **测试图片生成**:
```bash
curl -X POST http://localhost:3002/api/automation/test-image \
  -H "Content-Type: application/json" \
  -d '{"keyword": "aerospace technology", "category": "industry"}'
```

5. **测试完整流程**:
```bash
curl -X POST http://localhost:3002/api/automation/full-pipeline \
  -H "Content-Type: application/json" \
  -d '{"maxTasks": 2, "autoDeploy": false}'
``` 