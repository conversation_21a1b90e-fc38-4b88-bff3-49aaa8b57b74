import fetch from 'node-fetch';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

/**
 * 热词分析服务
 * 集成多种热词获取方案，提供健壮的fallback机制
 */
export class TrendAnalysisService {
  private config: {
    serpApiKey?: string;
    enableGoogleSuggest: boolean;
    enableLocalFallback: boolean;
    cacheDir: string;
  };

  constructor(config: Partial<TrendAnalysisService['config']> = {}) {
    this.config = {
      serpApiKey: process.env.SERP_API_KEY,
      enableGoogleSuggest: true,
      enableLocalFallback: true,
      cacheDir: join(__dirname, '../../../data/trends-cache'),
      ...config
    };
    
    // 确保缓存目录存在
    if (!fs.existsSync(this.config.cacheDir)) {
      fs.mkdirSync(this.config.cacheDir, { recursive: true });
    }
  }

  /**
   * 获取热词趋势（主入口）
   */
  async getTrendingKeywords(domain = 'aerospace defense'): Promise<{
    keywords: Array<{
      keyword: string;
      growth: string;
      relevance: 'high' | 'medium' | 'low';
      source: string;
    }>;
    source: string;
    timestamp: string;
  }> {
    console.log('🔍 开始获取热词趋势...');
    
    // 方案1: 尝试 SerpAPI
    if (this.config.serpApiKey) {
      try {
        const serpResult = await this.getSerpApiTrends(domain);
        if (serpResult.keywords.length > 0) {
          console.log('✅ SerpAPI 热词获取成功');
          await this.cacheTrends(serpResult);
          return serpResult;
        }
      } catch (error) {
        console.log('⚠️  SerpAPI 失败，尝试备用方案:', error.message);
      }
    }

    // 方案2: Google Suggest 自动补全
    if (this.config.enableGoogleSuggest) {
      try {
        const suggestResult = await this.getGoogleSuggestTrends(domain);
        if (suggestResult.keywords.length > 0) {
          console.log('✅ Google Suggest 热词获取成功');
          await this.cacheTrends(suggestResult);
          return suggestResult;
        }
      } catch (error) {
        console.log('⚠️  Google Suggest 失败:', error.message);
      }
    }

    // 方案3: 本地缓存备用
    if (this.config.enableLocalFallback) {
      try {
        const cacheResult = await this.getCachedTrends();
        if (cacheResult.keywords.length > 0) {
          console.log('✅ 使用本地缓存热词');
          return cacheResult;
        }
      } catch (error) {
        console.log('⚠️  本地缓存读取失败:', error.message);
      }
    }

    // 方案4: 预定义关键词库（最后备用）
    console.log('⚠️  所有热词源都失败，使用预定义关键词库');
    return this.getDefaultKeywords();
  }

  /**
   * SerpAPI 趋势获取
   */
  private async getSerpApiTrends(domain: string): Promise<any> {
    const searchTerms = [
      `${domain} trending`,
      `${domain} latest`,
      `${domain} breakthrough`,
      `${domain} innovation`
    ];

    const keywords = [];
    
    for (const term of searchTerms.slice(0, 2)) { // 限制调用次数
      try {
        const response = await fetch(
          `https://serpapi.com/search.json?q=${encodeURIComponent(term)}&tbm=nws&api_key=${this.config.serpApiKey}`
        );
        
        if (!response.ok) {
          throw new Error(`SerpAPI error: ${response.status}`);
        }
        
        const data = await response.json() as any;
        
        if (data.error) {
          throw new Error(`SerpAPI error: ${data.error}`);
        }
        
        // 从新闻标题中提取关键词
        if (data.news_results) {
          data.news_results.slice(0, 5).forEach((result: any) => {
            const extractedKeywords = this.extractKeywordsFromText(result.title);
            keywords.push(...extractedKeywords);
          });
        }
      } catch (error) {
        console.log(`SerpAPI 搜索 "${term}" 失败:`, error.message);
      }
    }

    return {
      keywords: this.processKeywords(keywords, 'serpapi'),
      source: 'SerpAPI',
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Google Suggest 趋势获取
   */
  private async getGoogleSuggestTrends(domain: string): Promise<any> {
    const baseKeywords = [
      'aerospace', 'defense', 'military', 'aviation', 'space', 
      'hypersonic', 'satellite', 'radar', 'drone', 'cyber'
    ];

    const keywords = [];

    for (const keyword of baseKeywords.slice(0, 5)) { // 限制调用次数
      try {
        const response = await fetch(
          `https://suggestqueries.google.com/complete/search?client=firefox&q=${encodeURIComponent(keyword)}`
        );
        
        if (response.ok) {
          const data = await response.json();
          if (data[1] && Array.isArray(data[1])) {
            keywords.push(...data[1].slice(0, 3)); // 取前3个建议
          }
        }
      } catch (error) {
        console.log(`Google Suggest 查询 "${keyword}" 失败:`, error.message);
      }
    }

    return {
      keywords: this.processKeywords(keywords, 'google-suggest'),
      source: 'Google Suggest',
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 获取缓存的趋势数据
   */
  private async getCachedTrends(): Promise<any> {
    const cacheFile = join(this.config.cacheDir, 'latest-trends.json');
    
    if (fs.existsSync(cacheFile)) {
      const cacheData = JSON.parse(fs.readFileSync(cacheFile, 'utf8'));
      const cacheAge = Date.now() - new Date(cacheData.timestamp).getTime();
      
      // 缓存不超过24小时
      if (cacheAge < 24 * 60 * 60 * 1000) {
        return {
          ...cacheData,
          source: `${cacheData.source} (cached)`
        };
      }
    }
    
    throw new Error('No valid cache found');
  }

  /**
   * 预定义关键词库（最后备用）
   */
  private getDefaultKeywords(): any {
    const defaultKeywords = [
      { keyword: 'hypersonic technology', growth: '+45%', relevance: 'high' as const, source: 'default' },
      { keyword: 'space economy', growth: '+32%', relevance: 'high' as const, source: 'default' },
      { keyword: 'autonomous defense', growth: '+28%', relevance: 'medium' as const, source: 'default' },
      { keyword: 'quantum radar', growth: '+67%', relevance: 'medium' as const, source: 'default' },
      { keyword: 'satellite communication', growth: '+23%', relevance: 'high' as const, source: 'default' },
      { keyword: 'drone warfare', growth: '+35%', relevance: 'medium' as const, source: 'default' },
      { keyword: 'cyber defense', growth: '+41%', relevance: 'high' as const, source: 'default' },
      { keyword: 'space debris', growth: '+29%', relevance: 'medium' as const, source: 'default' }
    ];

    return {
      keywords: defaultKeywords,
      source: 'Default Keywords',
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 从文本中提取关键词
   */
  private extractKeywordsFromText(text: string): string[] {
    if (!text) return [];
    
    // 简单的关键词提取逻辑
    const relevantWords = text.toLowerCase()
      .split(/\s+/)
      .filter(word => 
        word.length > 3 && 
        !['the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two', 'who', 'boy', 'did', 'man', 'way', 'with'].includes(word)
      );
    
    return relevantWords.slice(0, 5);
  }

  /**
   * 处理和标准化关键词
   */
  private processKeywords(rawKeywords: string[], source: string): Array<{
    keyword: string;
    growth: string;
    relevance: 'high' | 'medium' | 'low';
    source: string;
  }> {
    // 去重和过滤
    const uniqueKeywords = [...new Set(rawKeywords)]
      .filter(k => k && k.length > 3)
      .slice(0, 8);

    return uniqueKeywords.map((keyword, index) => ({
      keyword: keyword.toLowerCase(),
      growth: `+${Math.floor(Math.random() * 50 + 20)}%`, // 模拟增长率
      relevance: index < 3 ? 'high' as const : index < 6 ? 'medium' as const : 'low' as const,
      source
    }));
  }

  /**
   * 缓存趋势数据
   */
  private async cacheTrends(trendsData: any): Promise<void> {
    try {
      const cacheFile = join(this.config.cacheDir, 'latest-trends.json');
      fs.writeFileSync(cacheFile, JSON.stringify(trendsData, null, 2));
    } catch (error) {
      console.log('⚠️  缓存趋势数据失败:', error.message);
    }
  }
} 