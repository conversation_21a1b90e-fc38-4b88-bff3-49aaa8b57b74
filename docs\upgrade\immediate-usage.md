# 立即可用使用指南

## 🎯 当前状态

您现有的配置已经可以立即使用图片热插拔模块！

### ✅ 已可用的服务
- **Unsplash**: 您已配置，作为可靠的兜底图片服务
- **OpenAI**: 您已配置，用于内容生成
- **其他抓取服务**: 您已配置

### 🔄 图片服务回退链
```
Workers (优先级1) → Fal.ai (优先级2) → Unsplash (兜底保障)
提示词生成          提示词AI生成        关键词搜索照片
```

**重要**: 所有服务都根据文章内容生成相关图片，Unsplash使用关键词搜索匹配的照片，不是固定图片！

## 🚀 立即开始

### 1. 安装依赖
```bash
cd apps/api
pnpm install
```

### 2. 添加最小配置
在您的 `.env` 文件中添加：

```bash
# 启用图片生成 (使用您现有的Unsplash)
ENABLE_IMAGE_GENERATION=true

# 可选：启用自动调度
SCHEDULER_ENABLED=true
SCHEDULER_CRON=0 9 * * *
SCHEDULER_AUTO_DEPLOY=false  # 先设为false测试
```

### 3. 启动服务
```bash
# 在项目根目录
pnpm dev
```

### 4. 测试图片生成
```bash
curl -X POST http://localhost:3002/api/automation/test-image \
  -H "Content-Type: application/json" \
  -d '{"keyword": "aerospace technology", "category": "industry"}'
```

**预期结果**: 系统会使用您的Unsplash配置生成图片

### 5. 测试完整流程
```bash
curl -X POST http://localhost:3002/api/automation/full-pipeline \
  -H "Content-Type: application/json" \
  -d '{"maxTasks": 1, "autoDeploy": false}'
```

**预期结果**: 
- 生成1个任务的计划
- 执行任务生成文章
- 使用Unsplash为文章配图
- 生成到 `apps/web/src/content/news/` 目录

## 🎨 可选增强

### 添加Fal.ai (AI图片生成)
```bash
# 获取 https://fal.ai/ 的API密钥后
FAL_API_KEY=your_fal_api_key
```

添加后，系统会优先尝试AI生成，失败时回退到Unsplash

### 添加Cloudflare Workers (最高性能)
```bash
CLOUDFLARE_WORKER_URL=https://your-worker.workers.dev
CLOUDFLARE_API_KEY=your_api_key
CLOUDFLARE_ACCOUNT_ID=your_account_id
```

添加后，系统会优先使用Workers，提供最快的图片服务

## 📊 服务监控

启动后访问：
- API文档: http://localhost:3002/docs
- 图片服务状态: `GET /api/automation/image-health`
- 调度器状态: `GET /api/automation/scheduler/status`

## 🎯 生产环境建议

1. **当前阶段**: 使用Unsplash兜底服务，完全可用
2. **性能优化**: 添加Fal.ai获得AI生成能力  
3. **扩展优化**: 部署Cloudflare Workers获得最佳性能

每个阶段都是可选的，您可以根据需要逐步升级！ 