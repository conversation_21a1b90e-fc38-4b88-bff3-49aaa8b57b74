{"name": "@news-site/web", "version": "0.1.0", "type": "module", "private": true, "main": "src/index.astro", "scripts": {"dev": "astro dev", "build": "astro build", "preview": "astro preview", "type-check": "astro check", "lint": "eslint src/", "format": "prettier --write src/"}, "dependencies": {"@astrojs/mdx": "^4.3.0", "@astrojs/tailwind": "^6.0.2", "@astrojs/vercel": "^8.1.4", "@tailwindcss/typography": "^0.5.15", "alpinejs": "^3.14.7", "astro": "^5.8.0", "fuse.js": "^7.0.0", "tailwindcss": "^3.4.15"}, "devDependencies": {"@astrojs/check": "^0.9.4", "@astrojs/ts-plugin": "^1.10.4", "@types/alpinejs": "^3.13.10", "prettier": "^3.3.3", "typescript": "^5.7.2"}}