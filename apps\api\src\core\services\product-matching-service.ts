import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

export interface Product {
  title: string;
  description: string;
  keywords: string[];
  tags: string[];
  useCases: string[];
  url: string;
  image: string;
  model?: string; // 可选的型号字段
  status: {
    isNew: boolean;
    isRecommended: boolean;
  };
}

export interface ProductMatchResult {
  product: Product;
  score: number;
  matchedKeywords: string[];
  matchType: 'title' | 'keyword' | 'description' | 'tags' | 'useCases' | 'model';
}

export interface ProductMatchConfig {
  maxProducts: number;
  minScore: number;
  enableFuzzyMatch: boolean;
  preferRecommended: boolean;
}

export class ProductMatchingService {
  private products: Product[] = [];
  private config: ProductMatchConfig;
  
  constructor(config: Partial<ProductMatchConfig> = {}) {
    this.config = {
      maxProducts: 5,
      minScore: 0.3,
      enableFuzzyMatch: true,
      preferRecommended: true,
      ...config
    };
    
    this.loadProducts();
  }

  /**
   * 加载产品数据
   */
  private loadProducts(): void {
    try {
      // 获取当前文件的目录（ES模块兼容）
      const __filename = fileURLToPath(import.meta.url);
      const __dirname = dirname(__filename);
      
      // 尝试多个可能的路径
      const possiblePaths = [
        path.join(process.cwd(), 'data/products'),
        path.join(process.cwd(), '../../data/products'),
        path.resolve(__dirname, '../../../../../data/products')
      ];
      
      let productDataPath: string | null = null;
      for (const testPath of possiblePaths) {
        if (fs.existsSync(testPath)) {
          productDataPath = testPath;
          break;
        }
      }
      
      if (!productDataPath) {
        throw new Error(`Product data directory not found. Tried paths: ${possiblePaths.join(', ')}`);
      }
      
      console.log(`📁 使用产品数据路径: ${productDataPath}`);
      const files = fs.readdirSync(productDataPath);
      
      for (const file of files) {
        if (file.endsWith('.json')) {
          const filePath = path.join(productDataPath, file);
          const data = JSON.parse(fs.readFileSync(filePath, 'utf-8'));
          
          if (data.products && Array.isArray(data.products)) {
            this.products.push(...data.products);
          }
        }
      }
      
      console.log(`✅ 已加载 ${this.products.length} 个产品数据`);
    } catch (error) {
      console.error('❌ 加载产品数据失败:', error);
    }
  }

  /**
   * 智能产品匹配
   */
  public matchProducts(content: {
    title?: string;
    keyword?: string;
    description?: string;
    category?: string;
    extractedContent?: string;
  }): ProductMatchResult[] {
    const searchTerms = this.extractSearchTerms(content);
    const results: ProductMatchResult[] = [];

    for (const product of this.products) {
      const matchResult = this.calculateProductScore(product, searchTerms);
      
      if (matchResult.score >= this.config.minScore) {
        results.push(matchResult);
      }
    }

    // 排序和筛选
    return this.rankAndFilterResults(results);
  }

  /**
   * 提取搜索词汇
   */
  private extractSearchTerms(content: {
    title?: string;
    keyword?: string;
    description?: string;
    category?: string;
    extractedContent?: string;
  }): { term: string; weight: number }[] {
    const terms: { term: string; weight: number }[] = [];
    
    // 主要关键词（权重最高）
    if (content.keyword) {
      terms.push(...this.tokenize(content.keyword, 3.0));
    }
    
    // 标题关键词
    if (content.title) {
      terms.push(...this.tokenize(content.title, 2.0));
    }
    
    // 分类
    if (content.category) {
      terms.push(...this.tokenize(content.category, 1.5));
    }
    
    // 描述
    if (content.description) {
      terms.push(...this.tokenize(content.description, 1.0));
    }
    
    // 提取的内容（权重较低但覆盖面广）
    if (content.extractedContent) {
      const importantTerms = this.extractImportantTerms(content.extractedContent);
      terms.push(...importantTerms.map(term => ({ term, weight: 0.8 })));
    }
    
    return this.deduplicateTerms(terms);
  }

  /**
   * 分词并赋权重
   */
  private tokenize(text: string, weight: number): { term: string; weight: number }[] {
    return text
      .toLowerCase()
      .replace(/[^\w\s-]/g, ' ')
      .split(/\s+/)
      .filter(term => term.length > 2)
      .map(term => ({ term, weight }));
  }

  /**
   * 从长文本中提取重要术语
   */
  private extractImportantTerms(content: string): string[] {
    // 技术术语模式
    const techPatterns = [
      /\b[A-Z]{2,}\b/g, // 大写缩写词
      /\b\w*(?:tech|system|solution|platform|device|equipment|component)\w*\b/gi,
      /\b\w*(?:missile|radar|satellite|communication|guidance|control)\w*\b/gi,
      /\b\w*(?:defense|military|aerospace|naval|army|air force)\w*\b/gi
    ];
    
    const terms = new Set<string>();
    
    for (const pattern of techPatterns) {
      const matches = content.match(pattern) || [];
      matches.forEach(match => {
        if (match.length > 2) {
          terms.add(match.toLowerCase());
        }
      });
    }
    
    return Array.from(terms);
  }

  /**
   * 去重术语
   */
  private deduplicateTerms(terms: { term: string; weight: number }[]): { term: string; weight: number }[] {
    const termMap = new Map<string, number>();
    
    for (const { term, weight } of terms) {
      const currentWeight = termMap.get(term) || 0;
      termMap.set(term, Math.max(currentWeight, weight));
    }
    
    return Array.from(termMap.entries()).map(([term, weight]) => ({ term, weight }));
  }

  /**
   * 计算产品匹配分数
   */
  private calculateProductScore(
    product: Product, 
    searchTerms: { term: string; weight: number }[]
  ): ProductMatchResult {
    let totalScore = 0;
    const matchedKeywords: string[] = [];
    let primaryMatchType: ProductMatchResult['matchType'] = 'description';

    for (const { term, weight } of searchTerms) {
      // 标题匹配（最高权重）
      if (product.title.toLowerCase().includes(term)) {
        totalScore += weight * 3.0;
        matchedKeywords.push(term);
        primaryMatchType = 'title';
      }

      // 型号匹配（高权重）
      if (product.model && product.model.toLowerCase().includes(term)) {
        totalScore += weight * 2.8;
        matchedKeywords.push(term);
        if (primaryMatchType === 'description') primaryMatchType = 'model';
      }

      // 关键词精确匹配
      const keywordMatch = product.keywords.some(keyword =>
        keyword.toLowerCase().includes(term) || term.includes(keyword.toLowerCase())
      );
      if (keywordMatch) {
        totalScore += weight * 2.5;
        matchedKeywords.push(term);
        if (primaryMatchType === 'description') primaryMatchType = 'keyword';
      }

      // 描述匹配
      if (product.description.toLowerCase().includes(term)) {
        totalScore += weight * 1.5;
        matchedKeywords.push(term);
      }

      // 标签和用例匹配
      const tagMatch = [...product.tags, ...product.useCases].some(tag =>
        tag.toLowerCase().includes(term)
      );
      if (tagMatch) {
        totalScore += weight * 1.0;
        matchedKeywords.push(term);
        if (primaryMatchType === 'description') primaryMatchType = 'tags';
      }
    }

    // 归一化分数
    const maxPossibleScore = searchTerms.reduce((sum, { weight }) => sum + weight * 3.0, 0);
    const normalizedScore = maxPossibleScore > 0 ? totalScore / maxPossibleScore : 0;

    return {
      product,
      score: normalizedScore,
      matchedKeywords: [...new Set(matchedKeywords)],
      matchType: primaryMatchType
    };
  }

  /**
   * 排序和筛选结果
   */
  private rankAndFilterResults(results: ProductMatchResult[]): ProductMatchResult[] {
    // 排序：分数优先，推荐产品优先
    results.sort((a, b) => {
      // 首先按分数排序
      if (a.score !== b.score) {
        return b.score - a.score;
      }
      
      // 分数相同时，推荐产品优先
      if (this.config.preferRecommended) {
        const aRecommended = a.product.status.isRecommended;
        const bRecommended = b.product.status.isRecommended;
        
        if (aRecommended !== bRecommended) {
          return bRecommended ? 1 : -1;
        }
      }
      
      // 最后按匹配关键词数量排序
      return b.matchedKeywords.length - a.matchedKeywords.length;
    });

    return results.slice(0, this.config.maxProducts);
  }

  /**
   * 生成产品链接文本
   */
  public generateProductLinks(matches: ProductMatchResult[]): string[] {
    return matches.map(match => {
      const { product, matchedKeywords } = match;
      
      // 选择最相关的关键词作为链接文本
      const linkText = matchedKeywords[0] || product.title;
      
      return `[${linkText}](${product.url})`;
    });
  }

  /**
   * 生成结构化产品推荐
   */
  public generateProductRecommendations(matches: ProductMatchResult[]): Array<{
    id: string;
    title: string;
    description: string;
    url: string;
    image: string;
    matchScore: number;
    isRecommended: boolean;
  }> {
    return matches.map(match => ({
      id: match.product.title,
      title: match.product.title,
      description: match.product.description,
      url: match.product.url,
      image: match.product.image,
      matchScore: Math.round(match.score * 100),
      isRecommended: match.product.status.isRecommended
    }));
  }

  /**
   * 获取产品统计信息
   */
  public getStats(): {
    totalProducts: number;
    recommendedProducts: number;
    categories: string[];
  } {
    const categories = new Set<string>();
    let recommendedCount = 0;
    
    for (const product of this.products) {
      if (product.status.isRecommended) {
        recommendedCount++;
      }
      
      // 从关键词中提取分类
      product.keywords.forEach(keyword => {
        if (keyword.includes('Battery')) categories.add('Battery');
        if (keyword.includes('Actuator')) categories.add('Actuator');
        if (keyword.includes('Communication')) categories.add('Communication');
        if (keyword.includes('Thermal')) categories.add('Thermal');
        // ... 可以添加更多分类逻辑
      });
    }
    
    return {
      totalProducts: this.products.length,
      recommendedProducts: recommendedCount,
      categories: Array.from(categories)
    };
  }
}

// 导出单例实例
export const productMatchingService = new ProductMatchingService(); 