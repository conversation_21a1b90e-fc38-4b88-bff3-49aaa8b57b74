import { eq, desc, and, count } from 'drizzle-orm';
import { db } from './connection.js';
import { plans, tasks, contents, config, logs } from './schema.js';
import type { 
  Plan, NewPlan, 
  Task, NewTask, 
  Content, NewContent,
  Config, NewConfig,
  Log, NewLog 
} from './schema.js';

// 生成ID的工具函数
function generateId(): string {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

// 计划仓库
export const planRepository = {
  // 获取所有计划
  async getAll(): Promise<Plan[]> {
    return await db.select().from(plans).orderBy(desc(plans.createdAt));
  },

  // 根据ID获取计划
  async getById(id: string): Promise<Plan | undefined> {
    const result = await db.select().from(plans).where(eq(plans.id, id));
    return result[0];
  },

  // 根据日期获取计划
  async getByDate(date: string): Promise<Plan | undefined> {
    const result = await db.select().from(plans).where(eq(plans.date, date));
    return result[0];
  },

  // 创建计划
  async create(planData: Omit<NewPlan, 'id' | 'createdAt' | 'updatedAt'>): Promise<Plan> {
    const newPlan: NewPlan = {
      ...planData,
      id: generateId(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    await db.insert(plans).values(newPlan);
    return newPlan as Plan;
  },

  // 更新计划
  async update(id: string, updates: Partial<Omit<NewPlan, 'id' | 'createdAt'>>): Promise<Plan | undefined> {
    const updateData = {
      ...updates,
      updatedAt: new Date().toISOString()
    };
    
    await db.update(plans).set(updateData).where(eq(plans.id, id));
    return await this.getById(id);
  },

  // 删除计划
  async delete(id: string): Promise<boolean> {
    const result = await db.delete(plans).where(eq(plans.id, id));
    return result.changes > 0;
  }
};

// 任务仓库
export const taskRepository = {
  // 获取所有任务
  async getAll(limit = 50, offset = 0): Promise<Task[]> {
    return await db.select().from(tasks)
      .orderBy(desc(tasks.createdAt))
      .limit(limit)
      .offset(offset);
  },

  // 根据状态获取任务
  async getByStatus(status: 'pending' | 'in-progress' | 'completed' | 'failed', limit = 50): Promise<Task[]> {
    return await db.select().from(tasks)
      .where(eq(tasks.status, status))
      .orderBy(desc(tasks.createdAt))
      .limit(limit);
  },

  // 根据计划ID获取任务
  async getByPlanId(planId: string): Promise<Task[]> {
    return await db.select().from(tasks)
      .where(eq(tasks.planId, planId))
      .orderBy(desc(tasks.createdAt));
  },

  // 根据ID获取任务
  async getById(id: string): Promise<Task | undefined> {
    const result = await db.select().from(tasks).where(eq(tasks.id, id));
    return result[0];
  },

  // 创建任务
  async create(taskData: Omit<NewTask, 'id' | 'createdAt' | 'updatedAt'>): Promise<Task> {
    const newTask: NewTask = {
      ...taskData,
      id: generateId(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    await db.insert(tasks).values(newTask);
    return newTask as Task;
  },

  // 更新任务
  async update(id: string, updates: Partial<Omit<NewTask, 'id' | 'createdAt'>>): Promise<Task | undefined> {
    const updateData = {
      ...updates,
      updatedAt: new Date().toISOString()
    };
    
    await db.update(tasks).set(updateData).where(eq(tasks.id, id));
    return await this.getById(id);
  },

  // 批量更新任务状态
  async updateStatus(ids: string[], status: 'pending' | 'in-progress' | 'completed' | 'failed'): Promise<number> {
    // Note: 这里简化处理，实际应该使用 IN 操作
    let updated = 0;
    for (const id of ids) {
      const result = await db.update(tasks)
        .set({ 
          status, 
          updatedAt: new Date().toISOString() 
        })
        .where(eq(tasks.id, id));
      if (result.changes > 0) updated++;
    }
    return updated;
  },

  // 删除任务
  async delete(id: string): Promise<boolean> {
    const result = await db.delete(tasks).where(eq(tasks.id, id));
    return result.changes > 0;
  },

  // 统计任务数量
  async getStats(): Promise<{ total: number; completed: number; pending: number; failed: number }> {
    const [totalResult] = await db.select({ count: count() }).from(tasks);
    const [completedResult] = await db.select({ count: count() }).from(tasks).where(eq(tasks.status, 'completed'));
    const [pendingResult] = await db.select({ count: count() }).from(tasks).where(eq(tasks.status, 'pending'));
    const [failedResult] = await db.select({ count: count() }).from(tasks).where(eq(tasks.status, 'failed'));
    
    return {
      total: totalResult.count,
      completed: completedResult.count,
      pending: pendingResult.count,
      failed: failedResult.count
    };
  }
};

// 内容仓库
export const contentRepository = {
  // 根据任务ID获取内容
  async getByTaskId(taskId: string): Promise<Content | undefined> {
    const result = await db.select().from(contents).where(eq(contents.taskId, taskId));
    return result[0];
  },

  // 根据ID获取内容
  async getById(id: string): Promise<Content | undefined> {
    const result = await db.select().from(contents).where(eq(contents.id, id));
    return result[0];
  },

  // 创建内容
  async create(contentData: Omit<NewContent, 'id' | 'createdAt' | 'updatedAt'>): Promise<Content> {
    const newContent: NewContent = {
      ...contentData,
      id: generateId(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    await db.insert(contents).values(newContent);
    return newContent as Content;
  },

  // 更新内容
  async update(id: string, updates: Partial<Omit<NewContent, 'id' | 'createdAt'>>): Promise<Content | undefined> {
    const updateData = {
      ...updates,
      updatedAt: new Date().toISOString()
    };
    
    await db.update(contents).set(updateData).where(eq(contents.id, id));
    return await this.getById(id);
  }
};

// 配置仓库
export const configRepository = {
  // 获取配置
  async get(key: string): Promise<any> {
    const result = await db.select().from(config).where(eq(config.key, key));
    return result[0]?.value;
  },

  // 设置配置
  async set(key: string, value: any, description?: string): Promise<void> {
    const configData: NewConfig = {
      key,
      value,
      description,
      updatedAt: new Date().toISOString()
    };
    
    // 使用 UPSERT 操作
    await db.insert(config).values(configData)
      .onConflictDoUpdate({
        target: config.key,
        set: {
          value,
          description,
          updatedAt: new Date().toISOString()
        }
      });
  },

  // 获取所有配置
  async getAll(): Promise<Config[]> {
    return await db.select().from(config);
  }
};

// 日志仓库
export const logRepository = {
  // 添加日志
  async add(level: 'debug' | 'info' | 'warn' | 'error', message: string, data?: any, source?: string): Promise<void> {
    const logData: NewLog = {
      id: generateId(),
      level,
      message,
      data,
      source,
      createdAt: new Date().toISOString()
    };
    
    await db.insert(logs).values(logData);
  },

  // 获取最近的日志
  async getRecent(limit = 100): Promise<Log[]> {
    return await db.select().from(logs)
      .orderBy(desc(logs.createdAt))
      .limit(limit);
  },

  // 清理旧日志
  async cleanup(daysToKeep = 30): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
    
    const result = await db.delete(logs)
      .where(eq(logs.createdAt, cutoffDate.toISOString()));
    
    return result.changes;
  }
}; 