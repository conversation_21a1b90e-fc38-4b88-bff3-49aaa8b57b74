#!/bin/bash

echo "🔧 Ubuntu 系统导出问题修复脚本"
echo "================================="

# 设置错误时退出
set -e

# 完全清理
echo "🧹 完全清理所有构建文件和依赖..."
rm -rf node_modules
rm -rf packages/*/node_modules
rm -rf apps/*/node_modules
rm -rf packages/*/dist
rm -rf apps/*/dist
rm -f pnpm-lock.yaml

# 重新安装依赖
echo "📦 重新安装依赖..."
pnpm install

# 检查 shared 包的 tsconfig.json
echo "🔍 检查 shared 包配置..."
cd packages/shared

# 临时修改 tsconfig.json 为更兼容的配置
cat > tsconfig.json << 'EOF'
{
  "compilerOptions": {
    "target": "ES2022",
    "module": "ES2022",
    "moduleResolution": "node",
    "outDir": "./dist",
    "rootDir": "./src",
    "resolveJsonModule": true,
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "strict": false,
    "noImplicitAny": false,
    "allowJs": true,
    "declaration": true,
    "declarationMap": true,
    "sourceMap": false
  },
  "include": ["src/**/*"],
  "exclude": ["dist", "node_modules"]
}
EOF

# 构建 shared 包
echo "🏗️  构建 shared 包..."
pnpm run build

# 验证构建结果
echo "✅ 验证 shared 包构建结果..."
if [ ! -f "dist/index.js" ]; then
    echo "❌ 缺少 dist/index.js"
    exit 1
fi
if [ ! -f "dist/index.d.ts" ]; then
    echo "❌ 缺少 dist/index.d.ts"
    exit 1
fi
if [ ! -f "dist/types/api.d.ts" ]; then
    echo "❌ 缺少 dist/types/api.d.ts"
    exit 1
fi
if [ ! -f "dist/utils/frontmatter.d.ts" ]; then
    echo "❌ 缺少 dist/utils/frontmatter.d.ts"
    exit 1
fi

echo "✅ shared 包构建验证通过"

# 检查导出
echo "🔍 检查导出内容..."
echo "index.d.ts 内容:"
cat dist/index.d.ts
echo ""
echo "检查 TrendAnalysis 导出:"
grep -n "TrendAnalysis" dist/types/api.d.ts || echo "未找到 TrendAnalysis"
echo ""
echo "检查 generateFrontmatter 导出:"
grep -n "generateFrontmatter" dist/utils/frontmatter.d.ts || echo "未找到 generateFrontmatter"

# 返回根目录
cd ../..

# 构建 API
echo "🏗️  构建 API..."
cd apps/api

# 也为 API 使用更兼容的配置
cat > tsconfig.json << 'EOF'
{
  "compilerOptions": {
    "target": "ES2022",
    "module": "ES2022",
    "moduleResolution": "node",
    "outDir": "./dist",
    "rootDir": "./src",
    "resolveJsonModule": true,
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "strict": false,
    "noImplicitAny": false,
    "allowJs": true,
    "declaration": true,
    "declarationMap": true,
    "sourceMap": false
  },
  "include": ["src/**/*"],
  "exclude": ["dist", "node_modules"],
  "references": [
    {
      "path": "../../packages/shared"
    }
  ]
}
EOF

pnpm run build

# 验证 API 构建结果
if [ ! -f "dist/index.js" ]; then
    echo "❌ API 构建失败 - 缺少 dist/index.js"
    exit 1
fi

echo "✅ API 构建成功"

# 返回根目录
cd ../..

echo "🎉 修复完成！"
echo ""
echo "测试启动："
echo "cd apps/api && PORT=3003 node dist/index.js"
