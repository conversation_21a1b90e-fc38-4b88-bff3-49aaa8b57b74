实现类似 Google Trends 的功能：

---

## ✅ 替代 PyTrends 的 Node.js 热词方案

### 1. **Google Trends API（Node.js 非官方）**

社区提供了一个 Node 包，可以在 Node.js 中使用 Google Trends 数据：

#### 📦 安装

```bash
pnpm install google-trends-api
```

#### ✅ 示例代码（TS/JS 均可）

```ts
import googleTrends from 'google-trends-api';

googleTrends.relatedQueries({ keyword: 'aerospace', geo: 'US', timeframe: 'now 7-d' })
  .then((results) => {
    const data = JSON.parse(results);
    console.log(data.default.rankedList[0].rankedKeyword.map(k => k.query));
  })
  .catch(console.error);
```

#### 💡 可获取：

* related queries（相关查询词）
* trending searches（趋势热搜）
* interest over time（时间趋势）
* interest by region（地区热度）

#### 📌 注意：

* 和 PyTrends 类似，这也是**非官方 API**，数据来自 Google Trends Web 页面解析
* Google 频繁访问会 rate-limit，需要添加代理或控制频率

---

### 2. **Tavily + NLP 关键词抽取（推荐方案）**

如果你的目标是提取“热词”而非趋势图，你也可以：

* 用 Tavily 搜索关键词
* 抽取返回结果中出现频率高的词组
* 使用 `compromise` 或 `natural` 等 NLP 库抽取关键词

```bash
pnpm install compromise
```

```ts
import nlp from 'compromise';

const text = 'NASA is developing next-generation hypersonic aircraft for defense purposes.';
const doc = nlp(text);
console.log(doc.nouns().out('array'));  // ['NASA', 'aircraft', 'defense', 'purposes']
```

---

### 3. **结合 Google Suggest 实时热词（免登录、无 API Key）**

获取自动补全词，模拟热词：

```ts
const fetch = await import('node-fetch');

const query = 'aerospace';
const res = await fetch.default(`https://suggestqueries.google.com/complete/search?client=firefox&q=${query}`);
const json = await res.json();
console.log(json[1]);  // 结果是补全词列表
```

---

## ✅ 总结推荐

| 方案                  | 优点                              | 适用场景      |
| ------------------- | ------------------------------- | --------- |
| `google-trends-api` | 原始 Google Trends 数据，接近 PyTrends | 关键词趋势/相关词 |
| `Tavily + NLP`      | 更适合做语义提取、上下文关键词                 | 热词自动摘要    |
| `Google Suggest`    | 无需 API Key，适合快速补全词抓取            | 联想词、爆款预判  |

---
