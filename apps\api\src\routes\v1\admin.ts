import type { FastifyInstance } from 'fastify';

export default async function adminRoutes(fastify: FastifyInstance) {
  // GET /api/v1/admin/stats - 获取系统统计
  fastify.get('/stats', async (request, reply) => {
    return {
      success: true,
      data: {
        totalTasks: 0,
        completedTasks: 0,
        pendingTasks: 0,
        successRate: 0,
        avgProcessingTime: 0,
        lastGeneration: null
      },
      message: '系统统计获取成功 (临时响应)'
    };
  });

  // POST /api/v1/admin/reindex - 重建索引
  fastify.post('/reindex', async (request, reply) => {
    return {
      success: true,
      message: '重建索引完成 (临时响应)'
    };
  });
} 