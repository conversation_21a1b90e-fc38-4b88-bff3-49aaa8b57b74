# n8n 工作流集成指南

本指南详细说明如何将n8n与新闻平台的API服务集成，实现灵活的工作流编排。

## 📖 概述

n8n是一个基于节点的工作流自动化工具，可以让团队成员通过可视化界面编排复杂的自动化流程。结合我们的API服务，可以实现：

- **灵活的内容生成流程**
- **多人协作的时段管理**
- **基于条件的智能决策**
- **外部系统集成**
- **实时通知和监控**

## 🚀 快速开始

### 1. 安装n8n

#### 方式1: Docker (推荐)
```bash
# 使用Docker Compose
version: '3.7'
services:
  n8n:
    image: n8nio/n8n
    ports:
      - "5678:5678"
    environment:
      - DB_TYPE=postgresdb
      - DB_POSTGRESDB_HOST=postgres
      - DB_POSTGRESDB_PORT=5432
      - DB_POSTGRESDB_DATABASE=n8n
      - DB_POSTGRESDB_USER=n8n
      - DB_POSTGRESDB_PASSWORD=n8n
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=admin
      - N8N_BASIC_AUTH_PASSWORD=admin123
    volumes:
      - n8n_data:/home/<USER>/.n8n
    depends_on:
      - postgres

  postgres:
    image: postgres:13
    environment:
      - POSTGRES_DB=n8n
      - POSTGRES_USER=n8n
      - POSTGRES_PASSWORD=n8n
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  n8n_data:
  postgres_data:
```

#### 方式2: 全局安装
```bash
npm install n8n -g
n8n start
```

### 2. 基础配置

启动后访问 `http://localhost:5678`，创建管理员账户并配置基础设置。

## 🔧 API服务配置

### 环境变量设置

在n8n中配置以下环境变量或凭据：

```javascript
// API服务配置
const API_CONFIG = {
  baseUrl: 'http://localhost:3002',
  timeout: 30000,
  retries: 3
};

// 认证配置 (如果需要)
const AUTH_CONFIG = {
  apiKey: process.env.NEWS_API_KEY,
  authHeader: 'Authorization',
  authValue: `Bearer ${process.env.AUTH_TOKEN}`
};
```

## 📋 核心工作流模板

### 模板1: 基础内容生成流程

```json
{
  "name": "News Content Generation",
  "nodes": [
    {
      "name": "Webhook Trigger",
      "type": "n8n-nodes-base.webhook",
      "parameters": {
        "httpMethod": "POST",
        "path": "content-generation",
        "responseMode": "onReceived"
      }
    },
    {
      "name": "Generate Content",
      "type": "n8n-nodes-base.httpRequest",
      "parameters": {
        "method": "POST",
        "url": "http://localhost:3002/api/v1/content/generate",
        "headers": {
          "Content-Type": "application/json"
        },
        "body": {
          "keyword": "={{ $json.keyword }}",
          "category": "={{ $json.category || 'industry' }}",
          "relatedProducts": "={{ $json.relatedProducts || [] }}",
          "generateImage": "={{ $json.generateImage || true }}",
          "dryRun": "={{ $json.dryRun || false }}"
        },
        "options": {
          "timeout": 60000
        }
      }
    },
    {
      "name": "Check Result",
      "type": "n8n-nodes-base.if",
      "parameters": {
        "conditions": {
          "string": [
            {
              "value1": "={{ $json.success }}",
              "operation": "equal",
              "value2": "true"
            }
          ]
        }
      }
    },
    {
      "name": "Validate Hero Image",
      "type": "n8n-nodes-base.httpRequest",
      "parameters": {
        "method": "POST",
        "url": "http://localhost:3002/api/automation/validate-hero-images",
        "headers": {
          "Content-Type": "application/json"
        },
        "body": {
          "validateUrls": true,
          "fixIssues": true,
          "dryRun": false
        }
      }
    },
    {
      "name": "Send Success Notification",
      "type": "n8n-nodes-base.slack",
      "parameters": {
        "channel": "#news-automation",
        "text": "✅ Content generated successfully: {{ $node['Generate Content'].json.data.article.title }}"
      }
    },
    {
      "name": "Send Error Notification",
      "type": "n8n-nodes-base.slack",
      "parameters": {
        "channel": "#news-automation",
        "text": "❌ Content generation failed: {{ $node['Generate Content'].json.error }}"
      }
    }
  ],
  "connections": {
    "Webhook Trigger": {
      "main": [["Generate Content"]]
    },
    "Generate Content": {
      "main": [["Check Result"]]
    },
    "Check Result": {
      "main": [
        ["Validate Hero Image"],
        ["Send Error Notification"]
      ]
    },
    "Validate Hero Image": {
      "main": [["Send Success Notification"]]
    }
  }
}
```

### 模板2: 定时批量处理流程

```json
{
  "name": "Scheduled Batch Processing",
  "nodes": [
    {
      "name": "Schedule Trigger",
      "type": "n8n-nodes-base.cron",
      "parameters": {
        "rule": {
          "field": "cronExpression",
          "expression": "0 6 * * *"
        }
      }
    },
    {
      "name": "Get Pending Tasks",
      "type": "n8n-nodes-base.httpRequest",
      "parameters": {
        "method": "GET",
        "url": "http://localhost:3002/api/v1/content?status=pending&limit=10"
      }
    },
    {
      "name": "Split Tasks",
      "type": "n8n-nodes-base.splitInBatches",
      "parameters": {
        "batchSize": 1,
        "options": {}
      }
    },
    {
      "name": "Process Task",
      "type": "n8n-nodes-base.httpRequest",
      "parameters": {
        "method": "POST",
        "url": "http://localhost:3002/api/v1/content/generate",
        "headers": {
          "Content-Type": "application/json"
        },
        "body": {
          "taskId": "={{ $json.id }}",
          "keyword": "={{ $json.keyword }}",
          "category": "={{ $json.category }}",
          "relatedProducts": "={{ $json.relatedProducts }}",
          "generateImage": true
        }
      }
    },
    {
      "name": "Collect Results",
      "type": "n8n-nodes-base.merge",
      "parameters": {
        "mode": "append"
      }
    },
    {
      "name": "Pre-deployment Check",
      "type": "n8n-nodes-base.httpRequest",
      "parameters": {
        "method": "POST",
        "url": "http://localhost:3002/api/automation/pre-deployment-check",
        "headers": {
          "Content-Type": "application/json"
        },
        "body": {
          "validateHeroImages": true,
          "validateContent": true,
          "generateSearchIndex": true
        }
      }
    },
    {
      "name": "Deploy if Ready",
      "type": "n8n-nodes-base.if",
      "parameters": {
        "conditions": {
          "boolean": [
            {
              "value1": "={{ $json.data.readyForDeployment }}",
              "value2": true
            }
          ]
        }
      }
    }
  ]
}
```

## 👥 多用户协作配置

### 用户角色和权限

```javascript
// 用户角色定义
const USER_ROLES = {
  ADMIN: {
    permissions: ['all'],
    workflows: ['*'],
    timeRestrictions: null
  },
  EDITOR: {
    permissions: ['content:create', 'content:edit', 'workflow:execute'],
    workflows: ['content-generation', 'content-editing'],
    timeRestrictions: '06:00-22:00'
  },
  REVIEWER: {
    permissions: ['content:review', 'content:publish'],
    workflows: ['content-review', 'content-publishing'],
    timeRestrictions: '08:00-20:00'
  },
  OBSERVER: {
    permissions: ['content:read', 'workflow:read'],
    workflows: ['monitoring', 'reporting'],
    timeRestrictions: null
  }
};
```

### 时段分工配置

```json
{
  "schedules": {
    "morning_shift": {
      "time": "06:00-14:00",
      "users": ["editor-morning", "reviewer-morning"],
      "workflows": ["content-generation", "breaking-news-response"],
      "tasks": [
        "新闻抓取和初步处理",
        "内容生成和基础编辑",
        "图片处理和优化"
      ]
    },
    "afternoon_shift": {
      "time": "14:00-22:00", 
      "users": ["editor-afternoon", "reviewer-afternoon", "publisher"],
      "workflows": ["content-editing", "content-publishing", "seo-optimization"],
      "tasks": [
        "内容完善和深度编辑",
        "SEO优化和标签添加",
        "审核和发布流程"
      ]
    },
    "night_shift": {
      "time": "22:00-06:00",
      "users": ["system", "admin-oncall"],
      "workflows": ["automated-processing", "system-maintenance"],
      "tasks": [
        "自动化内容处理",
        "系统维护和备份",
        "数据清理和优化"
      ]
    }
  }
}
```

## 🎯 实际使用案例

### 案例1: 紧急新闻响应

当外部系统检测到紧急新闻时：

1. **外部系统** → Webhook触发
2. **n8n接收** → 解析事件数据
3. **优先级判断** → 如果是紧急新闻，跳过排队
4. **立即生成内容** → 调用高优先级API
5. **快速审核** → 通知相关编辑人员
6. **自动发布** → 经过简化审核后发布

```bash
# 触发紧急新闻流程
curl -X POST http://localhost:5678/webhook/external-trigger \
  -H "Content-Type: application/json" \
  -d '{
    "type": "breaking_news",
    "priority": "urgent",
    "keywords": ["SpaceX", "Falcon Heavy", "launch failure"],
    "source": "NASA Alert System",
    "timestamp": "2024-01-20T10:30:00Z"
  }'
```

### 案例2: 定时内容规划

每日早上6点自动执行：

1. **定时触发** → Cron触发器激活
2. **获取趋势** → 调用外部API获取热门话题
3. **生成计划** → 基于趋势数据生成内容计划
4. **分配任务** → 根据编辑人员时段分配任务
5. **发送通知** → 通知相关人员开始工作

### 案例3: 内容质量检查

在内容发布前自动执行：

1. **内容提交** → 编辑完成内容编辑
2. **自动检查** → heroImage验证、SEO检查、拼写检查
3. **质量评分** → 基于多个维度给出质量分数
4. **条件分支** → 高质量内容自动通过，低质量内容转人工审核
5. **发布决策** → 根据检查结果决定是否发布

## 📝 最佳实践

### 1. 工作流设计原则

- **模块化**: 将复杂流程拆分为小的、可复用的子工作流
- **错误处理**: 每个关键节点都应有错误处理机制
- **日志记录**: 记录重要的执行步骤和数据变化
- **幂等性**: 确保工作流可以安全地重复执行

### 2. 性能考虑

- **并行处理**: 合理使用并行节点提高效率
- **批处理**: 对大量数据使用批处理减少API调用
- **缓存**: 缓存常用数据减少重复计算
- **超时设置**: 为所有外部调用设置合理的超时时间

### 3. 安全措施

- **权限控制**: 严格控制用户权限和工作流访问
- **数据加密**: 敏感数据使用加密存储
- **审计日志**: 记录所有重要操作的审计日志
- **定期备份**: 建立可靠的备份和恢复机制

### 4. 团队协作

- **文档化**: 为每个工作流编写清晰的文档
- **版本控制**: 使用Git管理工作流版本
- **测试环境**: 在生产环境外测试工作流变更
- **代码审查**: 重要工作流变更需要团队审查

---

通过遵循本指南，您的团队可以充分利用n8n的强大功能，构建高效、可靠的新闻内容自动化流程。 