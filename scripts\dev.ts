#!/usr/bin/env tsx

import { spawn } from 'child_process';
import { join } from 'path';

const processes: Array<{ name: string; process: any }> = [];

function startProcess(name: string, command: string, args: string[], cwd?: string) {
  console.log(`🚀 启动 ${name}...`);
  
  const childProcess = spawn(command, args, {
    cwd: cwd || join(process.cwd()),
    stdio: 'inherit',
    shell: true
  });

  childProcess.on('error', (error) => {
    console.error(`❌ ${name} 启动失败:`, error);
  });

  childProcess.on('exit', (code) => {
    console.log(`🔴 ${name} 退出，代码: ${code}`);
  });

  processes.push({ name, process: childProcess });
  return childProcess;
}

async function main() {
  console.log('🎯 启动开发环境...\n');

  // 启动共享包构建监听
  startProcess(
    'Shared Package', 
    'pnpm', 
    ['--filter', '@news-site/shared', 'dev'],
    process.cwd()
  );

  // 等待一下让共享包先构建
  await new Promise(resolve => setTimeout(resolve, 3000));

  // 启动API服务
  startProcess(
    'API Service',
    'pnpm',
    ['--filter', '@news-site/api', 'dev'],
    process.cwd()
  );

  // 启动Astro开发服务器
  startProcess(
    'Astro Web',
    'pnpm',
    ['dev:web'],
    process.cwd()
  );

  console.log('\n✅ 所有服务已启动!');
  console.log('📍 API服务: http://localhost:3002');
  console.log('📍 Web服务: http://localhost:4321');
  console.log('📚 API文档: http://localhost:3002/docs');
  console.log('\n按 Ctrl+C 停止所有服务\n');
}

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n🛑 正在关闭所有服务...');
  
  processes.forEach(({ name, process }) => {
    console.log(`🔴 关闭 ${name}`);
    process.kill('SIGTERM');
  });

  setTimeout(() => {
    console.log('👋 所有服务已关闭');
    process.exit(0);
  }, 2000);
});

main().catch(console.error); 