/**
 * 任务状态枚举
 */
export type TaskStatus = 
  | 'new'           // 新任务，待处理
  | 'processing'    // 处理中
  | 'completed'     // 已完成
  | 'failed'        // 失败
  | 'skipped'       // 跳过（如重复内容）
  | 'scheduled';    // 已调度，等待执行

/**
 * 任务来源类型
 */
export type TaskSource = 
  | 'manual'        // 手动添加
  | 'serp_trends'   // SerpAPI 趋势发现
  | 'serp_related'  // SerpAPI 相关查询
  | 'scheduled'     // 定时任务
  | 'auto_discover'; // 自动发现

/**
 * 任务优先级
 */
export type TaskPriority = 'low' | 'normal' | 'high' | 'urgent';

/**
 * 核心任务对象
 */
export interface Task {
  /** 唯一任务ID */
  id: string;
  
  /** 目标关键词 */
  keyword: string;
  
  /** 趋势评分 (0-100，可选) */
  trendScore?: number;
  
  /** 任务来源 */
  source: TaskSource;
  
  /** 当前状态 */
  status: TaskStatus;
  
  /** 优先级 */
  priority: TaskPriority;
  
  /** 创建时间 */
  createdAt: string; // ISO 8601 格式
  
  /** 更新时间 */
  updatedAt?: string; // ISO 8601 格式
  
  /** 计划执行时间 (可选) */
  scheduledFor?: string; // ISO 8601 格式
  
  /** 相关产品ID列表 */
  relatedProducts?: string[];
  
  /** 目标URL (如果已选定) */
  targetUrl?: string;
  
  /** 重试次数 */
  retryCount?: number;
  
  /** 失败原因 */
  failureReason?: string;
  
  /** 生成的文章文件路径 */
  outputPath?: string;
  
  /** 元数据 */
  metadata?: {
    /** 搜索量 */
    searchVolume?: number;
    /** 竞争度 */
    competition?: 'low' | 'medium' | 'high';
    /** 相关搜索词 */
    relatedQueries?: string[];
    /** 估计字数 */
    estimatedWordCount?: number;
    /** 目标分类 */
    category?: string;
    /** 自定义标签 */
    tags?: string[];
  };
}

/**
 * 任务创建输入
 */
export interface CreateTaskInput {
  keyword: string;
  source: TaskSource;
  priority?: TaskPriority;
  trendScore?: number;
  scheduledFor?: string;
  relatedProducts?: string[];
  targetUrl?: string;
  metadata?: Task['metadata'];
}

/**
 * 任务更新输入
 */
export interface UpdateTaskInput {
  status?: TaskStatus;
  priority?: TaskPriority;
  targetUrl?: string;
  relatedProducts?: string[];
  retryCount?: number;
  failureReason?: string;
  outputPath?: string;
  metadata?: Partial<Task['metadata']>;
}

/**
 * 任务查询过滤器
 */
export interface TaskFilter {
  status?: TaskStatus | TaskStatus[];
  source?: TaskSource | TaskSource[];
  priority?: TaskPriority | TaskPriority[];
  keyword?: string; // 模糊匹配
  createdAfter?: string; // ISO 8601
  createdBefore?: string; // ISO 8601
  hasTargetUrl?: boolean;
  minTrendScore?: number;
  maxTrendScore?: number;
}

/**
 * 任务排序选项
 */
export interface TaskSort {
  field: keyof Task;
  direction: 'asc' | 'desc';
}

/**
 * 任务查询结果
 */
export interface TaskQueryResult {
  tasks: Task[];
  total: number;
  page?: number;
  pageSize?: number;
}

/**
 * 任务统计信息
 */
export interface TaskStats {
  total: number;
  byStatus: Record<TaskStatus, number>;
  bySource: Record<TaskSource, number>;
  byPriority: Record<TaskPriority, number>;
  avgTrendScore?: number;
  recentCompletions: number; // 最近24小时完成数
  recentFailures: number; // 最近24小时失败数
}

/**
 * 批量任务操作结果
 */
export interface BatchTaskResult {
  success: number;
  failed: number;
  skipped: number;
  errors: Array<{
    taskId: string;
    error: string;
  }>;
}

/**
 * 任务执行上下文
 */
export interface TaskExecutionContext {
  task: Task;
  dryRun?: boolean; // 试运行模式
  skipDuplicateCheck?: boolean; // 跳过重复检查
  forceRegenerate?: boolean; // 强制重新生成
  customPrompt?: string; // 自定义写作提示
  imageGeneration?: boolean; // 是否生成图片
  publishImmediately?: boolean; // 是否立即发布
}

/**
 * 任务执行结果
 */
export interface TaskExecutionResult {
  taskId: string;
  success: boolean;
  outputPath?: string;
  generatedImageUrl?: string;
  wordCount?: number;
  processingTimeMs: number;
  error?: string;
  warnings?: string[];
  metadata?: {
    titleGenerated?: string;
    categoryAssigned?: string;
    seoScore?: number;
    readabilityScore?: number;
  };
} 