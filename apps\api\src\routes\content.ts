import type { FastifyInstance } from 'fastify';
import { ContentGeneratorAgent } from '../core/agents/content-generator-agent.js';

export async function contentRoutes(fastify: FastifyInstance) {
  const contentGenerator = new ContentGeneratorAgent();
  await contentGenerator.initialize();

  // 生成内容
  fastify.post('/generate', async (request, reply) => {
    try {
      const result = await contentGenerator.execute(request.body, {
        requestId: request.id,
        userId: 'api-user',
        dryRun: false,
        metadata: { source: 'api', endpoint: '/api/content/generate' }
      });

      return {
        success: true,
        data: result
      };
    } catch (error) {
      reply.code(400);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  // 批量生成内容
  fastify.post('/batch-generate', async (request, reply) => {
    try {
      const { tasks } = request.body as { tasks: any[] };
      const results: any[] = [];

      for (const taskData of tasks) {
        try {
          const result = await contentGenerator.execute(taskData, {
            requestId: `${request.id}_${taskData.taskId}`,
            userId: 'api-user',
            dryRun: false,
            metadata: { source: 'api', endpoint: '/api/content/batch-generate' }
          });
          results.push(result);
        } catch (error) {
          results.push({
            taskId: taskData.taskId,
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

      return {
        success: true,
        data: {
          total: tasks.length,
          successful: results.filter((r: any) => r.success).length,
          failed: results.filter((r: any) => !r.success).length,
          results
        }
      };
    } catch (error) {
      reply.code(400);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  // 内容生成Agent健康检查
  fastify.get('/health', async (request, reply) => {
    try {
      const health = await contentGenerator.healthCheck();
      return {
        success: true,
        data: health
      };
    } catch (error) {
      reply.code(500);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });
} 