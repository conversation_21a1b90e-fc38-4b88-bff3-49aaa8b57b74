import { BaseImageProvider, ImageGenerationRequest, ImageGenerationResult } from './base-image-provider.js';
import { SimpleWorkerImageService, createSimpleWorkerImageService, SimpleImageGenerationRequest } from './simple-worker-image-service.js';

export interface ImageServiceConfig {
  fallbackImage?: string;
  enableLogging?: boolean;
}

export interface ImageServiceStats {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
}

export class ImageService {
  private workerService: SimpleWorkerImageService;
  private config: ImageServiceConfig;
  private stats: ImageServiceStats;
  private logger?: (level: string, message: string, data?: any) => void;

  constructor(config: ImageServiceConfig, logger?: (level: string, message: string, data?: any) => void) {
    this.config = config;
    this.logger = logger;
    this.stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0
    };

    // 初始化简化的Worker服务
    this.workerService = createSimpleWorkerImageService();

    if (this.logger) {
      this.logger('info', 'Simplified Image service initialized with Worker-only provider', {});
    }
  }



  async generateImage(request: ImageGenerationRequest): Promise<ImageGenerationResult> {
    this.stats.totalRequests++;

    if (this.logger) {
      this.logger('info', 'Starting simplified image generation with Worker', {
        keyword: request.keyword,
        title: request.title,
        category: request.category
      });
    }

    try {
      // 构建简化的Worker请求
      const workerRequest: SimpleImageGenerationRequest = {
        prompt: this.workerService.buildImagePrompt(request.title, request.keyword, request.category),
        keyword: request.keyword,
        title: request.title,
        category: request.category
      };

      // 直接调用Worker服务
      const result = await this.workerService.generateImage(workerRequest);

      if (result.success) {
        this.stats.successfulRequests++;
      } else {
        this.stats.failedRequests++;
      }

      // 转换为兼容的返回格式
      return {
        success: result.success,
        url: result.url,
        provider: result.provider || 'worker',
        error: result.error,
        metadata: {
          elapsedMs: result.elapsedMs,
          r2Stored: result.r2Stored,
          r2Error: result.r2Error
        }
      };

    } catch (error) {
      this.stats.failedRequests++;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      if (this.logger) {
        this.logger('error', 'Image generation failed completely', { error: errorMessage });
      }

      // 返回默认背景图片
      return {
        success: true,
        url: this.config.fallbackImage || '/hero-bg.jpg',
        provider: 'fallback',
        error: errorMessage,
        metadata: {
          isFallback: true
        }
      };
    }
  }

  async healthCheck(): Promise<Record<string, any>> {
    // 简化的健康检查，仅检查Worker服务配置
    const workerConfigured = !!(process.env.CLOUDFLARE_WORKER_URL && process.env.IMAGE_API_KEY);

    return {
      worker: {
        healthy: workerConfigured,
        configured: workerConfigured,
        url: process.env.CLOUDFLARE_WORKER_URL ? '***configured***' : 'missing',
        apiKey: process.env.IMAGE_API_KEY ? '***configured***' : 'missing'
      },
      stats: this.stats,
      overallHealth: workerConfigured
    };
  }

  getStats(): ImageServiceStats {
    return { ...this.stats };
  }

  getConfig(): ImageServiceConfig {
    return this.config;
  }


}

// 简化的工厂函数，创建Worker-only图片服务
export function createImageService(logger?: (level: string, message: string, data?: any) => void): ImageService {
  const config: ImageServiceConfig = {
    fallbackImage: process.env.FALLBACK_IMAGE_URL || '/hero-bg.jpg',
    enableLogging: process.env.NODE_ENV !== 'production'
  };

  // 添加调试日志
  if (logger) {
    logger('info', 'Simplified ImageService configuration (Worker-only)', {
      workerUrl: process.env.CLOUDFLARE_WORKER_URL ? '***configured***' : 'missing',
      imageApiKey: process.env.IMAGE_API_KEY ? '***configured***' : 'missing',
      fallbackImage: config.fallbackImage
    });
  }

  return new ImageService(config, logger);
}

