import { TrendAnalysisService } from './trend-analysis-service.js';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

/**
 * 计划生成服务
 * 健壮的任务计划生成，集成多种热词来源
 */
export class PlanGenerationService {
  private trendService: TrendAnalysisService;
  private config: {
    defaultSourceUrls: string[];
    categories: string[];
    relatedProductsMap: Record<string, string[]>;
  };

  constructor() {
    this.trendService = new TrendAnalysisService();
    this.config = {
      defaultSourceUrls: [
        'https://defensenews.com/',
        'https://aviationweek.com/',
        'https://spacenews.com/',
        'https://aerospace.org/news',
        'https://breakingdefense.com/',
        'https://www.flightglobal.com/',
        'https://www.aerospace-technology.com/'
      ],
      categories: ['research', 'industry', 'frontier', 'misc'],
      relatedProductsMap: {
        'hypersonic': ['hypersonic-systems', 'radar-systems'],
        'space': ['satellite-communication'],
        'radar': ['radar-systems'],
        'satellite': ['satellite-communication'],
        'drone': ['hypersonic-systems', 'radar-systems'],
        'cyber': ['radar-systems'],
        'autonomous': ['hypersonic-systems', 'radar-systems'],
        'quantum': ['radar-systems'],
        'default': ['satellite-communication']
      }
    };
  }

  /**
   * 生成日度内容计划（主入口）
   */
  async generateDailyPlan(options: {
    date: string;
    maxTasks?: number;
    categories?: string[];
    forceRegenerate?: boolean;
  }): Promise<{
    id: string;
    date: string;
    status: string;
    tasks: Array<{
      id: string;
      planId: string;
      title: string;
      description: string;
      keyword: string;
      sourceUrl: string;
      targetCategory: string;
      priority: number;
      relatedProducts: string[];
      estimatedDuration: number;
      createdAt: string;
      updatedAt: string;
    }>;
    stats: any;
    metadata: {
      trendsSource: string;
      keywordsUsed: string[];
      generationTime: number;
    };
  }> {
    const startTime = Date.now();
    console.log(`📋 开始生成 ${options.date} 的内容计划...`);

    try {
      // 1. 获取热词趋势
      console.log('🔍 步骤1: 获取热词趋势');
      const trendsData = await this.trendService.getTrendingKeywords('aerospace defense');
      
      // 2. 生成任务列表
      console.log('📝 步骤2: 基于热词生成任务');
      const tasks = await this.generateTasksFromTrends(
        trendsData, 
        options.maxTasks || 5,
        options.categories || this.config.categories
      );

      // 3. 构建计划对象
      const planId = this.generatePlanId();
      const plan = {
        id: planId,
        date: options.date,
        status: 'active',
        tasks: tasks.map(task => ({
          ...task,
          planId,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        })),
        stats: {
          totalTasks: tasks.length,
          highPriorityTasks: tasks.filter(t => t.priority >= 4).length,
          categoriesDistribution: this.getCategoryDistribution(tasks)
        },
        metadata: {
          trendsSource: trendsData.source,
          keywordsUsed: trendsData.keywords.map(k => k.keyword),
          generationTime: Date.now() - startTime
        }
      };

      console.log(`✅ 计划生成完成，共 ${tasks.length} 个任务`);
      console.log(`📊 热词来源: ${trendsData.source}`);
      
      return plan;

    } catch (error) {
      console.error(`❌ 计划生成失败: ${error.message}`);
      
      // 备用计划：使用预定义模板
      console.log('🔄 使用备用计划模板...');
      return this.generateFallbackPlan(options);
    }
  }

  /**
   * 基于热词趋势生成任务
   */
  private async generateTasksFromTrends(
    trendsData: any, 
    maxTasks: number, 
    categories: string[]
  ): Promise<any[]> {
    const tasks = [];
    const availableCategories = [...categories];
    
    // 从热词中选择最相关的几个
    const selectedKeywords = trendsData.keywords
      .filter((k: any) => k.relevance === 'high' || k.relevance === 'medium')
      .slice(0, maxTasks);

    for (let i = 0; i < Math.min(selectedKeywords.length, maxTasks); i++) {
      const keyword = selectedKeywords[i];
      const category = availableCategories[i % availableCategories.length];
      
      const task = {
        id: this.generateTaskId(),
        title: this.generateTaskTitle(keyword.keyword),
        description: this.generateTaskDescription(keyword.keyword),
        keyword: keyword.keyword,
        sourceUrl: this.selectSourceUrl(keyword.keyword),
        targetCategory: category,
        priority: this.calculatePriority(keyword),
        relatedProducts: this.getRelatedProducts(keyword.keyword),
        estimatedDuration: this.estimateDuration(keyword.keyword)
      };

      tasks.push(task);
    }

    return tasks;
  }

  /**
   * 生成任务标题
   */
  private generateTaskTitle(keyword: string): string {
    const titleTemplates = [
      `The Future of ${this.capitalize(keyword)}: Latest Developments and Trends`,
      `Breaking Ground: Advances in ${this.capitalize(keyword)} Technology`, 
      `${this.capitalize(keyword)} Revolution: Industry Impact and Analysis`,
      `Next-Gen ${this.capitalize(keyword)}: Innovation and Applications`,
      `${this.capitalize(keyword)} Breakthrough: Technical Analysis and Implications`
    ];

    return titleTemplates[Math.floor(Math.random() * titleTemplates.length)];
  }

  /**
   * 生成任务描述
   */
  private generateTaskDescription(keyword: string): string {
    return `Comprehensive analysis of ${keyword} technology developments, industry applications, and future implications in aerospace and defense sectors.`;
  }

  /**
   * 选择合适的源URL
   */
  private selectSourceUrl(keyword: string): string {
    // 根据关键词类型选择最相关的新闻源
    if (keyword.includes('space') || keyword.includes('satellite')) {
      return 'https://spacenews.com/';
    } else if (keyword.includes('defense') || keyword.includes('military')) {
      return 'https://defensenews.com/';
    } else if (keyword.includes('aviation') || keyword.includes('aircraft')) {
      return 'https://aviationweek.com/';
    } else {
      // 随机选择一个默认源
      const randomIndex = Math.floor(Math.random() * this.config.defaultSourceUrls.length);
      return this.config.defaultSourceUrls[randomIndex];
    }
  }

  /**
   * 获取相关产品
   */
  private getRelatedProducts(keyword: string): string[] {
    for (const [key, products] of Object.entries(this.config.relatedProductsMap)) {
      if (keyword.toLowerCase().includes(key)) {
        return products;
      }
    }
    return this.config.relatedProductsMap.default;
  }

  /**
   * 计算任务优先级
   */
  private calculatePriority(keyword: any): number {
    switch (keyword.relevance) {
      case 'high': return Math.floor(Math.random() * 2) + 4; // 4-5
      case 'medium': return Math.floor(Math.random() * 2) + 2; // 2-3  
      case 'low': return 1;
      default: return 3;
    }
  }

  /**
   * 估算任务持续时间（秒）
   */
  private estimateDuration(keyword: string): number {
    const baseTime = 600; // 10分钟基础时间
    const complexity = keyword.split(' ').length; // 关键词复杂度
    return baseTime + (complexity * 100) + Math.floor(Math.random() * 300);
  }

  /**
   * 备用计划生成
   */
  private generateFallbackPlan(options: any): any {
    console.log('📋 生成备用计划（使用预定义模板）');
    
    const fallbackTasks = [
      {
        keyword: 'hypersonic technology',
        category: 'industry',
        priority: 5
      },
      {
        keyword: 'space economy',
        category: 'research', 
        priority: 4
      },
      {
        keyword: 'autonomous defense',
        category: 'frontier',
        priority: 3
      }
    ];

    const planId = this.generatePlanId();
    const tasks = fallbackTasks.slice(0, options.maxTasks || 3).map(template => ({
      id: this.generateTaskId(),
      planId,
      title: this.generateTaskTitle(template.keyword),
      description: this.generateTaskDescription(template.keyword),
      keyword: template.keyword,
      sourceUrl: this.selectSourceUrl(template.keyword),
      targetCategory: template.category,
      priority: template.priority,
      relatedProducts: this.getRelatedProducts(template.keyword),
      estimatedDuration: this.estimateDuration(template.keyword),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }));

    return {
      id: planId,
      date: options.date,
      status: 'active',
      tasks,
      stats: {
        totalTasks: tasks.length,
        highPriorityTasks: tasks.filter(t => t.priority >= 4).length,
        categoriesDistribution: this.getCategoryDistribution(tasks)
      },
      metadata: {
        trendsSource: 'Fallback Template',
        keywordsUsed: fallbackTasks.map(t => t.keyword),
        generationTime: 0
      }
    };
  }

  /**
   * 工具方法
   */
  private generatePlanId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateTaskId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private capitalize(str: string): string {
    return str.split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  }

  private getCategoryDistribution(tasks: any[]): Record<string, number> {
    const distribution: Record<string, number> = {};
    tasks.forEach(task => {
      distribution[task.targetCategory] = (distribution[task.targetCategory] || 0) + 1;
    });
    return distribution;
  }
} 