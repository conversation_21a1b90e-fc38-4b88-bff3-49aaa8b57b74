# 🚀 Lightsail完整自动化部署文档

## 📋 目录
1. [环境准备](#环境准备)
2. [项目部署](#项目部署)
3. [环境变量配置](#环境变量配置)
4. [自动化脚本](#自动化脚本)
5. [定时任务设置](#定时任务设置)
6. [测试验证](#测试验证)

---

## 🛠️ 环境准备

### 第1步：登录Lightsail并切换用户
```bash
# 登录到Lightsail实例
ssh -i your-key.pem ubuntu@your-lightsail-ip

# 确保在ubuntu用户下操作
sudo su - ubuntu
cd /home/<USER>
```

### 第2步：安装Node.js和包管理器
```bash
# 安装fnm (Fast Node Manager)
curl -fsSL https://fnm.vercel.app/install | bash

# 重新加载环境
source ~/.bashrc

# 安装并使用指定Node.js版本
fnm use --install-if-missing 22.14.0

# 验证Node.js版本
node --version  # 应显示 v22.14.0

# 安装全局工具
sudo npm install -g pnpm pm2

# 验证安装
pnpm --version
pm2 --version
```

---

## 📦 项目部署

### 第3步：克隆项目
```bash
# 克隆项目到服务器
cd /home/<USER>
git clone <your-repository-url> news-site
cd news-site

# 安装项目依赖
pnpm install

# 验证项目结构
ls -la
```

---

## 🔧 环境变量配置

### 第4步：创建环境配置文件
```bash
# 在项目根目录创建.env文件
nano /home/<USER>/news-site/.env
```

**添加以下内容**：
```bash
# Vercel部署配置
VERCEL_TOKEN=your_vercel_token_here
VERCEL_ORG_ID=your_org_id_here
VERCEL_PROJECT_ID=your_project_id_here

# OpenAI API
OPENAI_API_KEY=your_openai_api_key_here

# 搜索和爬虫API
SERP_API_KEY=your_serp_api_key_here
TAVILY_API_KEY=your_tavily_api_key_here
FIRECRAWL_API_KEY=your_firecrawl_api_key_here
NEWS_API_KEY=your_news_api_key_here

# 图片生成API
FAL_KEY=your_fal_key_here
UNSPLASH_ACCESS_KEY=your_unsplash_access_key_here

# API服务配置
API_KEY=production-api-key-change-me
PORT=3002

# 可选：模拟模式（设为false使用真实API）
USE_MOCK=false
```

**保存文件**：`Ctrl+X`, `Y`, `Enter`

---

## 📝 自动化脚本

### 第5步：创建日志目录（需要sudo）
```bash
# 创建日志目录和文件
sudo mkdir -p /var/log
sudo touch /var/log/news-automation.log
sudo chown ubuntu:ubuntu /var/log/news-automation.log
```

### 第6步：创建自动化脚本
```bash
# 创建自动化脚本
nano /home/<USER>/daily-automation.sh
```

**添加以下完整脚本内容**：

```bash
#!/bin/bash

# =================================
# 新闻站点每日自动化脚本
# 版本: 2.0
# 功能: 内容生成 -> 图片处理 -> 静态构建 -> Vercel部署
# =================================

set -e  # 出错时立即退出

# 配置变量
PROJECT_DIR="/home/<USER>/news-site"
LOG_FILE="/var/log/news-automation.log"
API_PORT=3002
MAX_RETRIES=3
RETRY_DELAY=10

# 获取当前时间
get_timestamp() {
    date '+%Y-%m-%d %H:%M:%S'
}

# 日志函数
log() {
    local timestamp=$(get_timestamp)
    echo "[$timestamp] $1" | tee -a $LOG_FILE
}

log_info() {
    log "ℹ️  INFO: $1"
}

log_success() {
    log "✅ SUCCESS: $1"
}

log_error() {
    log "❌ ERROR: $1"
}

log_warning() {
    log "⚠️  WARNING: $1"
}

# 错误处理函数
handle_error() {
    log_error "$1"
    
    # 可选：发送错误通知
    # curl -X POST "$SLACK_WEBHOOK_URL" \
    #      -H 'Content-Type: application/json' \
    #      -d "{\"text\":\"❌ News automation failed: $1\"}" || true
    
    exit 1
}

# 重试执行函数
retry_command() {
    local cmd="$1"
    local description="$2"
    local retries=0
    
    while [ $retries -lt $MAX_RETRIES ]; do
        log_info "Attempting: $description (try $((retries + 1))/$MAX_RETRIES)"
        
        if eval "$cmd"; then
            log_success "$description completed"
            return 0
        else
            retries=$((retries + 1))
            if [ $retries -lt $MAX_RETRIES ]; then
                log_warning "$description failed, retrying in ${RETRY_DELAY}s..."
                sleep $RETRY_DELAY
            fi
        fi
    done
    
    handle_error "$description failed after $MAX_RETRIES attempts"
}

# 检查API服务健康状态
check_api_health() {
    log_info "Checking API service health..."
    
    local retries=0
    while [ $retries -lt 30 ]; do  # 最多等待5分钟
        if curl -f -s http://localhost:$API_PORT/health > /dev/null 2>&1; then
            log_success "API service is healthy"
            return 0
        fi
        
        retries=$((retries + 1))
        log_info "API not ready, waiting... ($retries/30)"
        sleep 10
    done
    
    handle_error "API service health check failed"
}

# 启动API服务
start_api_service() {
    log_info "Starting API service..."
    
    # 检查是否已经运行
    if curl -f -s http://localhost:$API_PORT/health > /dev/null 2>&1; then
        log_info "API service is already running"
        return 0
    fi
    
    # 切换到API目录
    cd "$PROJECT_DIR/apps/api"
    
    # 使用PM2启动服务
    if command -v pm2 > /dev/null 2>&1; then
        log_info "Starting API service with PM2..."
        pm2 delete news-api 2>/dev/null || true  # 删除可能存在的旧进程
        pm2 start "pnpm start" --name "news-api" --log /var/log/news-api.log
    else
        log_info "Starting API service in background..."
        nohup pnpm dev:api > /var/log/news-api.log 2>&1 &
    fi
    
    # 等待服务启动
    sleep 15
    check_api_health
}

# 停止API服务
stop_api_service() {
    log_info "Stopping API service..."
    
    if command -v pm2 > /dev/null 2>&1; then
        pm2 delete news-api 2>/dev/null || true
    else
        pkill -f "pnpm dev:api" || true
    fi
    
    log_success "API service stopped"
}

# 更新代码
update_code() {
    log_info "Checking for code updates..."
    
    cd "$PROJECT_DIR"
    
    # 获取远程更新
    git fetch origin
    
    local local_commit=$(git rev-parse HEAD)
    local remote_commit=$(git rev-parse origin/main)
    
    if [ "$local_commit" != "$remote_commit" ]; then
        log_info "New changes detected, pulling updates..."
        git pull origin main
        
        log_info "Installing/updating dependencies..."
        pnpm install
        
        log_success "Code updated successfully"
        return 0
    else
        log_info "Code is up to date"
        return 1
    fi
}

# 运行完整自动化
run_automation() {
    log_info "Starting full automation process..."
    
    cd "$PROJECT_DIR"
    
    # 运行自动化脚本
    if node scripts/comprehensive-automation-test.js --full; then
        log_success "Automation process completed successfully"
        return 0
    else
        handle_error "Automation process failed"
    fi
}

# 清理日志文件
cleanup_logs() {
    log_info "Cleaning up old logs..."
    
    # 保留最近7天的日志
    find /var/log -name "news-*.log" -mtime +7 -delete 2>/dev/null || true
    
    # 截断主日志文件如果太大（>10MB）
    if [ -f "$LOG_FILE" ] && [ $(stat -c%s "$LOG_FILE") -gt 10485760 ]; then
        tail -n 1000 "$LOG_FILE" > "${LOG_FILE}.tmp"
        mv "${LOG_FILE}.tmp" "$LOG_FILE"
        log_info "Log file truncated to last 1000 lines"
    fi
}

# 系统状态检查
system_check() {
    log_info "Performing system checks..."
    
    # 检查磁盘空间
    local disk_usage=$(df /home | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ "$disk_usage" -gt 85 ]; then
        log_warning "Disk usage is high: ${disk_usage}%"
    fi
    
    # 检查内存使用
    local memory_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    if [ "$memory_usage" -gt 90 ]; then
        log_warning "Memory usage is high: ${memory_usage}%"
    fi
    
    # 检查必要的环境变量
    local required_vars=("VERCEL_TOKEN" "OPENAI_API_KEY" "SERP_API_KEY")
    for var in "${required_vars[@]}"; do
        if [ -z "${!var:-}" ]; then
            cd "$PROJECT_DIR"
            source .env 2>/dev/null || true
            if [ -z "${!var:-}" ]; then
                handle_error "Required environment variable $var is not set"
            fi
        fi
    done
    
    log_success "System checks passed"
}

# 发送完成通知
send_notification() {
    local status="$1"
    local message="$2"
    
    # 可选：发送成功通知到Slack/Discord/邮件等
    # if [ -n "$SLACK_WEBHOOK_URL" ]; then
    #     local emoji="✅"
    #     [ "$status" != "success" ] && emoji="❌"
    #     
    #     curl -X POST "$SLACK_WEBHOOK_URL" \
    #          -H 'Content-Type: application/json' \
    #          -d "{\"text\":\"$emoji News Automation: $message\"}" || true
    # fi
    
    log_info "Notification: $status - $message"
}

# 主函数
main() {
    local start_time=$(date +%s)
    
    log_success "=== Daily News Automation Started ==="
    log_info "Timestamp: $(get_timestamp)"
    log_info "Project: $PROJECT_DIR"
    log_info "User: $(whoami)"
    log_info "Node Version: $(node --version)"
    
    # 执行系统检查
    system_check
    
    # 清理旧日志
    cleanup_logs
    
    # 更新代码
    local code_updated=false
    if update_code; then
        code_updated=true
        # 如果代码更新了，重启API服务
        stop_api_service
    fi
    
    # 启动API服务
    start_api_service
    
    # 运行自动化流程
    run_automation
    
    # 计算执行时间
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    local duration_formatted=$(printf "%02d:%02d:%02d" $((duration/3600)) $((duration%3600/60)) $((duration%60)))
    
    log_success "=== Daily News Automation Completed ==="
    log_info "Total execution time: $duration_formatted"
    log_info "Code updated: $code_updated"
    
    # 发送完成通知
    send_notification "success" "Daily automation completed in $duration_formatted"
}

# 信号处理
trap 'log_error "Script interrupted"; exit 130' INT TERM

# 执行主函数
main "$@"
```

**保存文件**：`Ctrl+X`, `Y`, `Enter`

### 第7步：设置脚本权限
```bash
# 设置执行权限
chmod +x /home/<USER>/daily-automation.sh

# 验证权限
ls -la /home/<USER>/daily-automation.sh
```

---

## ⏰ 定时任务设置

### 第8步：配置Cron定时任务
```bash
# 编辑crontab
crontab -e

# 如果是第一次，选择编辑器（推荐选择nano）
```

**添加以下定时任务**：
```bash
# 每天上午9点执行自动化
0 9 * * * /home/<USER>/daily-automation.sh >> /var/log/news-automation.log 2>&1

# 或者每12小时执行一次（上午9点和晚上9点）
# 0 9,21 * * * /home/<USER>/daily-automation.sh >> /var/log/news-automation.log 2>&1

# 每天凌晨2点清理日志
0 2 * * * find /var/log -name "news-*.log" -mtime +7 -delete 2>/dev/null
```

**保存crontab**：`Ctrl+X`, `Y`, `Enter`

### 第9步：验证定时任务
```bash
# 查看当前定时任务
crontab -l

# 检查cron服务状态
sudo systemctl status cron
```

---

## 🧪 测试验证

### 第10步：手动测试
```bash
# 手动运行一次自动化脚本
/home/<USER>/daily-automation.sh

# 实时查看日志
tail -f /var/log/news-automation.log
```

### 第11步：验证API服务
```bash
# 检查API服务状态
curl http://localhost:3002/health

# 查看PM2进程状态
pm2 status

# 查看API日志
pm2 logs news-api
```

### 第12步：验证Vercel部署
在脚本运行完成后，应该能看到：
- 新内容生成
- 静态站点构建
- 自动部署到Vercel
- 部署URL输出

---

## 📊 监控和维护

### 查看日志
```bash
# 查看自动化日志
tail -f /var/log/news-automation.log

# 查看最近的错误
grep "ERROR" /var/log/news-automation.log | tail -20

# 查看API日志
tail -f /var/log/news-api.log
```

### 手动操作
```bash
# 手动停止API服务
pm2 delete news-api

# 手动启动API服务
cd /home/<USER>/news-site/apps/api
pm2 start "pnpm start" --name "news-api"

# 重启服务
pm2 restart news-api

# 查看服务状态
pm2 status
```

### 故障排除
```bash
# 检查环境变量
cd /home/<USER>/news-site
cat .env

# 检查Node.js版本
node --version

# 检查项目依赖
pnpm list

# 重新安装依赖
pnpm install

# 手动构建测试
cd apps/web
pnpm build
```

---

## 🔐 安全建议

1. **环境变量保护**：
   ```bash
   # 设置.env文件权限
   chmod 600 /home/<USER>/news-site/.env
   ```

2. **定期更新**：
   ```bash
   # 定期更新系统
   sudo apt update && sudo apt upgrade
   
   # 更新Node.js和pnpm
   fnm install --latest
   npm install -g pnpm@latest
   ```

3. **备份重要数据**：
   - 定期备份环境变量文件
   - 备份生成的内容
   - 备份日志文件

---

## 🎯 完整流程总结

1. ✅ **环境准备**：Node.js + pnpm + pm2
2. ✅ **项目部署**：克隆代码 + 安装依赖
3. ✅ **环境配置**：创建.env文件
4. ✅ **脚本创建**：自动化脚本 + 权限设置
5. ✅ **定时任务**：配置cron + 验证
6. ✅ **测试验证**：手动运行 + 监控日志

现在您拥有了一个**完全自动化的新闻站点生成和部署系统**！🚀

---

## 📱 快速参考命令

### 常用操作
```bash
# 查看自动化状态
tail -f /var/log/news-automation.log

# 手动运行自动化
/home/<USER>/daily-automation.sh

# 重启API服务
pm2 restart news-api

# 查看定时任务
crontab -l

# 检查API健康状态
curl http://localhost:3002/health
```

### 紧急故障处理
```bash
# 停止所有相关进程
pm2 delete all
pkill -f "pnpm"

# 重新启动整个系统
cd /home/<USER>/news-site
pnpm install
/home/<USER>/daily-automation.sh
```

---

**部署完成后，您的新闻站点将实现完全自动化的内容生成和发布流程！** 🌟
