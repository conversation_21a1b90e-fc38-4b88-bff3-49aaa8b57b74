import { z, defineCollection } from 'astro:content';
import { glob } from 'astro/loaders';

const news = defineCollection({
  loader: glob({ pattern: '**/*.{md,mdx}', base: './src/content/news' }),
  schema: z.object({
    title: z.string(),
    description: z.string().optional(),
    date: z.union([
      z.string().transform((str) => new Date(str)),
      z.date()
    ]),
    category: z.enum(['industry', 'research', 'events', 'frontier', 'insight', 'misc']),
    tags: z.array(z.string()).default([]),
    author: z.string().default('Editorial Team'),
    featured: z.boolean().default(false),
    draft: z.boolean().default(false),
    heroImage: z.string().optional(),
    relatedProducts: z.array(z.string()).default([]),
    lang: z.string().default('en'),
    readTime: z.number().optional(),
    summary: z.string().optional()
  })
});

export const collections = {
  news,
}; 