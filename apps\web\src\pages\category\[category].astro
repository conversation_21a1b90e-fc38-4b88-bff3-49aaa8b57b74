---
import { getCollection } from 'astro:content';
import BaseLayout from '../../layouts/BaseLayout.astro';
import Header from '../../components/Header.astro';
import Footer from '../../components/Footer.astro';

export async function getStaticPaths() {
  const allPosts = await getCollection('news', ({ data }) => {
    return data.draft !== true;
  });

  // 定义所有预期的分类，确保即使没有文章也能生成路由
  const predefinedCategories = ['industry', 'research', 'events', 'frontier', 'insight', 'misc'];

  // 获取实际存在的分类
  const existingCategories = [...new Set(allPosts.map(post => post.data.category))];

  // 合并预定义分类和实际分类，确保所有分类都有路由
  const allCategories = [...new Set([...predefinedCategories, ...existingCategories])];

  return allCategories.map(category => {
    const categoryPosts = allPosts
      .filter(post => post.data.category === category)
      .sort((a, b) => b.data.date.valueOf() - a.data.date.valueOf());

    return {
      params: { category },
      props: {
        category,
        posts: categoryPosts,
        totalPosts: categoryPosts.length
      }
    };
  });
}

const { category, posts, totalPosts } = Astro.props;

// Define the category type for TypeScript
type CategoryType = 'industry' | 'research' | 'events' | 'frontier' | 'insight' | 'misc';

const categoryInfo: Record<CategoryType, {
  title: string;
  description: string;
  icon: string;
  gradient: string;
  bgColor: string;
  accentColor: string;
}> = {
  industry: {
    title: 'Industry News',
    description: 'Latest developments and company news from the aerospace and defense industry',
    icon: '🏭',
    gradient: 'from-blue-500 to-blue-700',
    bgColor: 'bg-blue-50',
    accentColor: 'text-blue-600'
  },
  research: {
    title: 'Research & Development',
    description: 'Cutting-edge research and technical breakthroughs in aerospace and defense',
    icon: '🔬',
    gradient: 'from-green-500 to-green-700',
    bgColor: 'bg-green-50',
    accentColor: 'text-green-600'
  },
  events: {
    title: 'Events & Conferences',
    description: 'Coverage of trade shows, conferences, and industry events',
    icon: '📅',
    gradient: 'from-purple-500 to-purple-700',
    bgColor: 'bg-purple-50',
    accentColor: 'text-purple-600'
  },
  frontier: {
    title: 'Technology Frontier',
    description: 'Emerging technologies and future innovations shaping the industry',
    icon: '🚀',
    gradient: 'from-red-500 to-red-700',
    bgColor: 'bg-red-50',
    accentColor: 'text-red-600'
  },
  insight: {
    title: 'Expert Insights',
    description: 'Analysis and commentary from industry experts and thought leaders',
    icon: '💡',
    gradient: 'from-yellow-500 to-yellow-700',
    bgColor: 'bg-yellow-50',
    accentColor: 'text-yellow-600'
  },
  misc: {
    title: 'Other News',
    description: 'Additional news and updates from the aerospace and defense sectors',
    icon: '📰',
    gradient: 'from-gray-500 to-gray-700',
    bgColor: 'bg-gray-50',
    accentColor: 'text-gray-600'
  }
};

const info = categoryInfo[category as CategoryType] || {
  title: (category as string).charAt(0).toUpperCase() + (category as string).slice(1),
  description: `News and updates in the ${category} category`,
  icon: '📄',
  gradient: 'from-primary-500 to-primary-700',
  bgColor: 'bg-primary-50',
  accentColor: 'text-primary-600'
};
---

<BaseLayout 
  title={`${info.title} | Aerospace & Defense News`}
  description={info.description}
>
  <Header />
  
  <!-- Hero Section -->
  <section class={`relative ${info.bgColor} overflow-hidden`}>
    <div class="absolute inset-0 bg-grid-white/[0.05] bg-[size:20px_20px]"></div>
    <div class="container-page section-spacing relative">
      <div class="text-center max-w-4xl mx-auto animate-fade-in">
        <!-- Category Icon -->
        <div class={`w-24 h-24 mx-auto mb-6 bg-gradient-to-br ${info.gradient} rounded-3xl flex items-center justify-center text-white text-5xl shadow-strong animate-scale-in`}>
          {info.icon}
        </div>
        
        <!-- Category Title -->
        <h1 class="heading-xl text-secondary-900 mb-6">
          {info.title}
        </h1>
        
        <!-- Description -->
        <p class="body-lg text-secondary-600 mb-8 max-w-3xl mx-auto">
          {info.description}
        </p>
        
        <!-- Stats -->
        <div class="flex items-center justify-center space-x-6 text-sm">
          <div class="flex items-center space-x-2">
            <span class="badge badge-primary">
              {totalPosts} articles
            </span>
          </div>
          <div class="flex items-center space-x-2 text-secondary-500">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>Updated regularly</span>
          </div>
        </div>
      </div>
    </div>
  </section>

  <main class="bg-white">
    <!-- Breadcrumb -->
    <div class="container-page py-6">
      <nav aria-label="Breadcrumb">
        <ol class="flex items-center space-x-2 text-sm text-secondary-500">
          <li>
            <a href="/" class="nav-link">Home</a>
          </li>
          <li>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
          </li>
          <li class="text-secondary-900 font-medium capitalize" aria-current="page">
            {category}
          </li>
        </ol>
      </nav>
    </div>

    <!-- Articles Section -->
    <section class="container-page pb-20">
      {posts.length > 0 ? (
        <div class="grid-cards">
          {posts.map((post, index) => (
            <article class="card-interactive group animate-fade-in" style={`animation-delay: ${index * 100}ms`}>
              {post.data.heroImage && (
                <div class="aspect-ratio-16-9 overflow-hidden rounded-t-xl">
                  <img 
                    src={post.data.heroImage}
                    alt={post.data.title}
                    class="object-cover group-hover:scale-105 transition-transform duration-500"
                    loading="lazy"
                  />
                </div>
              )}
              
              <div class="p-6">
                <!-- Meta info -->
                <div class="flex items-center justify-between mb-4">
                  <time class="text-sm text-secondary-500" datetime={new Date(post.data.date).toISOString()}>
                    {new Date(post.data.date).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long', 
                      day: 'numeric'
                    })}
                  </time>
                  {post.data.featured && (
                    <span class="badge badge-accent">
                      Featured
                    </span>
                  )}
                </div>
                
                <!-- Title -->
                <h2 class="text-xl font-bold text-secondary-900 mb-3 line-clamp-2 group-hover:text-primary-600 transition-colors">
                  <a href={`/news/${post.id}`} class="focus-outline">
                    {post.data.title}
                  </a>
                </h2>
                
                <!-- Description -->
                {post.data.description && (
                  <p class="text-secondary-600 mb-4 line-clamp-3">
                    {post.data.description}
                  </p>
                )}
                
                <!-- Footer -->
                <div class="flex items-center justify-between">
                  <div class="flex flex-wrap gap-1">
                    {post.data.tags && post.data.tags.slice(0, 2).map(tag => (
                      <span class="bg-secondary-100 text-secondary-600 text-xs px-2 py-1 rounded-full">
                        #{tag}
                      </span>
                    ))}
                  </div>
                  
                  <a 
                    href={`/news/${post.id}`}
                    class="inline-flex items-center text-primary-600 hover:text-primary-700 font-semibold text-sm transition-colors group"
                  >
                    Read more
                    <svg class="w-3 h-3 ml-1 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                  </a>
                </div>
              </div>
            </article>
          ))}
        </div>
      ) : (
        <!-- Empty State -->
        <div class="text-center py-20 animate-fade-in">
          <div class="w-24 h-24 mx-auto mb-6 bg-secondary-100 rounded-3xl flex items-center justify-center text-4xl">
            📝
          </div>
          <h2 class="heading-md text-secondary-900 mb-4">No articles yet</h2>
          <p class="body-lg text-secondary-600 mb-8 max-w-2xl mx-auto">
            We're working on bringing you the latest {category} news. Check back soon for updates!
          </p>
          <a href="/" class="btn-primary">
            Browse other categories
          </a>
        </div>
      )}

      <!-- Load More -->
      {posts.length > 12 && (
        <div class="text-center mt-12">
          <button 
            class="btn-secondary"
            onclick="loadMoreArticles()"
          >
            Load More Articles
          </button>
        </div>
      )}
    </section>

    <!-- Related Categories -->
    {posts.length > 0 && (
      <section class="bg-secondary-50 section-spacing">
        <div class="container-page">
          <h3 class="heading-md text-secondary-900 mb-8 text-center">Explore Other Categories</h3>
          <div class="grid-features">
            {Object.entries(categoryInfo)
              .filter(([cat]) => cat !== category)
              .map(([cat, catInfo], index) => (
                <a 
                  href={`/category/${cat}`}
                  class="group card-interactive animate-fade-in"
                  style={`animation-delay: ${index * 100}ms`}
                >
                  <div class="p-8 text-center">
                    <div class={`w-16 h-16 mx-auto mb-4 bg-gradient-to-br ${catInfo.gradient} rounded-2xl flex items-center justify-center text-white text-3xl shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110`}>
                      {catInfo.icon}
                    </div>
                    <h4 class="text-lg font-bold text-secondary-900 mb-2 group-hover:text-primary-600 transition-colors">
                      {catInfo.title}
                    </h4>
                    <p class="text-secondary-600 text-sm">
                      {catInfo.description}
                    </p>
                  </div>
                </a>
              ))
            }
          </div>
        </div>
      </section>
    )}
  </main>

  <Footer />
</BaseLayout>

<style>
  /* Grid background pattern */
  .bg-grid-white\/\[0\.05\] {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' width='32' height='32' fill='none' stroke='rgb(255 255 255 / 0.05)'%3e%3cpath d='m0 .5h32m-32 32v-32'/%3e%3c/svg%3e");
  }

  /* Line clamp utilities */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Animation for staggered entrance */
  .animate-fade-in {
    animation: fadeIn 0.6s ease-out both;
  }
  
  @media (prefers-reduced-motion: reduce) {
    .animate-fade-in {
      animation: none;
    }
  }
</style>

<script>
  function loadMoreArticles() {
    // TODO: Implement pagination/load more functionality
    console.log('Loading more articles...');
    // This could trigger a fetch request to load additional articles
    // or show more items from the existing data
  }
</script> 