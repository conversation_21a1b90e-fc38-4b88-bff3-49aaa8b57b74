### 项目需求（简洁版）

1. **统一的服务端核心**

   * 单仓库、全 TypeScript、基于 **Fastify + BullMQ**。
   * 内置 `node-cron` 自驱；n8n 仅保留少量 HTTP 工作流作「人工协作开关」。

2. **Agent-驱动的内容流水线**

   * `keywordAgent`　→　`gatherAgent`　→　`writerAgent`。
   * LangChain Tools 独立文件，随时可增删（Firecrawl、NewsAPI、未来 Twitter…）。
   * Writer Agent 生成 **Markdown + front-matter**，提交到 GitHub。

3. **可热插拔的「图片模块」** （重点）

   ```
   primary  ➜  fallback1  ➜  fallback2
   Workers  ➜  fal.ai     ➜  Unsplash
   ```

   * **接口规范**：输入 `prompt | keyword`；输出 `{ url, provider }`。
   * 每个实现放 `tools/image/*.ts`，在 `imageRouter.ts` 顺序调用，配置化选择。
   * 将来要换 Stable Diffusion、本地生成，只需新增一个实现并在 config 切换顺序，无需改其他代码。

4. **文件/任务管理**

   * 计划与任务共用 `tasks/YYYY-MM-DD.json`；状态：`planned → writing → done/error`。
   * `/v1/plan/generate` 覆盖写文件；`/v1/tasks/append` 追加或更新。

5. **发布链路**

   * GitHub PR → GitHub Actions → `astro build` → **Vercel Pages**（静态 HTML）。
   * Workers 仅用于图像 CDN 或轻量边缘逻辑，不承载写作/抓取。

> **目标**：任何“改图”需求都只触动 `tools/image/` 子目录；任何“写作策略”只触动 `agents/`；流程编排与部署保持不变。
