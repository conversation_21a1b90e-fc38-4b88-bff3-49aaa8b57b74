{"$schema": "https://turbo.build/schema.json", "globalDependencies": [".env", ".env.local"], "pipeline": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**", ".astro/**"]}, "dev": {"cache": false, "persistent": true}, "lint": {"dependsOn": ["^lint"], "outputs": []}, "type-check": {"dependsOn": ["^type-check"], "outputs": []}, "test": {"dependsOn": ["^build"], "outputs": ["coverage/**"]}, "clean": {"cache": false}}}