# Ubuntu 系统部署修复总结

## 修复的问题

### 1. TypeScript 类型错误 - fastify.log 方法参数问题
- **问题**: `fastify.log` 方法只接受单个字符串参数
- **影响文件**: `apps/api/src/routes/v1/content.ts`, `image.ts`, `plan.ts`
- **修复**: 将所有 `fastify.log` 调用改为传递单个字符串参数

### 2. shared 包类型声明问题 ⚠️ **新发现的问题**
- **问题**: `packages/shared/src/index.ts` 中的导入路径使用了 `.js` 扩展名
- **影响**: 导致 TypeScript 无法找到对应的类型声明
- **错误信息**: `Cannot find the corresponding type declarations`

#### 修复内容：
**packages/shared/src/index.ts**
```typescript
// 修复前（错误）：
export * from './types/api.js';
export * from './utils/constants.js';
export * from './utils/validation.js';
export * from './utils/frontmatter.js';

// 修复后（正确）：
export * from './types/api';
export * from './utils/constants';
export * from './utils/validation';
export * from './utils/frontmatter';
```

**原因**: 
- 在 TypeScript 源文件中，应该使用不带扩展名的导入路径
- TypeScript 编译器会自动处理扩展名
- 使用 `.js` 扩展名会导致类型声明文件生成错误

## Ubuntu 部署步骤

### 方法1: 使用自动化脚本（推荐）

1. **更新代码**:
   ```bash
   cd /home/<USER>/news-site
   git pull origin main
   ```

2. **运行构建脚本**:
   ```bash
   chmod +x ubuntu-build.sh
   ./ubuntu-build.sh
   ```

3. **启动服务**:
   ```bash
   cd apps/api
   export PORT=3003  # 如果 3002 被占用
   pnpm start
   ```

### 方法2: 手动步骤

1. **清理环境**:
   ```bash
   cd /home/<USER>/news-site
   rm -rf packages/shared/dist
   rm -rf apps/api/dist
   ```

2. **安装系统依赖**（首次部署）:
   ```bash
   sudo apt update
   sudo apt install -y build-essential python3 python3-dev libsqlite3-dev
   ```

3. **配置 pnpm**（首次部署）:
   ```bash
   pnpm config set auto-install-peers true
   pnpm config set shamefully-hoist true
   pnpm approve-builds
   ```

4. **安装项目依赖**:
   ```bash
   pnpm install
   ```

5. **按正确顺序构建**:
   ```bash
   # 先构建 shared 包
   cd packages/shared
   pnpm run build
   
   # 验证 shared 包构建结果
   ls -la dist/  # 应该看到 index.js 和 index.d.ts
   
   # 再构建 API
   cd ../../apps/api
   pnpm run build
   
   # 验证 API 构建结果
   ls -la dist/  # 应该看到 index.js
   ```

6. **启动服务**:
   ```bash
   # 如果端口3002被占用，使用其他端口
   export PORT=3003
   pnpm start
   # 或者直接运行
   node dist/index.js
   ```

## 验证清单

构建成功后，检查以下文件是否存在：

### shared 包
- ✅ `packages/shared/dist/index.js`
- ✅ `packages/shared/dist/index.d.ts`
- ✅ `packages/shared/dist/types/api.js`
- ✅ `packages/shared/dist/types/api.d.ts`
- ✅ `packages/shared/dist/utils/frontmatter.js`
- ✅ `packages/shared/dist/utils/frontmatter.d.ts`

### API 包
- ✅ `apps/api/dist/index.js`
- ✅ `apps/api/dist/index.d.ts`
- ✅ 其他所有路由和服务文件

## 常见问题排查

### 1. 如果仍然出现 "Cannot find the corresponding type declarations" 错误：
```bash
# 删除所有 node_modules 和构建文件
rm -rf node_modules apps/*/node_modules packages/*/node_modules
rm -rf packages/*/dist apps/*/dist
rm -f pnpm-lock.yaml

# 重新安装和构建
pnpm install
./ubuntu-build.sh
```

### 2. 如果 shared 包构建失败：
```bash
cd packages/shared
rm -rf node_modules dist
pnpm install
pnpm run build
```

### 3. 如果 API 构建失败：
```bash
cd apps/api
rm -rf node_modules dist
pnpm install
pnpm run build
```

## 注意事项

1. **构建顺序**: 必须先构建 shared 包，再构建 API
2. **类型声明**: shared 包的导入路径不能使用 `.js` 扩展名
3. **依赖管理**: 使用 pnpm workspace 确保包之间的正确链接
4. **权限**: 确保 Ubuntu 用户有读写项目目录的权限
5. **环境变量**: 设置必要的 API keys 和环境变量

## 错误修复统计

总共修复了 **2类主要问题**：
1. fastify.log 类型错误: 9个错误
2. shared 包类型声明错误: 4个导入路径修复

更新日期: 2025-08-07